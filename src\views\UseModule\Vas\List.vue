<template>
  <div class="list-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="订单号">
          <el-input v-model="searchForm.orderSn" placeholder="请输入订单号" clearable />
        </el-form-item>
        
        <el-form-item label="店铺名称">
          <el-input v-model="searchForm.mallName" placeholder="请输入店铺名称" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="search-area">
      <!-- 操作按钮 -->
      <ActionBar @refresh="refreshData">
        <template #left>
          <!-- <el-button type="danger" :disabled="multiple" @click="handleDelete">删除</el-button>
          <el-button type="warning" :disabled="single" @click="handleEdit">编辑</el-button> -->
        </template>
      </ActionBar>
      
      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        border
        stripe
        style="width: 100%;"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        
        <el-table-column label="订单号" align="center" prop="orderSn" width="180" />
        <el-table-column label="店铺名称" align="center" prop="mallName" />
        <el-table-column label="服务名称" align="center" prop="serviceName" />
        <el-table-column label="实付价格" align="center" prop="amount">
          <template #default="scope">
            {{ formatAmount(scope.row.amount) }}
          </template>
        </el-table-column>
        
        <el-table-column label="支付状态" align="center" prop="payStatus" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.payStatus === 1 ? 'success' : 'danger'">
              {{ scope.row.payStatus === 1 ? '已支付' : '未支付' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="售后状态" align="center" prop="refundStatus" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.refundStatus === 1 ? 'warning' : 'info'">
              {{ scope.row.refundStatus === 1 ? '已售后' : '未售后' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row.id)">删除</el-button>
          </template> 
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      :title="dialogTitle"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="订单号">{{ currentRow.orderSn }}</el-descriptions-item>
        <el-descriptions-item label="店铺名称">{{ currentRow.mallName }}</el-descriptions-item>
        <el-descriptions-item label="服务名称">{{ currentRow.serviceName }}</el-descriptions-item>
        <el-descriptions-item label="服务规格">{{ currentRow.skuSpec }}</el-descriptions-item>
        <el-descriptions-item label="实付价格">{{ formatAmount(currentRow.amount) }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <el-tag :type="currentRow.payStatus === 1 ? 'success' : 'danger'">
            {{ currentRow.payStatus === 1 ? '已支付' : '未支付' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(currentRow.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ formatTime(currentRow.payTime) }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { vasApi } from '@/api/modules/vas.js'
import ActionBar from '@/components/ActionBar.vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableRef = ref(null)

// 搜索表单
const searchForm = reactive({
  orderSn: '',
  mallName: '',
  payStatus: '',
  refundStatus: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const currentRow = ref({})
const detailVisible = ref(false)
const dialogTitle = ref('服务订单详情')

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

// 初始化
onMounted(() => {
  fetchData()
})

// 获取表格数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      size: pagination.size,
      mallName: searchForm.mallName !== null ? searchForm.mallName : undefined
    }
    
    const res = await vasApi.pageQuery(params)
    if (res.code === 200) {
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = (row) => {
  currentRow.value = row
  dialogTitle.value = `服务订单详情 - ${row.orderSn}`
  detailVisible.value = true
}

// 编辑
const handleEdit = async () => {
  if (ids.value.length !== 1) return
  const row = tableData.value.find(item => item.id === ids.value[0])
  currentRow.value = { ...row }
  // 这里可以打开编辑对话框
  ElMessage.info('编辑功能待实现')
}

// 删除
const handleDelete = async (id) => {
  const deleteIds = id ? [id] : ids.value
  if (deleteIds.length === 0) return
  
  try {
    await ElMessageBox.confirm('确定要删除选中的服务订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const res = await vasApi.delete(deleteIds.join(','))
    if (res.code === 200) {
      ElMessage.success('删除成功')
      fetchData()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchData()
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

// 页码变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}
</script>

<style scoped>
/* 保持原有样式不变 */
.search-area {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: left;
}

.search-area .el-form-item {
  margin-right: 18px;
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>