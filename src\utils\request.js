/**
 * 外部API请求工具
 */

// 检查字符串是否为URL
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// 发送GET请求到外部API
export const fetchExternalApi = async (url) => {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
      },
      // 禁用缓存
      cache: 'no-cache',
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // 先尝试获取文本响应
    const text = await response.text();
    
    // 如果文本是URL，直接返回
    if (isValidUrl(text)) {
      console.log('直接返回URL:', text);
      return { url: text.trim() };
    }
    
    // 尝试解析为JSON
    try {
      const jsonData = JSON.parse(text);
      return jsonData;
    } catch (jsonError) {
      // 如果不是JSON，则作为文本返回
      return { text };
    }
  } catch (error) {
    console.error('外部API请求失败:', error);
    throw error;
  }
};

export default {
  fetchExternalApi,
  isValidUrl
}; 