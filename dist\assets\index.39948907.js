import{d as e,Z as a,a8 as l,_ as t,a9 as n,aa as r,a3 as o,ab as i,ac as d,af as u,ag as s,a5 as c,am as p,as as m,o as v,k as g,m as h,w as b,v as f,at as w,F as I,l as y,t as _,ao as z,a6 as C,a7 as V}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                     */import{i as j}from"./index.1c8cd61b.js";const k=async(e={})=>{const a=new URLSearchParams;e.pageNum&&a.append("pageNum",e.pageNum),e.pageSize&&a.append("pageSize",e.pageSize),e.userId&&a.append("userId",e.userId),e.phonenumber&&a.append("phonenumber",e.phonenumber);const l=`/inviteCodes/erp/getInviteCodes?${a.toString()}`,t=await j.get(l);return console.log("API原始响应:",t),t},S=e=>j.get(`/inviteCodes/inviteRelations/inviter?inviterId=${e}`),U={class:"invitation-container"},x=(e=>(C("data-v-c6a650d2"),e=e(),V(),e))((()=>y("div",{class:"card-header"},[y("span",null,"邀请码管理")],-1))),N={class:"invite-url-container"},D={class:"pagination-container"};var A=e({__name:"index",setup(e){const C=a([]),V=a([]),j=a(!1),A=a(!1),P=a(!1),$=a(null),L=a(0),R=a(10),T=a(1),F=l({userId:"",phonenumber:""}),Z=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")},q=async()=>{j.value=!0;try{const e={pageNum:T.value,pageSize:R.value};F.userId&&(e.userId=F.userId),F.phonenumber&&(e.phonenumber=F.phonenumber);const a=await k(e);a&&a.data&&200===a.code?(C.value=a.data.list||[],L.value=a.data.total||0,R.value=a.data.pageSize||10,T.value=a.data.pageNum||1,console.log("设置到inviteCodes的数据:",C.value)):(console.error("无法解析API返回数据:",a),z.error("获取邀请码列表失败"),C.value=[],L.value=0)}catch(e){console.error("获取邀请码列表出错:",e),z.error("获取邀请码列表出错"),C.value=[],L.value=0}finally{j.value=!1}},B=()=>{T.value=1,q()},E=()=>{F.userId="",F.phonenumber="",T.value=1,q()},G=e=>{R.value=e,T.value=1,q()},H=e=>{T.value=e,q()};return t((()=>{q()})),(e,a)=>{const l=n,t=r,k=o,q=i,J=d,K=u,M=s,O=c,Q=p,W=m;return v(),g("div",U,[h(O,{class:"box-card"},{header:b((()=>[x])),default:b((()=>[h(q,{inline:!0,class:"search-form"},{default:b((()=>[h(t,{label:"用户ID"},{default:b((()=>[h(l,{modelValue:F.userId,"onUpdate:modelValue":a[0]||(a[0]=e=>F.userId=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])])),_:1}),h(t,{label:"手机号码"},{default:b((()=>[h(l,{modelValue:F.phonenumber,"onUpdate:modelValue":a[1]||(a[1]=e=>F.phonenumber=e),placeholder:"请输入手机号码",clearable:""},null,8,["modelValue"])])),_:1}),h(t,null,{default:b((()=>[h(k,{type:"primary",onClick:B},{default:b((()=>[f("查询")])),_:1}),h(k,{onClick:E},{default:b((()=>[f("重置")])),_:1})])),_:1})])),_:1}),w((v(),I(K,{data:C.value,stripe:"",style:{width:"100%"},border:""},{default:b((()=>[h(J,{prop:"id",label:"ID",width:"70",align:"center"}),h(J,{prop:"userId",label:"用户ID","min-width":"180",align:"center"}),h(J,{prop:"phonenumber",label:"手机号码","min-width":"120",align:"center"}),h(J,{prop:"code",label:"邀请码",width:"120",align:"center"}),h(J,{label:"邀请链接","min-width":"300",align:"center"},{default:b((e=>[y("div",N,[h(l,{modelValue:e.row.inviteUrl,"onUpdate:modelValue":a=>e.row.inviteUrl=a,readonly:"",size:"small",class:"invite-url-input"},null,8,["modelValue","onUpdate:modelValue"]),h(k,{type:"primary",size:"small",onClick:a=>{return l=e.row.inviteUrl,void navigator.clipboard.writeText(l).then((()=>{z.success("邀请链接已复制到剪贴板")})).catch((()=>{z.error("复制失败，请手动复制")}));var l},class:"copy-btn"},{default:b((()=>[f(" 复制 ")])),_:2},1032,["onClick"])])])),_:1}),h(J,{label:"创建时间",width:"160",align:"center"},{default:b((e=>[f(_(Z(e.row.createdAt)),1)])),_:1}),h(J,{label:"操作",width:"120",align:"center"},{default:b((e=>[h(k,{type:"primary",size:"small",onClick:a=>(async e=>{console.log("inviterId:",e),$.value=e,P.value=!0,A.value=!0;try{const a=await S(e);a&&a.data&&200===a.code?(V.value=a.data||[],console.log("处理后的被邀请人数据:",V.value),0===V.value.length&&z.info("该用户暂无邀请记录")):(console.warn("获取被邀请人列表返回异常结构:",a),V.value=[])}catch(a){console.error("获取被邀请人列表出错:",a),V.value=[]}finally{A.value=!1}})(e.row.userId)},{default:b((()=>[f(" 查看被邀请人 ")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[W,j.value]]),y("div",D,[h(M,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:L.value,"page-size":R.value,"current-page":T.value,"page-sizes":[10,20,50,100],onSizeChange:G,onCurrentChange:H},null,8,["total","page-size","current-page"])])])),_:1}),h(Q,{modelValue:P.value,"onUpdate:modelValue":a[2]||(a[2]=e=>P.value=e),title:`邀请总数: ${V.value.length}人`,width:"50%"},{default:b((()=>[w((v(),I(K,{data:V.value,stripe:"",style:{width:"100%"},border:""},{default:b((()=>[h(J,{prop:"phonenumber",label:"手机号码","min-width":"150",align:"center"}),h(J,{label:"邀请时间","min-width":"150",align:"center"},{default:b((e=>[f(_(Z(e.row.inviteTime)),1)])),_:1})])),_:1},8,["data"])),[[W,A.value]])])),_:1},8,["modelValue","title"])])}}});A.__scopeId="data-v-c6a650d2";export{A as default};
