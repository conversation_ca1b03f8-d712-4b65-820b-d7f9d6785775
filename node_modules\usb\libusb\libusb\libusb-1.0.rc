/*
 * For Windows: input this file to the Resource Compiler to produce a binary
 * .res file. This is then embedded in the resultant library (like any other
 * compilation object).
 * The information can then be queried using standard APIs and can also be
 * viewed with utilities such as Windows Explorer.
 */
#include "winresrc.h"

#include "version.h"
#ifndef LIBUSB_VERSIONSTRING
#define LU_STR(s) #s
#define LU_XSTR(s) LU_STR(s)
#define LIBUSB_VERSIONSTRING \
	LU_XSTR(LIBUSB_MAJOR) "." LU_XSTR(LIBUSB_MINOR) "." \
	LU_XSTR(LIBUSB_MICRO) "." LU_XSTR(LIBUSB_NANO) LIBUSB_RC "\0"
#endif

VS_VERSION_INFO VERSIONINFO
 FILEVERSION LIBUSB_MAJOR,LIBUSB_MINOR,LIBUSB_MICRO,LIBUSB_NANO
 PRODUCTVERSION LIBUSB_MAJOR,LIBUSB_MINOR,LIB<PERSON><PERSON>_MICRO,LIBUSB_NANO
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
	BLOCK "StringFileInfo"
	BEGIN
		BLOCK "040904b0"
		BEGIN
			VALUE "CompanyName", "libusb.info\0"
			VALUE "FileDescription", "C library for writing portable USB drivers in userspace\0"
			VALUE "FileVersion", LIBUSB_VERSIONSTRING
			VALUE "InternalName", "libusb\0"
			VALUE "LegalCopyright", "See individual source files, GNU LGPL v2.1 or later.\0"
			VALUE "LegalTrademarks", "http://www.gnu.org/licenses/lgpl-2.1.html\0"
			VALUE "OriginalFilename", "libusb-1.0.dll\0"
			VALUE "PrivateBuild", "\0"
			VALUE "ProductName", "libusb-1.0\0"
			VALUE "ProductVersion", LIBUSB_VERSIONSTRING
			VALUE "SpecialBuild", "\0"
		END
	END
	BLOCK "VarFileInfo"
	BEGIN
		VALUE "Translation", 0x409, 1200
	END
END
