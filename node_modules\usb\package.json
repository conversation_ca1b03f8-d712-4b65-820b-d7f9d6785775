{"name": "usb", "description": "Library to access USB devices", "license": "MIT", "version": "2.15.0", "main": "dist/index.js", "engines": {"node": ">=12.22.0 <13.0 || >=14.17.0"}, "repository": {"type": "git", "url": "https://github.com/node-usb/node-usb.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kevinmehall.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://timryan.org"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegecko"}, {"name": "<PERSON>"}], "keywords": ["usb", "hardware"], "scripts": {"install": "node-gyp-build", "rebuild": "node-gyp rebuild", "clean": "git clean -fx ./build ./prebuilds ./dist ./docs ./node_modules", "compile": "tsc", "postcompile": "eslint . --ext .ts", "watch": "tsc -w --preserveWatchOutput", "test": "mocha --require coffeescript/register --grep Module test/*", "full-test": "mocha --timeout 10000 --require coffeescript/register test/*.coffee", "valgrind": "coffee -c test/*.coffee; valgrind --leak-check=full --show-possibly-lost=no node --expose-gc --trace-gc node_modules/mocha/bin/_mocha -R spec", "docs": "typedoc", "prebuild": "prebuildify --napi --strip --name node.napi", "prebuild-cross": "prebuildify-cross --napi --strip --name node.napi", "prepublishOnly": "prebuildify-ci download", "prebuild-download": "prebuildify-ci download"}, "dependencies": {"@types/w3c-web-usb": "^1.0.6", "node-addon-api": "^8.0.0", "node-gyp-build": "^4.5.0"}, "devDependencies": {"@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^5.45.1", "@typescript-eslint/parser": "^5.45.1", "coffeescript": "^2.7.0", "eslint": "^8.29.0", "mocha": "^11.0.1", "node-gyp": "^10.0.1", "prebuildify": "^6.0.1", "prebuildify-ci": "^1.0.5", "prebuildify-cross": "^5.1.1", "typedoc": "^0.27.2", "typescript": "^5.4.5"}, "binary": {"napi_versions": [8]}}