import instance from '../../utils/axios.js'

const vasApi = {
  // 分页查询
  pageQuery: (params) => {
    const convertedParams = {
      ...params,
      pageNum: params.page,
      pageSize: params.size
    }
    delete convertedParams.page
    delete convertedParams.size
    
    return instance.get('/vas/pageQuery', { 
      params: convertedParams
    })
  },
  
  // 新增服务订单
  create: (data) => {
    return instance.post('/vas', data)
  },
  
  // 更新服务订单
  update: (data) => {
    return instance.put('/vas', data)
  },
  
  // 删除服务订单
  delete: (id) => {
    return instance.delete(`/vas/${id}`)
  },
  
  // 根据ID获取详情
  getById: (id) => {
    return instance.get(`/vas/${id}`)
  },
  
  // 根据订单号查询
  getByOrderSn: (orderSn) => {
    return instance.get(`/vas/orderSn/${orderSn}`)
  },
  
  // 根据店铺ID查询
  getByMallId: (mallId) => {
    return instance.get(`/vas/mall/${mallId}`)
  }
}

export { vasApi }