<template>
	<div class="list-container">
		<!-- 搜索区域 -->
		<div class="search-area">
			<el-form :inline="true" :model="searchForm">
				<el-form-item label="入驻标识">
					<el-input v-model="searchForm.settled_cost_key" placeholder="请输入入驻标识" clearable />
				</el-form-item>
				<el-form-item label="标题">
					<el-input v-model="searchForm.title" placeholder="请输入标题" clearable />
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button @click="resetSearch">重置</el-button>
				</el-form-item>
			</el-form>
		</div>

		<!-- 操作按钮 -->
		<ActionBar @refresh="refreshData">
			<template #left>
				<el-button type="primary" @click="handleAdd">新增</el-button>
				<el-button @click="toggleSelection">全选/反选</el-button>
			</template>
		</ActionBar>

		<!-- 数据表格 -->
		<el-table
			ref="tableRef"
			:data="tableData"
			border
			stripe
			style="width: 100%;"
			@selection-change="handleSelectionChange"
			row-key="id"
		>
			<!-- 展开行：显示 constraint_json 的各项数据 -->
			<el-table-column type="expand">
				<template #default="{ row }">
					<div v-if="row.constraint_json">
						<div v-if="parseConstraint(row.constraint_json)" class="expand-container">
							<!-- 基本约束信息 -->
							<div class="expand-block">
								<h4 class="expand-title">基本约束</h4>
							<div class="expand-row-item">
									<span class="expand-label">最小本数:</span>
								<span class="expand-value">{{ parseConstraint(row.constraint_json).books_count_min }}</span>
							</div>
							<div class="expand-row-item">
									<span class="expand-label">最大本数:</span>
								<span class="expand-value">{{ parseConstraint(row.constraint_json).books_count_max }}</span>
								</div>
							</div>
							
							<!-- 小程序收费信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).miniapp">
								<h4 class="expand-title">小程序收费</h4>
								<div class="expand-row-item">
									<span class="expand-label">月收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).miniapp.month_fee || 0 }}元
									</span>
								</div>

								<div class="expand-row-item">
									<span class="expand-label">年收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).miniapp.year_fee || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).miniapp.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">推广员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).miniapp.kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.kickback_type || 0)">
									<span class="expand-label">推广员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.kickback_type || 0)">
									<span class="expand-label">月推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).miniapp.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.kickback_type || 0)">
									<span class="expand-label">年推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).miniapp.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).miniapp.kickback_type || 0)">
									<span class="expand-label">推广员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
								
								<!-- 会员佣金 -->
								<div class="expand-row-item">
									<span class="expand-label">会员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).miniapp.member_kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.member_kickback_type || 0)">
									<span class="expand-label">会员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.member_kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.member_kickback_type || 0)">
									<span class="expand-label">月会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).miniapp.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.member_kickback_type || 0)">
									<span class="expand-label">年会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).miniapp.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).miniapp.member_kickback_type || 0)">
									<span class="expand-label">会员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.member_kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
							</div>
							
							<!-- 账号收费信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).account">
								<h4 class="expand-title">账号收费</h4>
								<div class="expand-row-item">
									<span class="expand-label">月收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).account.month_fee || 0 }}元
									</span>
								</div>

								<div class="expand-row-item">
									<span class="expand-label">年收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).account.year_fee || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).account.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">推广员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).account.kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).account.kickback_type || 0)">
									<span class="expand-label">推广员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).account.kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).account.kickback_type || 0)">
									<span class="expand-label">月推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).account.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).account.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).account.kickback_type || 0)">
									<span class="expand-label">年推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).account.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).account.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).account.kickback_type || 0)">
									<span class="expand-label">推广员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).account.kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
								
								<!-- 会员佣金 -->
								<div class="expand-row-item">
									<span class="expand-label">会员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).account.member_kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).account.member_kickback_type || 0)">
									<span class="expand-label">会员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).account.member_kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).account.member_kickback_type || 0)">
									<span class="expand-label">月会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).account.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).account.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).account.member_kickback_type || 0)">
									<span class="expand-label">年会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).account.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).account.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).account.member_kickback_type || 0)">
									<span class="expand-label">会员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).account.member_kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
							</div>
							
							<!-- 孔夫子店铺收费信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).kongfz">
								<h4 class="expand-title">孔夫子店铺</h4>
								<div class="expand-row-item">
									<span class="expand-label">月收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).kongfz.month_fee || 0 }}元
									</span>
								</div>

								<div class="expand-row-item">
									<span class="expand-label">年收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).kongfz.year_fee || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).kongfz.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).kongfz.kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.kickback_type || 0)">
									<span class="expand-label">提点比例:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.kickback_type || 0)">
									<span class="expand-label">月推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).kongfz.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.kickback_type || 0)">
									<span class="expand-label">年推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).kongfz.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).kongfz.kickback_type || 0)">
									<span class="expand-label">固定费用:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
								
								<!-- 会员佣金 -->
								<div class="expand-row-item">
									<span class="expand-label">会员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).kongfz.member_kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.member_kickback_type || 0)">
									<span class="expand-label">会员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.member_kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.member_kickback_type || 0)">
									<span class="expand-label">月会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).kongfz.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.member_kickback_type || 0)">
									<span class="expand-label">年会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).kongfz.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).kongfz.member_kickback_type || 0)">
									<span class="expand-label">会员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.member_kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
							</div>
							
							<!-- 拼多多专营店收费信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).pdd">
								<h4 class="expand-title">拼多多专营店</h4>
								<div class="expand-row-item">
									<span class="expand-label">月收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).pdd.month_fee || 0 }}元
									</span>
								</div>

								<div class="expand-row-item">
									<span class="expand-label">年收费:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).pdd.year_fee || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).pdd.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).pdd.kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).pdd.kickback_type || 0)">
									<span class="expand-label">提点比例:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).pdd.kickback_type || 0)">
									<span class="expand-label">月推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).pdd.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).pdd.kickback_type || 0)">
									<span class="expand-label">年推广员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).pdd.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).pdd.kickback_type || 0)">
									<span class="expand-label">固定费用:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
								
								<!-- 会员佣金 -->
								<div class="expand-row-item">
									<span class="expand-label">会员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).pdd.member_kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).pdd.member_kickback_type || 0)">
									<span class="expand-label">会员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.member_kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).pdd.member_kickback_type || 0)">
									<span class="expand-label">月会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).pdd.month_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).pdd.member_kickback_type || 0)">
									<span class="expand-label">年会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).pdd.year_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).pdd.member_kickback_type || 0)">
									<span class="expand-label">会员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).pdd.member_kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
							</div>
							
							<!-- 资源费分级收费信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).resource_tier">
								<h4 class="expand-title">资源费分级</h4>
								<div class="expand-row-item">
									<span class="expand-label">3000条以下:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier1_fee || 0 }}元/月
									</span>
								</div>

								<div class="expand-row-item">
									<span class="expand-label">3000-20000:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier2_fee || 50 }}元/月
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">3000-20000成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier2_cost || 0 }}元/月
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">20000-50000:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier3_fee || 100 }}元/月
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">20000-50000成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier3_cost || 0 }}元/月
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">50000-100000:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier4_fee || 200 }}元/月
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">50000-100000成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier4_cost || 0 }}元/月
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">100000条以上:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier5_fee || 300 }}元/月
									</span>
								</div>
								<!-- <div class="expand-row-item">
									<span class="expand-label">100000条以上成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.tier5_cost || 0 }}元/月
									</span>
								</div> -->
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).resource_tier.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0)">
									<span class="expand-label">提点比例:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0)">
									<span class="expand-label">3000条以下佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).resource_tier.tier1_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0)">
									<span class="expand-label">3000-20000佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).resource_tier.tier2_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0)">
									<span class="expand-label">固定费用:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
								
								<!-- 会员佣金 -->
								<div class="expand-row-item">
									<span class="expand-label">会员佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).resource_tier.member_kickback_type || 0) }}
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.member_kickback_type || 0)">
									<span class="expand-label">会员提点:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.member_kickback_value || '{}', 'point') }}%
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.member_kickback_type || 0)">
									<span class="expand-label">3000条以下会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).resource_tier.tier1_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.member_kickback_type || 0)">
									<span class="expand-label">3000-20000会员佣金:</span>
									<span class="expand-value">
										{{ (parseConstraint(row.constraint_json).resource_tier.tier2_fee * parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.member_kickback_value || '{}', 'point') / 100).toFixed(2) }}元
									</span>
								</div>
								<div class="expand-row-item" v-if="[2, 3].includes(parseConstraint(row.constraint_json).resource_tier.member_kickback_type || 0)">
									<span class="expand-label">会员固费:</span>
									<span class="expand-value">
										{{ parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.member_kickback_value || '{}', 'fixed') }}元
									</span>
								</div>
							</div>
							
							<!-- 交易手续费信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).transaction">
								<h4 class="expand-title">交易手续费</h4>
								<div class="expand-row-item">
									<span class="expand-label">同库房同店铺:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).transaction.same_warehouse_shop || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).transaction.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).transaction.kickback_type || 0) }}
									</span>
								</div>
								<!-- 其他交易手续费信息 -->
							</div>
							
							<!-- 同库房不同店铺费用信息 -->
							<div class="expand-block" v-if="parseConstraint(row.constraint_json).different_shop">
								<h4 class="expand-title">同库房不同店铺</h4>
								<div class="expand-row-item">
									<span class="expand-label">扣库房:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).different_shop.warehouse_percent || 0 }}% + 
										{{ parseConstraint(row.constraint_json).different_shop.warehouse_fixed || 0.02 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">扣店主:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).different_shop.owner_percent || 0 }}% + 
										{{ parseConstraint(row.constraint_json).different_shop.owner_fixed || 0.02 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">成本:</span>
									<span class="expand-value">
										{{ parseConstraint(row.constraint_json).different_shop.cost || 0 }}元
									</span>
								</div>
								<div class="expand-row-item">
									<span class="expand-label">佣金类型:</span>
									<span class="expand-value">
										{{ formatKickbackType(parseConstraint(row.constraint_json).different_shop.kickback_type || 0) }}
									</span>
								</div>
								<!-- 其他同库房不同店铺信息 -->
							</div>
						</div>
						<div v-else>
							<span style="color: red;">约束条件格式错误</span>
						</div>
					</div>
					<div v-else>
						<span style="color: #999;">无约束条件</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column type="selection" align="center" width="55" />
			<el-table-column prop="title" align="center" label="标题" width="120" />
			<el-table-column prop="settled_cost_key" align="center" label="入驻标识" width="180" />
			<!-- 小程序收费佣金 -->
			<el-table-column align="center" label="小程序佣金" width="180">
				<template #default="{ row }">
					<div v-if="parseConstraint(row.constraint_json) && parseConstraint(row.constraint_json).miniapp">
						<div>
							类型: {{ formatKickbackType(parseConstraint(row.constraint_json).miniapp.kickback_type || 0) }}
						</div>
						<div v-if="[1, 3].includes(parseConstraint(row.constraint_json).miniapp.kickback_type || 0)">
							提点: {{ parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.kickback_value || '{}', 'point') }}%
						</div>
						<div v-if="[2, 3].includes(parseConstraint(row.constraint_json).miniapp.kickback_type || 0)">
							固费: {{ parseKickbackValueJson(parseConstraint(row.constraint_json).miniapp.kickback_value || '{}', 'fixed') }}元
						</div>
					</div>
					<span v-else>-</span>
				</template>
			</el-table-column>

			<el-table-column label="操作" align="center" fixed="right">
				<template #default="{ row }">
					<el-button-group>
						<el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
						<el-button type="danger" size="small" @click="handleDelete(row)" :disabled="row.is_del === 1">
							删除
						</el-button>
					</el-button-group>
				</template>
			</el-table-column>
			<!-- 孔夫子店铺佣金 -->
			<el-table-column align="center" label="孔夫子佣金" width="180">
				<template #default="{ row }">
					<div v-if="parseConstraint(row.constraint_json) && parseConstraint(row.constraint_json).kongfz">
						<div>
							类型: {{ formatKickbackType(parseConstraint(row.constraint_json).kongfz.kickback_type || 0) }}
						</div>
						<div v-if="[1, 3].includes(parseConstraint(row.constraint_json).kongfz.kickback_type || 0)">
							提点: {{ parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.kickback_value || '{}', 'point') }}%
						</div>
						<div v-if="[2, 3].includes(parseConstraint(row.constraint_json).kongfz.kickback_type || 0)">
							固费: {{ parseKickbackValueJson(parseConstraint(row.constraint_json).kongfz.kickback_value || '{}', 'fixed') }}元
						</div>
					</div>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<!-- 资源费分级佣金 -->
			<el-table-column align="center" label="资源费佣金" width="180">
				<template #default="{ row }">
					<div v-if="parseConstraint(row.constraint_json) && parseConstraint(row.constraint_json).resource_tier">
						<div>
							类型: {{ formatKickbackType(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0) }}
						</div>
						<div v-if="[1, 3].includes(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0)">
							提点: {{ parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.kickback_value || '{}', 'point') }}%
						</div>
						<div v-if="[2, 3].includes(parseConstraint(row.constraint_json).resource_tier.kickback_type || 0)">
							固费: {{ parseKickbackValueJson(parseConstraint(row.constraint_json).resource_tier.kickback_value || '{}', 'fixed') }}元
						</div>
					</div>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column prop="price" align="center" label="价格(元)" width="120" />
			<el-table-column prop="state" align="center" label="状态" width="80">
				<template #default="{ row }">
					<el-tag :type="row.state === 1 ? 'success' : 'danger'">{{ row.state === 1 ? '正常' : '失效' }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="note" align="center" label="备注" width="200" />
			<el-table-column prop="is_del" align="center" label="删除状态" width="100">
				<template #default="{ row }">
					{{ row.is_del === 0 ? '正常' : '已删除' }}
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination">
			<el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
				:total="pagination.total" :page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
				@current-change="handleCurrentChange" />
		</div>

		<!-- 添加模态框组件 -->
		<el-dialog
			v-model="dialogVisible"
			:title="dialogType === 'add' ? '新增配置' : '编辑配置'"
			width="50%"
			:close-on-click-modal="false"
		>
			<el-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				label-width="120px"
				label-position="right"
			>
				<el-form-item label="标题" prop="title">
					<el-input v-model="formData.title" placeholder="请输入标题" />
				</el-form-item>
				<el-form-item label="入驻标识" prop="settled_cost_key">
					<el-input v-model="formData.settled_cost_key" placeholder="请输入入驻标识" :disabled="dialogType === 'edit'" />
				</el-form-item>
				<el-form-item label="会员类型" prop="member_type">
					<el-select v-model="formData.member_type" placeholder="请选择会员类型" clearable>
						<el-option label="小程序" :value="1" />
					</el-select>
				</el-form-item>
				<el-form-item label="最小本数" prop="books_count_min">
					<el-input-number v-model="formData.books_count_min" :min="0" :precision="0" placeholder="请输入最小本数" />
				</el-form-item>
				<el-form-item label="最大本数" prop="books_count_max">
					<el-input-number v-model="formData.books_count_max" :min="0" :precision="0" placeholder="请输入最大本数" />
				</el-form-item>
				<!-- <el-form-item label="佣金类型" prop="kickback_type">
					<el-select v-model="formData.kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				
				<el-form-item 
					v-if="[1, 3].includes(formData.kickback_type)" 
					label="提点比例" 
					prop="kickback_point"
				>
					<el-input-number 
						v-model="formData.kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				
				<el-form-item 
					v-if="[2, 3].includes(formData.kickback_type)" 
					label="固定费用" 
					prop="kickback_fixed"
				>
					<el-input-number 
						v-model="formData.kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<el-form-item label="资源费类型" prop="resource_cost_type">
					<el-select v-model="formData.resource_cost_type" placeholder="请选择资源费类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.resource_cost_type)" 
					label="资源费值" 
					prop="resource_cost_value"
				>
					<el-input-number 
						v-model="formData.resource_cost_value" 
						:min="0" 
						:precision="2" 
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.resource_cost_type)" 
					label="服务费率" 
					prop="service_rate"
				>
					<el-input-number 
						v-model="formData.service_rate" 
						:min="0" 
						:precision="2" 
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item> -->


				<!-- 小程序收费 -->
				<el-divider content-position="left">小程序收费</el-divider>
				<el-form-item label="月收费" prop="miniapp_month_fee">
					<el-input-number v-model="formData.miniapp_month_fee" :min="0" :precision="2" placeholder="请输入月收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="成本" prop="miniapp_cost">
					<el-input-number v-model="formData.miniapp_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="年收费" prop="miniapp_year_fee">
					<el-input-number v-model="formData.miniapp_year_fee" :min="0" :precision="2" placeholder="请输入年收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 推广员佣金 -->
				<el-form-item label="推广员佣金类型" prop="miniapp_kickback_type">
					<el-select v-model="formData.miniapp_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.miniapp_kickback_type)" 
					label="提点比例" 
					prop="miniapp_kickback_point"
				>
					<el-input-number 
						v-model="formData.miniapp_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateMiniappKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.miniapp_kickback_type)" 
					label="固定费用" 
					prop="miniapp_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.miniapp_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateMiniappKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="miniapp_member_kickback_type">
					<el-select v-model="formData.miniapp_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.miniapp_member_kickback_type)" 
					label="提点比例" 
					prop="miniapp_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.miniapp_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateMiniappMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.miniapp_member_kickback_type)" 
					label="固定费用" 
					prop="miniapp_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.miniapp_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateMiniappMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 新增账号收费 -->
				<el-divider content-position="left">小程序新增账号收费</el-divider>
				<el-form-item label="月收费" prop="account_month_fee">
					<el-input-number v-model="formData.account_month_fee" :min="0" :precision="2" placeholder="请输入月收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="成本" prop="account_cost">
					<el-input-number v-model="formData.account_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="年收费" prop="account_year_fee">
					<el-input-number v-model="formData.account_year_fee" :min="0" :precision="2" placeholder="请输入年收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 推广员佣金 -->
				<el-form-item label="推广员佣金类型" prop="account_kickback_type">
					<el-select v-model="formData.account_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.account_kickback_type)" 
					label="提点比例" 
					prop="account_kickback_point"
				>
					<el-input-number 
						v-model="formData.account_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateAccountKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.account_kickback_type)" 
					label="固定费用" 
					prop="account_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.account_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateAccountKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="account_member_kickback_type">
					<el-select v-model="formData.account_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.account_member_kickback_type)" 
					label="提点比例" 
					prop="account_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.account_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateAccountMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.account_member_kickback_type)" 
					label="固定费用" 
					prop="account_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.account_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateAccountMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 孔夫子店铺收费 -->
				<el-divider content-position="left">孔夫子店铺收费</el-divider>
				<el-form-item label="月收费" prop="kongfz_month_fee">
					<el-input-number v-model="formData.kongfz_month_fee" :min="0" :precision="2" placeholder="请输入月收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="成本" prop="kongfz_cost">
					<el-input-number v-model="formData.kongfz_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="年收费" prop="kongfz_year_fee">
					<el-input-number v-model="formData.kongfz_year_fee" :min="0" :precision="2" placeholder="请输入年收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 推广员佣金 -->
				<el-form-item label="推广员佣金类型" prop="kongfz_kickback_type">
					<el-select v-model="formData.kongfz_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.kongfz_kickback_type)" 
					label="提点比例" 
					prop="kongfz_kickback_point"
				>
					<el-input-number 
						v-model="formData.kongfz_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateKongfzKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.kongfz_kickback_type)" 
					label="固定费用" 
					prop="kongfz_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.kongfz_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateKongfzKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="kongfz_member_kickback_type">
					<el-select v-model="formData.kongfz_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.kongfz_member_kickback_type)" 
					label="提点比例" 
					prop="kongfz_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.kongfz_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateKongfzMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.kongfz_member_kickback_type)" 
					label="固定费用" 
					prop="kongfz_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.kongfz_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateKongfzMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 拼多多专营店收费 -->
				<el-divider content-position="left">拼多多专营店收费</el-divider>
				<el-form-item label="月收费" prop="pdd_month_fee">
					<el-input-number v-model="formData.pdd_month_fee" :min="0" :precision="2" placeholder="请输入月收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="成本" prop="pdd_cost">
					<el-input-number v-model="formData.pdd_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="年收费" prop="pdd_year_fee">
					<el-input-number v-model="formData.pdd_year_fee" :min="0" :precision="2" placeholder="请输入年收费" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 推广员佣金 -->
				<el-form-item label="推广员佣金类型" prop="pdd_kickback_type">
					<el-select v-model="formData.pdd_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.pdd_kickback_type)" 
					label="提点比例" 
					prop="pdd_kickback_point"
				>
					<el-input-number 
						v-model="formData.pdd_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updatePddKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.pdd_kickback_type)" 
					label="固定费用" 
					prop="pdd_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.pdd_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updatePddKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="pdd_member_kickback_type">
					<el-select v-model="formData.pdd_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.pdd_member_kickback_type)" 
					label="提点比例" 
					prop="pdd_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.pdd_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updatePddMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.pdd_member_kickback_type)" 
					label="固定费用" 
					prop="pdd_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.pdd_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updatePddMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 资源费分级收费 -->
				<el-divider content-position="left">资源费分级收费</el-divider>
				<el-form-item label="3000条以下" prop="resource_tier1_fee">
					<el-input-number v-model="formData.resource_tier1_fee" :min="0" :precision="2" placeholder="请输入月费用" />
					<span style="margin-left: 8px;">元/月 (默认为0免费)</span>
				</el-form-item>
				<el-form-item label="3000-20000条" prop="resource_tier2_fee">
					<el-input-number v-model="formData.resource_tier2_fee" :min="0" :precision="2" placeholder="请输入月费用" />
					<span style="margin-left: 8px;">元/月 (默认为50)</span>
				</el-form-item>
				<el-form-item label="20000-50000条" prop="resource_tier3_fee">
					<el-input-number v-model="formData.resource_tier3_fee" :min="0" :precision="2" placeholder="请输入月费用" />
					<span style="margin-left: 8px;">元/月 (默认为100)</span>
				</el-form-item>
				<el-form-item label="50000-100000条" prop="resource_tier4_fee">
					<el-input-number v-model="formData.resource_tier4_fee" :min="0" :precision="2" placeholder="请输入月费用" />
					<span style="margin-left: 8px;">元/月 (默认为200)</span>
				</el-form-item>
				<el-form-item label="100000条以上" prop="resource_tier5_fee">
					<el-input-number v-model="formData.resource_tier5_fee" :min="0" :precision="2" placeholder="请输入月费用" />
					<span style="margin-left: 8px;">元/月 (默认为300)</span>
				</el-form-item>
				<el-form-item label="成本" prop="resource_tier_cost">
					<el-input-number v-model="formData.resource_tier_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 推广员佣金 -->
				<el-form-item label="推广员佣金类型" prop="resource_tier_kickback_type">
					<el-select v-model="formData.resource_tier_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.resource_tier_kickback_type)" 
					label="提点比例" 
					prop="resource_tier_kickback_point"
				>
					<el-input-number 
						v-model="formData.resource_tier_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateResourceTierKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.resource_tier_kickback_type)" 
					label="固定费用" 
					prop="resource_tier_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.resource_tier_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateResourceTierKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="resource_tier_member_kickback_type">
					<el-select v-model="formData.resource_tier_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.resource_tier_member_kickback_type)" 
					label="提点比例" 
					prop="resource_tier_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.resource_tier_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateResourceTierMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.resource_tier_member_kickback_type)" 
					label="固定费用" 
					prop="resource_tier_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.resource_tier_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateResourceTierMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 交易手续费 -->
				<el-divider content-position="left">交易手续费</el-divider>
				<el-form-item label="同库房同店铺" prop="transaction_same_warehouse_shop">
					<el-input-number v-model="formData.transaction_same_warehouse_shop" :min="0" :precision="2" placeholder="请输入费用" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="成本" prop="transaction_cost">
					<el-input-number v-model="formData.transaction_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="佣金类型" prop="transaction_same_kickback_type">
					<el-select v-model="formData.transaction_same_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.transaction_same_kickback_type)" 
					label="提点比例" 
					prop="transaction_same_kickback_point"
				>
					<el-input-number 
						v-model="formData.transaction_same_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateTransactionSameKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.transaction_same_kickback_type)" 
					label="固定费用" 
					prop="transaction_same_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.transaction_same_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateTransactionSameKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="transaction_same_member_kickback_type">
					<el-select v-model="formData.transaction_same_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.transaction_same_member_kickback_type)" 
					label="提点比例" 
					prop="transaction_same_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.transaction_same_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateTransactionSameMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.transaction_same_member_kickback_type)" 
					label="固定费用" 
					prop="transaction_same_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.transaction_same_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateTransactionSameMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 同库房不同店铺 -->
				<el-divider content-position="left">同库房不同店铺</el-divider>
				<el-form-item label="扣库房百分比" prop="different_shop_warehouse_percent">
					<el-input-number v-model="formData.different_shop_warehouse_percent" :min="0" :max="100" :precision="2" placeholder="请输入百分比" />
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item label="扣库房固定金额" prop="different_shop_warehouse_fixed">
					<el-input-number v-model="formData.different_shop_warehouse_fixed" :min="0" :precision="2" placeholder="请输入固定金额" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="扣店主百分比" prop="different_shop_owner_percent">
					<el-input-number v-model="formData.different_shop_owner_percent" :min="0" :max="100" :precision="2" placeholder="请输入百分比" />
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item label="扣店主固定金额" prop="different_shop_owner_fixed">
					<el-input-number v-model="formData.different_shop_owner_fixed" :min="0" :precision="2" placeholder="请输入固定金额" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="成本" prop="different_shop_cost">
					<el-input-number v-model="formData.different_shop_cost" :min="0" :precision="2" placeholder="请输入成本" />
					<span style="margin-left: 8px;">元</span>
				</el-form-item>
				<el-form-item label="佣金类型" prop="different_shop_kickback_type">
					<el-select v-model="formData.different_shop_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.different_shop_kickback_type)" 
					label="提点比例" 
					prop="different_shop_kickback_point"
				>
					<el-input-number 
						v-model="formData.different_shop_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateDifferentShopKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.different_shop_kickback_type)" 
					label="固定费用" 
					prop="different_shop_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.different_shop_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateDifferentShopKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 会员佣金 -->
				<el-form-item label="会员佣金类型" prop="different_shop_member_kickback_type">
					<el-select v-model="formData.different_shop_member_kickback_type" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.different_shop_member_kickback_type)" 
					label="提点比例" 
					prop="different_shop_member_kickback_point"
				>
					<el-input-number 
						v-model="formData.different_shop_member_kickback_point" 
						:min="0" 
						:max="100"
						:precision="2" 
						@change="updateDifferentShopMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.different_shop_member_kickback_type)" 
					label="固定费用" 
					prop="different_shop_member_kickback_fixed"
				>
					<el-input-number 
						v-model="formData.different_shop_member_kickback_fixed" 
						:min="0" 
						:precision="2" 
						@change="updateDifferentShopMemberKickbackValue"
					/>
					<span style="margin-left: 8px;">元</span>
				</el-form-item>

				<!-- 基本信息 -->
				<el-divider content-position="left">基本信息</el-divider>
				<el-form-item label="价格(元)" prop="price">
					<el-input-number v-model="formData.price" :min="0" :precision="0" />
				</el-form-item>
				<el-form-item label="状态" prop="state">
					<el-switch
						v-model="formData.state"
						:active-value="1"
						:inactive-value="0"
						active-text="正常"
						inactive-text="失效"
					/>
				</el-form-item>
				<el-form-item label="备注" prop="note">
					<el-input
						v-model="formData.note"
						type="textarea"
						:rows="3"
						placeholder="请输入备注信息"
					/>
				</el-form-item>
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import type { ElTable, FormInstance, FormRules } from 'element-plus'
import { settledCostConfigApi } from '@/api/modules/settledCostConfig'
import type { SettledCostConfigDTO } from '@/api/settledCostConfig'
import { ElMessage } from 'element-plus'
import ActionBar from '../../components/ActionBar.vue'


interface TableItem {
	id: number
	title: string
	settled_cost_key: string
	constraint_json: string
	kickback_type: number
	kickback_value: string
	resource_cost_type: number
	resource_cost_value: number
	service_rate: number
	price: number
	state: number
	note: string
	created_by: number
	created_time: number
	updated_by: number
	updated_time: number
	is_del: number
}

interface SubmitData {
	id?: number
	title: string
	settledCostKey: string
	constraintJson: string
	kickbackType: number
	kickbackValue: string
	resourceCostType: number
	resourceCostValue: number
	serviceRate: number
	price: number
	state: number
	note: string
}

// 表格数据
const tableData = ref<TableItem[]>([])

// 表格中展示同库房不同店铺费用信息的方法
const formatDifferentShopFee = (constraintJson: string) => {
	if (!constraintJson) return '';
	try {
		const data = JSON.parse(constraintJson);
		if (!data.different_shop) return '';
		
		const warehouse = `库房: ${data.different_shop.warehouse_percent || 0}% + ${data.different_shop.warehouse_fixed || 0}元`;
		const owner = `店主: ${data.different_shop.owner_percent || 0}% + ${data.different_shop.owner_fixed || 0}元`;
		
		return `${warehouse}<br>${owner}`;
	} catch (e) {
		console.error('解析同库房不同店铺费用失败:', e);
		return '';
	}
}

// 搜索表单
const searchForm = reactive({
	settled_cost_key: '',
	title: ''
})

// 分页配置
const pagination = reactive({
	current: 1,
	size: 10,
	total: 0
})

// 选中行数据
const multipleSelection = ref<TableItem[]>([])

// 表格ref
const tableRef = ref<InstanceType<typeof ElTable> | null>(null)

// 格式化佣金类型
const formatKickbackType = (type: number) => {
	const types = ['预留', '提点', '固定费用', '提点/固费']
	return types[type] || '未知'
}

// 解析佣金值
const parseKickbackValue = (value: any, field: 'point' | 'fixed') => {
	if (typeof value === 'string') {
		try {
			const data = JSON.parse(value)
			return data[field] || 0
		} catch {
			return 0
		}
	} else if (typeof value === 'object' && value !== null) {
		return value[field] || 0
	}
	return value || 0
}

// 解析 constraint_json 字段
const parseConstraint = (jsonStr: string) => {
	try {
		return JSON.parse(jsonStr)
	} catch {
		return null
	}
}

// 解析佣金值
const parseKickbackValueJson = (jsonStr: string, field: 'point' | 'fixed') => {
	try {
		if (!jsonStr) return 0;
		const data = JSON.parse(jsonStr);
		return data[field] || 0;
	} catch {
		return 0;
	}
}

// 获取数据
const fetchData = async () => {
	try {
		const params = {
			pageNum: pagination.current,
			pageSize: pagination.size,
			settledCostKey: searchForm.settled_cost_key || undefined,
			title: searchForm.title || undefined
		}
		const response = await settledCostConfigApi.searchSettledCostConfig(params)
		if (response.code === 200 && response.data) {
			// 将后端返回的数据转换为表格需要的格式
			tableData.value = response.data.list.map(item => ({
				id: item.id,
				title: item.title,
				settled_cost_key: item.settledCostKey,
				constraint_json: item.constraintJson,
				kickback_type: item.kickbackType,
				kickback_value: item.kickbackValue,
				resource_cost_type: item.resourceCostType,
				resource_cost_value: item.resourceCostValue,
				service_rate: item.serviceRate,
				price: item.price / 100, // 将分转换为元
				state: item.state,
				note: item.note,
				created_by: item.createdBy,
				created_time: item.createdTime,
				updated_by: item.updatedBy,
				updated_time: item.updatedTime,
				is_del: item.isDel
			}))
			// 更新分页总数
			pagination.total = response.data.total || 0
		} else {
			ElMessage.error(response.message || '获取数据失败')
			// 发生错误时清空数据
			tableData.value = []
			pagination.total = 0
		}
	} catch (error) {
		console.error('获取数据失败:', error)
		ElMessage.error('获取数据失败')
		// 发生错误时清空数据
		tableData.value = []
		pagination.total = 0
	}
}

// 搜索
const handleSearch = () => {
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 重置搜索
const resetSearch = () => {
	searchForm.settled_cost_key = ''
	searchForm.title = ''
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
	pagination.size = size
		pagination.current = 1 // 切换每页条数时，重置到第一页

	fetchData()
}

// 页码变化
const handleCurrentChange = (current: number) => {
	pagination.current = current
	fetchData()
}

// 多选处理
const handleSelectionChange = (val: TableItem[]) => {
	multipleSelection.value = val
}

// 全选/反选
const toggleSelection = () => {
	if (!tableRef.value) return
	if (multipleSelection.value.length === tableData.value.length && tableData.value.length > 0) {
		// 已全选，执行反选（全部取消）
		tableData.value.forEach(row => {
			tableRef.value!.toggleRowSelection(row, false)
		})
	} else {
		// 未全选，执行全选
		tableData.value.forEach(row => {
			tableRef.value!.toggleRowSelection(row, true)
		})
	}
}

// 模态框相关数据
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
	id: 0,
	title: '',
	settled_cost_key: '',
	constraint_json: '',
	books_count_min: 0,
	books_count_max: 0,
	member_type: 1, // 添加会员类型，默认为1（小程序）
	kickback_type: 0,
	kickback_value: '',
	kickback_point: 0,
	kickback_fixed: 0,
	resource_cost_type: 0,
	resource_cost_value: 0,
	service_rate: 0,
	price: 0,
	state: 1,
	note: '',
	miniapp_month_fee: 0,
	miniapp_cost: 0,
	miniapp_year_fee: 0,
	miniapp_kickback_type: 0,
	miniapp_kickback_point: 0,
	miniapp_kickback_fixed: 0,
	miniapp_member_kickback_type: 0,
	miniapp_member_kickback_point: 0,
	miniapp_member_kickback_fixed: 0,
	account_month_fee: 0,
	account_cost: 0,
	account_year_fee: 0,
	account_kickback_type: 0,
	account_kickback_point: 0,
	account_kickback_fixed: 0,
	account_member_kickback_type: 0,
	account_member_kickback_point: 0,
	account_member_kickback_fixed: 0,
	kongfz_month_fee: 0,
	kongfz_cost: 0,
	kongfz_year_fee: 0,
	kongfz_kickback_type: 0,
	kongfz_kickback_point: 0,
	kongfz_kickback_fixed: 0,
	kongfz_member_kickback_type: 0,
	kongfz_member_kickback_point: 0,
	kongfz_member_kickback_fixed: 0,
	pdd_month_fee: 0,
	pdd_cost: 0,
	pdd_year_fee: 0,
	pdd_kickback_type: 0,
	pdd_kickback_point: 0,
	pdd_kickback_fixed: 0,
	pdd_member_kickback_type: 0,
	pdd_member_kickback_point: 0,
	pdd_member_kickback_fixed: 0,
	resource_tier1_fee: 0,
	resource_tier2_fee: 50,
	resource_tier3_fee: 100,
	resource_tier4_fee: 200,
	resource_tier5_fee: 300,
	resource_tier_cost: 0,
	resource_tier_kickback_type: 0,
	resource_tier_member_kickback_type: 0,
	transaction_same_warehouse_shop: 0,
	transaction_cost: 0,
	transaction_same_kickback_type: 0,
	transaction_same_kickback_point: 0,
	transaction_same_kickback_fixed: 0,
	transaction_same_member_kickback_type: 0,
	transaction_same_member_kickback_point: 0,
	transaction_same_member_kickback_fixed: 0,
	different_shop_warehouse_percent: 0,
	different_shop_warehouse_fixed: 0.02,
	different_shop_owner_percent: 0,
	different_shop_owner_fixed: 0.02,
	different_shop_cost: 0,
	different_shop_kickback_type: 0,
	different_shop_member_kickback_type: 0,
	resource_tier_kickback_point: 0,
	resource_tier_kickback_fixed: 0,
	resource_tier_member_kickback_point: 0,
	resource_tier_member_kickback_fixed: 0,
	different_shop_kickback_point: 0,
	different_shop_kickback_fixed: 0,
	different_shop_member_kickback_point: 0,
	different_shop_member_kickback_fixed: 0
})

// 表单验证规则
const formRules: FormRules = {
	title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
	settled_cost_key: [{ required: true, message: '请输入入驻标识', trigger: 'blur' }],
	books_count_min: [
		{ required: true, message: '请输入最小本数', trigger: 'blur' },
		{ validator: (rule, value, callback) => {
			if (value >= formData.books_count_max) {
				callback(new Error('最小本数必须小于最大本数'));
			} else {
				callback();
			}
		}, trigger: 'blur' }
	],
	books_count_max: [
		{ required: true, message: '请输入最大本数', trigger: 'blur' },
		{ validator: (rule, value, callback) => {
			if (value <= formData.books_count_min) {
				callback(new Error('最大本数必须大于最小本数'));
			} else {
				callback();
			}
		}, trigger: 'blur' }
	],
	kickback_type: [{ required: true, message: '请选择佣金类型', trigger: 'change' }],
	kickback_point: [
		{ 
			validator: (rule, value, callback) => {
				if ([1, 3].includes(formData.kickback_type) && (value === undefined || value === null)) {
					callback(new Error('请输入提点比例'));
				} else {
					callback();
				}
			}, 
			trigger: 'blur' 
		}
	],
	kickback_fixed: [
		{ 
			validator: (rule, value, callback) => {
				if ([2, 3].includes(formData.kickback_type) && (value === undefined || value === null)) {
					callback(new Error('请输入固定费用'));
				} else {
					callback();
				}
			}, 
			trigger: 'blur' 
		}
	],
	resource_cost_type: [{ required: true, message: '请选择资源费类型', trigger: 'change' }],
	resource_cost_value: [{ required: true, message: '请输入资源费值', trigger: 'blur' }],
	service_rate: [{ required: true, message: '请输入服务费率', trigger: 'blur' }],
	price: [{ required: true, message: '请输入价格', trigger: 'blur' }]
}

// 更新佣金值
const updateKickbackValue = () => {
	// 将对象转换为JSON字符串
	formData.kickback_value = JSON.stringify({
		point: formData.kickback_point,
		fixed: formData.kickback_fixed
	})
}

// 更新小程序佣金值
const updateMiniappKickbackValue = () => {
	// 小程序收费的佣金值将直接保存在 constraintJson 中
}

// 更新小程序会员佣金值
const updateMiniappMemberKickbackValue = () => {
	// 小程序会员佣金值将直接保存在 constraintJson 中
}

// 更新账号佣金值
const updateAccountKickbackValue = () => {
	// 账号收费的佣金值将直接保存在 constraintJson 中
}

// 更新账号会员佣金值
const updateAccountMemberKickbackValue = () => {
	// 账号会员佣金值将直接保存在 constraintJson 中
}

// 更新孔夫子店铺佣金值
const updateKongfzKickbackValue = () => {
	// 孔夫子店铺收费的佣金值将直接保存在 constraintJson 中
}

// 更新拼多多专营店佣金值
const updatePddKickbackValue = () => {
	// 拼多多专营店收费的佣金值将直接保存在 constraintJson 中
}

// 更新拼多多专营店会员佣金值
const updatePddMemberKickbackValue = () => {
	// 拼多多专营店会员佣金值将直接保存在 constraintJson 中
}

// 更新孔夫子店铺会员佣金值
const updateKongfzMemberKickbackValue = () => {
	// 孔夫子店铺会员佣金值将直接保存在 constraintJson 中
}

// 更新资源费分级佣金值
const updateResourceTierKickbackValue = () => {
	// 资源费分级收费的佣金值将直接保存在 constraintJson 中
}

// 更新资源费分级会员佣金值
const updateResourceTierMemberKickbackValue = () => {
	// 资源费分级收费的会员佣金值将直接保存在 constraintJson 中
}

// 更新交易手续费佣金值
const updateTransactionSameKickbackValue = () => {
	// 同库房同店铺交易手续费的佣金值将直接保存在 constraintJson 中
}

// 更新交易手续费会员佣金值
const updateTransactionSameMemberKickbackValue = () => {
	// 同库房同店铺交易手续费的会员佣金值将直接保存在 constraintJson 中
}

// 更新同库房不同店铺佣金值
const updateDifferentShopKickbackValue = () => {
	// 同库房不同店铺收费的佣金值将直接保存在 constraintJson 中
}

// 更新同库房不同店铺会员佣金值
const updateDifferentShopMemberKickbackValue = () => {
	// 同库房不同店铺收费的会员佣金值将直接保存在 constraintJson 中
}

// 监听佣金类型变化
watch(() => formData.kickback_type, () => {
	updateKickbackValue()
})

// 重置表单数据
const resetFormData = () => {
	Object.assign(formData, {
		id: 0,
		title: '',
		settled_cost_key: '',
		constraint_json: '',
		books_count_min: 0,
		books_count_max: 0,
		member_type: 1, // 添加会员类型，默认为1（小程序）
		kickback_type: 0,
		kickback_value: '',
		kickback_point: 0,
		kickback_fixed: 0,
		resource_cost_type: 0,
		resource_cost_value: 0,
		service_rate: 0,
		price: 0,
		state: 1,
		note: '',
		miniapp_month_fee: 0,
		miniapp_cost: 0,
		miniapp_year_fee: 0,
		miniapp_kickback_type: 0,
		miniapp_kickback_point: 0,
		miniapp_kickback_fixed: 0,
		miniapp_member_kickback_type: 0,
		miniapp_member_kickback_point: 0,
		miniapp_member_kickback_fixed: 0,
		account_month_fee: 0,
		account_cost: 0,
		account_year_fee: 0,
		account_kickback_type: 0,
		account_kickback_point: 0,
		account_kickback_fixed: 0,
		account_member_kickback_type: 0,
		account_member_kickback_point: 0,
		account_member_kickback_fixed: 0,
		kongfz_month_fee: 0,
		kongfz_cost: 0,
		kongfz_year_fee: 0,
		kongfz_kickback_type: 0,
		kongfz_kickback_point: 0,
		kongfz_kickback_fixed: 0,
		kongfz_member_kickback_type: 0,
		kongfz_member_kickback_point: 0,
		kongfz_member_kickback_fixed: 0,
		pdd_month_fee: 0,
		pdd_cost: 0,
		pdd_year_fee: 0,
		pdd_kickback_type: 0,
		pdd_kickback_point: 0,
		pdd_kickback_fixed: 0,
		pdd_member_kickback_type: 0,
		pdd_member_kickback_point: 0,
		pdd_member_kickback_fixed: 0,
		resource_tier1_fee: 0,
		resource_tier2_fee: 50,
		resource_tier3_fee: 100,
		resource_tier4_fee: 200,
		resource_tier5_fee: 300,
		resource_tier_cost: 0,
		resource_tier_kickback_type: 0,
		resource_tier_member_kickback_type: 0,
		transaction_same_warehouse_shop: 0,
		transaction_cost: 0,
		transaction_same_kickback_type: 0,
		transaction_same_kickback_point: 0,
		transaction_same_kickback_fixed: 0,
		transaction_same_member_kickback_type: 0,
		transaction_same_member_kickback_point: 0,
		transaction_same_member_kickback_fixed: 0,
		different_shop_warehouse_percent: 0,
		different_shop_warehouse_fixed: 0.02,
		different_shop_owner_percent: 0,
		different_shop_owner_fixed: 0.02,
		different_shop_cost: 0,
		different_shop_kickback_type: 0,
		different_shop_member_kickback_type: 0,
		resource_tier_kickback_point: 0,
		resource_tier_kickback_fixed: 0,
		resource_tier_member_kickback_point: 0,
		resource_tier_member_kickback_fixed: 0,
		different_shop_kickback_point: 0,
		different_shop_kickback_fixed: 0,
		different_shop_member_kickback_point: 0,
		different_shop_member_kickback_fixed: 0
	})
}

// 新增
const handleAdd = () => {
	dialogType.value = 'add'
	resetFormData()
	dialogVisible.value = true
}

// 编辑
const handleEdit = (row: TableItem) => {
	dialogType.value = 'edit'
	resetFormData()
	
	// 解析约束条件
	let min = 0, max = 0
	const constraintData = typeof row.constraint_json === 'string' 
		? JSON.parse(row.constraint_json)
		: row.constraint_json
		
	if (constraintData) {
		min = constraintData.books_count_min ?? 0
		max = constraintData.books_count_max ?? 0
		
		// 小程序收费
		if (constraintData.miniapp) {
			formData.miniapp_month_fee = constraintData.miniapp.month_fee ?? 0
			formData.miniapp_cost = constraintData.miniapp.cost ?? 0
			formData.miniapp_year_fee = constraintData.miniapp.year_fee ?? 0
			formData.miniapp_kickback_type = constraintData.miniapp.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.miniapp.kickback_value || '{}')
				formData.miniapp_kickback_point = kickbackValue.point ?? 0
				formData.miniapp_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析小程序佣金值失败:', e)
			}

			// 解析会员佣金值
			formData.miniapp_member_kickback_type = constraintData.miniapp.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.miniapp.member_kickback_value || '{}')
				formData.miniapp_member_kickback_point = memberKickbackValue.point ?? 0
				formData.miniapp_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析小程序会员佣金值失败:', e)
			}
		}
		
		// 账号收费
		if (constraintData.account) {
			formData.account_month_fee = constraintData.account.month_fee ?? 0
			formData.account_cost = constraintData.account.cost ?? 0
			formData.account_year_fee = constraintData.account.year_fee ?? 0
			formData.account_kickback_type = constraintData.account.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.account.kickback_value || '{}')
				formData.account_kickback_point = kickbackValue.point ?? 0
				formData.account_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析账号佣金值失败:', e)
			}
			
			// 解析会员佣金值
			formData.account_member_kickback_type = constraintData.account.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.account.member_kickback_value || '{}')
				formData.account_member_kickback_point = memberKickbackValue.point ?? 0
				formData.account_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析账号会员佣金值失败:', e)
			}
		}
		
		// 孔夫子店铺收费
		if (constraintData.kongfz) {
			formData.kongfz_month_fee = constraintData.kongfz.month_fee ?? 0
			formData.kongfz_cost = constraintData.kongfz.cost ?? 0
			formData.kongfz_year_fee = constraintData.kongfz.year_fee ?? 0
			formData.kongfz_kickback_type = constraintData.kongfz.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.kongfz.kickback_value || '{}')
				formData.kongfz_kickback_point = kickbackValue.point ?? 0
				formData.kongfz_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析孔夫子店铺佣金值失败:', e)
			}
			
			// 解析会员佣金值
			formData.kongfz_member_kickback_type = constraintData.kongfz.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.kongfz.member_kickback_value || '{}')
				formData.kongfz_member_kickback_point = memberKickbackValue.point ?? 0
				formData.kongfz_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析孔夫子店铺会员佣金值失败:', e)
			}
		}
		
		// 拼多多专营店收费
		if (constraintData.pdd) {
			formData.pdd_month_fee = constraintData.pdd.month_fee ?? 0
			formData.pdd_cost = constraintData.pdd.cost ?? 0
			formData.pdd_year_fee = constraintData.pdd.year_fee ?? 0
			formData.pdd_kickback_type = constraintData.pdd.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.pdd.kickback_value || '{}')
				formData.pdd_kickback_point = kickbackValue.point ?? 0
				formData.pdd_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析拼多多专营店佣金值失败:', e)
			}
			
			// 解析会员佣金值
			formData.pdd_member_kickback_type = constraintData.pdd.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.pdd.member_kickback_value || '{}')
				formData.pdd_member_kickback_point = memberKickbackValue.point ?? 0
				formData.pdd_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析拼多多专营店会员佣金值失败:', e)
			}
		}
		
		// 资源费分级收费
		if (constraintData.resource_tier) {
			formData.resource_tier1_fee = constraintData.resource_tier.tier1_fee ?? 0
			formData.resource_tier2_fee = constraintData.resource_tier.tier2_fee ?? 50
			formData.resource_tier3_fee = constraintData.resource_tier.tier3_fee ?? 100
			formData.resource_tier4_fee = constraintData.resource_tier.tier4_fee ?? 200
			formData.resource_tier5_fee = constraintData.resource_tier.tier5_fee ?? 300
			formData.resource_tier_cost = constraintData.resource_tier.cost ?? 0
			formData.resource_tier_kickback_type = constraintData.resource_tier.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.resource_tier.kickback_value || '{}')
				formData.resource_tier_kickback_point = kickbackValue.point ?? 0
				formData.resource_tier_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析资源费分级佣金值失败:', e)
			}
			
			// 解析会员佣金值
			formData.resource_tier_member_kickback_type = constraintData.resource_tier.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.resource_tier.member_kickback_value || '{}')
				formData.resource_tier_member_kickback_point = memberKickbackValue.point ?? 0
				formData.resource_tier_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析资源费分级会员佣金值失败:', e)
			}
		}
		
		// 交易手续费
		if (constraintData.transaction) {
			formData.transaction_same_warehouse_shop = constraintData.transaction.same_warehouse_shop ?? 0
			formData.transaction_cost = constraintData.transaction.cost ?? 0
			formData.transaction_same_kickback_type = constraintData.transaction.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.transaction.kickback_value || '{}')
				formData.transaction_same_kickback_point = kickbackValue.point ?? 0
				formData.transaction_same_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析交易手续费佣金值失败:', e)
			}
			
			// 解析会员佣金值
			formData.transaction_same_member_kickback_type = constraintData.transaction.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.transaction.member_kickback_value || '{}')
				formData.transaction_same_member_kickback_point = memberKickbackValue.point ?? 0
				formData.transaction_same_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析交易手续费会员佣金值失败:', e)
			}
		}
		
		// 同库房不同店铺
		if (constraintData.different_shop) {
			formData.different_shop_warehouse_percent = constraintData.different_shop.warehouse_percent ?? 0
			formData.different_shop_warehouse_fixed = constraintData.different_shop.warehouse_fixed ?? 0.02
			formData.different_shop_owner_percent = constraintData.different_shop.owner_percent ?? 0
			formData.different_shop_owner_fixed = constraintData.different_shop.owner_fixed ?? 0.02
			formData.different_shop_cost = constraintData.different_shop.cost ?? 0
			formData.different_shop_kickback_type = constraintData.different_shop.kickback_type ?? 0
			
			// 解析推广员佣金值
			try {
				const kickbackValue = JSON.parse(constraintData.different_shop.kickback_value || '{}')
				formData.different_shop_kickback_point = kickbackValue.point ?? 0
				formData.different_shop_kickback_fixed = kickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析同库房不同店铺佣金值失败:', e)
			}
			
			// 解析会员佣金值
			formData.different_shop_member_kickback_type = constraintData.different_shop.member_kickback_type ?? 0
			try {
				const memberKickbackValue = JSON.parse(constraintData.different_shop.member_kickback_value || '{}')
				formData.different_shop_member_kickback_point = memberKickbackValue.point ?? 0
				formData.different_shop_member_kickback_fixed = memberKickbackValue.fixed ?? 0
			} catch (e) {
				console.error('解析同库房不同店铺会员佣金值失败:', e)
			}
		}
	}
	
	// 解析佣金值
	let kickbackPoint = 0
	let kickbackFixed = 0
	const kickbackData = typeof row.kickback_value === 'string'
		? JSON.parse(row.kickback_value)
		: row.kickback_value
	
	if (kickbackData) {
		kickbackPoint = kickbackData.point ?? 0
		kickbackFixed = kickbackData.fixed ?? 0
	} else {
		kickbackPoint = row.kickback_type === 1 ? Number(row.kickback_value) : 0
		kickbackFixed = row.kickback_type === 2 ? Number(row.kickback_value) : 0
	}
	
	Object.assign(formData, {
		id: row.id,
		title: row.title,
		settled_cost_key: row.settled_cost_key,
		member_type: constraintData.member_type || 1, // 设置会员类型，默认为1（小程序）
		constraint_json: constraintData,
		books_count_min: min,
		books_count_max: max,
		kickback_type: row.kickback_type,
		kickback_value: typeof kickbackData === 'string' ? kickbackData : JSON.stringify(kickbackData),
		kickback_point: kickbackPoint,
		kickback_fixed: kickbackFixed,
		resource_cost_type: row.resource_cost_type,
		resource_cost_value: row.resource_cost_value,
		service_rate: row.service_rate,
		price: row.price,
		state: row.state,
		note: row.note
	})
	
	dialogVisible.value = true
}

// 提交表单
const submitLoading = ref(false)
const handleSubmit = async () => {
	if (!formRef.value) return
	submitLoading.value = true
	await formRef.value.validate(async (valid) => {
		if (valid) {
			try {
				// 确保佣金值已更新
				updateKickbackValue()
				
				// 构建约束对象
				const constraintObj = {
					books_count_min: formData.books_count_min,
					books_count_max: formData.books_count_max,
					member_type: formData.member_type, // 添加会员类型
					
					// 小程序收费
					miniapp: {
						month_fee: formData.miniapp_month_fee,
						cost: formData.miniapp_cost,
						year_fee: formData.miniapp_year_fee,
						kickback_type: formData.miniapp_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.miniapp_kickback_point,
							fixed: formData.miniapp_kickback_fixed
						}),
						member_kickback_type: formData.miniapp_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.miniapp_member_kickback_point,
							fixed: formData.miniapp_member_kickback_fixed
						})
					},
					
					// 账号收费
					account: {
						month_fee: formData.account_month_fee,
						cost: formData.account_cost,
						year_fee: formData.account_year_fee,
						kickback_type: formData.account_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.account_kickback_point,
							fixed: formData.account_kickback_fixed
						}),
						member_kickback_type: formData.account_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.account_member_kickback_point,
							fixed: formData.account_member_kickback_fixed
						})
					},
					
					// 孔夫子店铺收费
					kongfz: {
						month_fee: formData.kongfz_month_fee,
						cost: formData.kongfz_cost,
						year_fee: formData.kongfz_year_fee,
						kickback_type: formData.kongfz_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.kongfz_kickback_point,
							fixed: formData.kongfz_kickback_fixed
						}),
						member_kickback_type: formData.kongfz_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.kongfz_member_kickback_point,
							fixed: formData.kongfz_member_kickback_fixed
						})
					},
					
					// 拼多多专营店收费
					pdd: {
						month_fee: formData.pdd_month_fee,
						cost: formData.pdd_cost,
						year_fee: formData.pdd_year_fee,
						kickback_type: formData.pdd_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.pdd_kickback_point,
							fixed: formData.pdd_kickback_fixed
						}),
						member_kickback_type: formData.pdd_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.pdd_member_kickback_point,
							fixed: formData.pdd_member_kickback_fixed
						})
					},
					
					// 资源费分级收费
					resource_tier: {
						tier1_fee: formData.resource_tier1_fee,
						tier2_fee: formData.resource_tier2_fee,
						tier3_fee: formData.resource_tier3_fee,
						tier4_fee: formData.resource_tier4_fee,
						tier5_fee: formData.resource_tier5_fee,
						cost: formData.resource_tier_cost,
						kickback_type: formData.resource_tier_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.resource_tier_kickback_point,
							fixed: formData.resource_tier_kickback_fixed
						}),
						member_kickback_type: formData.resource_tier_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.resource_tier_member_kickback_point,
							fixed: formData.resource_tier_member_kickback_fixed
						})
					},
					
					// 交易手续费
					transaction: {
						same_warehouse_shop: formData.transaction_same_warehouse_shop,
						cost: formData.transaction_cost,
						kickback_type: formData.transaction_same_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.transaction_same_kickback_point,
							fixed: formData.transaction_same_kickback_fixed
						}),
						member_kickback_type: formData.transaction_same_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.transaction_same_member_kickback_point,
							fixed: formData.transaction_same_member_kickback_fixed
						})
					},
					
					// 同库房不同店铺
					different_shop: {
						warehouse_percent: formData.different_shop_warehouse_percent,
						warehouse_fixed: formData.different_shop_warehouse_fixed,
						owner_percent: formData.different_shop_owner_percent,
						owner_fixed: formData.different_shop_owner_fixed,
						cost: formData.different_shop_cost,
						kickback_type: formData.different_shop_kickback_type,
						kickback_value: JSON.stringify({
							point: formData.different_shop_kickback_point,
							fixed: formData.different_shop_kickback_fixed
						}),
						member_kickback_type: formData.different_shop_member_kickback_type,
						member_kickback_value: JSON.stringify({
							point: formData.different_shop_member_kickback_point,
							fixed: formData.different_shop_member_kickback_fixed
						})
					}
				}
				
				// 构建提交数据
				const submitData = {
					title: formData.title,
					settledCostKey: formData.settled_cost_key,
					constraintJson: JSON.stringify(constraintObj),
					kickbackType: formData.kickback_type,
					kickbackValue: formData.kickback_value,
					resourceCostType: formData.resource_cost_type,
					resourceCostValue: formData.resource_cost_value,
					serviceRate: formData.service_rate,
					price: formData.price,
					state: formData.state,
					note: formData.note || ''
				}
				
				if (dialogType.value === 'edit') {
					// 使用类型断言来添加id属性
					(submitData as any).id = formData.id
				}
				
				console.log('提交数据:', JSON.stringify(submitData, null, 2))
				
				if (dialogType.value === 'add') {
					// 新增
					const response = await settledCostConfigApi.addSettledCostConfig(submitData)
					console.log('新增接口响应:', response)
					ElMessage.success('新增成功')
				} else {
					// 修改
					const response = await settledCostConfigApi.updateSettledCostConfig(submitData)
					console.log('更新接口响应:', response)
					ElMessage.success('修改成功')
				}
				dialogVisible.value = false
				await fetchData()
			} catch (error) {
				console.error('提交失败:', error)
				
				// 详细打印错误信息
				if (error.response) {
					console.error('错误状态码:', error.response.status)
					console.error('错误响应头:', error.response.headers)
					console.error('错误响应数据:', error.response.data)
				}
				
				let msg = '提交失败';
				// 尝试获取更详细的错误信息
				if (error.response && error.response.data) {
					const responseData = error.response.data;
					console.log('完整错误响应:', responseData)
					if (responseData.message) {
						msg = responseData.message;
					} else if (responseData.error) {
						msg = responseData.error;
					}
				} else if (error.message) {
					msg = error.message;
				}
				ElMessage.error(msg)
			} finally {
				submitLoading.value = false
			}
		} else {
			submitLoading.value = false
		}
	})
}

// 删除
const handleDelete = async (row: SettledCostConfigDTO) => {
	try {
		await settledCostConfigApi.deleteSettledCostConfig(row.id)
		ElMessage.success('删除成功')
		await fetchData() // 重新加载数据
	} catch (error) {
		console.error('删除失败:', error)
		ElMessage.error('删除失败')
	}
}

// 格式化资源费类型
const formatResourceCostType = (type: number) => {
	const types = ['预留', '提点', '固定费用', '提点/固费']
	return types[type] || '未知'
}

// 刷新数据
const refreshData = () => {
	fetchData()
}

// 初始化加载数据
onMounted(() => {
	fetchData()
})
</script>

<style scoped>
.list-container {
	background: #fff;
}

.search-area {
	margin-bottom: 10px;
	padding: 10px;
	background: #f5f7fa;
	border-radius: 4px;
	text-align: left;
}

.action-bar {
	margin-bottom: 10px;
	text-align: left;
}

.pagination {
	margin-top: 10px;
	display: flex;
	justify-content: flex-end;
}

.el-table {
	margin-top: 10px;
}

.el-button-group {
	display: flex;
	gap: 5px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

:deep(.el-dialog__body) {
	padding-top: 20px;
}

.expand-container {
	display: flex;
	flex-wrap: wrap;
	margin: 10px 0;
	background-color: #f9f9f9;
	border-radius: 5px;
	padding: 10px;
}

.expand-block {
	width: 250px;
	margin-right: 20px;
	margin-bottom: 15px;
	border: 1px solid #eaeaea;
	border-radius: 4px;
	padding: 10px;
	background-color: #fff;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.expand-title {
	font-size: 14px;
	color: #409EFF;
	margin-bottom: 10px;
	font-weight: 500;
	border-bottom: 1px dashed #eaeaea;
	padding-bottom: 5px;
}

.expand-row-item {
	margin-bottom: 6px;

	font-size: 13px;
	line-height: 1.5;
}
.expand-label {
	color: #606266;
	font-weight: normal;
	margin-right: 6px;
}
.expand-value {
	color: #303133;
	font-weight: 500;
}
</style>          