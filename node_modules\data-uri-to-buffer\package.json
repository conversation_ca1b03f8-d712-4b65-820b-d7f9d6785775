{"name": "data-uri-to-buffer", "version": "0.0.3", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}}