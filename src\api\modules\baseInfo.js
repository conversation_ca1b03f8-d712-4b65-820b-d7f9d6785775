import request from '@/utils/request'

/**
 * 分页查询书籍基础信息列表
 * @param {Object} query 查询参数
 * @returns {Promise} 请求Promise对象
 */
export function getBaseInfoList(query) {
  return request({
    url: '/baseInfo/list',
    method: 'get',
    params: {
      pageNum: query.pageNum || 1,
      pageSize: query.pageSize || 10,
      bookName: query.bookName,
      isbn: query.isbn,
      author: query.author,
      publisher: query.publisher
    }
  })
} 