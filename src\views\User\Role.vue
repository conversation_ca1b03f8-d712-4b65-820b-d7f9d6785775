<template>
  <div class="role-manage">
    <div class="role-list">
      <el-card>
        <template #header>
          <div class="header">
            <span>角色列表</span>
            <el-button type="primary" @click="handleAddRole">新增角色</el-button>
          </div>
        </template>
        <el-table :data="roleList" style="width: 100%" v-loading="loading">
          <el-table-column prop="roleName" label="角色名称" />
          <el-table-column prop="roleCode" label="角色编码" />
          <el-table-column prop="description" label="角色描述" />
          <el-table-column label="状态">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280">
            <template #default="scope">
              <el-button @click="handleEditRole(scope.row)" type="primary" size="small">编辑</el-button>
              <el-button @click="handleSetPermission(scope.row)" type="warning" size="small">权限设置</el-button>
              <el-popconfirm title="确认删除?" @confirm="handleDeleteRole(scope.row.id)">
                <template #reference>
                  <el-button type="danger" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 角色表单对话框 -->
    <el-dialog v-model="roleDialogVisible" :title="isEdit ? '编辑角色' : '新增角色'">
      <el-form :model="roleForm" label-width="80px" :rules="roleRules" ref="roleFormRef">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="roleForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="roleForm.roleCode" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="roleForm.description" placeholder="请输入角色描述" type="textarea" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="roleForm.status" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRoleForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限设置对话框 -->
    <el-dialog v-model="permissionDialogVisible" title="权限设置">
      <el-tree
        ref="permissionTree"
        :data="permissionTreeData"
        show-checkbox
        node-key="id"
        :props="{ label: 'name' }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRolePermissions">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getRoleList, addRole, updateRole, deleteRole, getRolePermissions, updateRolePermissions } from '@/api/role'
import { getPermissionTree } from '@/api/permission'

export default {
  name: 'RoleManage',
  setup() {
    // 角色列表数据
    const loading = ref(false)
    const roleList = ref([])
    
    // 角色表单数据
    const roleFormRef = ref(null)
    const roleDialogVisible = ref(false)
    const isEdit = ref(false)
    const roleForm = reactive({
      id: '',
      roleName: '',
      roleCode: '',
      description: '',
      status: 1
    })
    const roleRules = {
      roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
      roleCode: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
    }
    
    // 权限设置数据
    const permissionTree = ref(null)
    const permissionTreeData = ref([])
    const permissionDialogVisible = ref(false)
    const currentRoleId = ref('')
    
    // 获取角色列表
    const fetchRoleList = async () => {
      loading.value = true
      try {
        const res = await getRoleList()
        roleList.value = res.data || []
      } catch (error) {
        console.log(error)
        if(error.status === 403){
          ElMessage.error("您没有权限执行此操作")
        }else{
          console.error('获取角色列表失败', error)
          ElMessage.error('获取角色列表失败')
        }
      } finally {
        loading.value = false
      }
    }
    
    // 获取权限树
    const fetchPermissionTree = async () => {
      try {
        const res = await getPermissionTree()
        permissionTreeData.value = res.data || []
      } catch (error) {
        console.error('获取权限树失败', error)
        ElMessage.error('获取权限树失败')
      }
    }
    
    // 获取角色权限
    const fetchRolePermissions = async (roleId) => {
      try {
        const res = await getRolePermissions(roleId)
        if (permissionTree.value) {
          permissionTree.value.setCheckedKeys(res.data || [])
        }
      } catch (error) {
        console.error('获取角色权限失败', error)
        ElMessage.error('获取角色权限失败')
      }
    }
    
    // 新增角色
    const handleAddRole = () => {
      isEdit.value = false
      roleForm.id = ''
      roleForm.roleName = ''
      roleForm.roleCode = ''
      roleForm.description = ''
      roleForm.status = 1
      roleDialogVisible.value = true
    }
    
    // 编辑角色
    const handleEditRole = (row) => {
      isEdit.value = true
      roleForm.id = row.id
      roleForm.roleName = row.roleName
      roleForm.roleCode = row.roleCode
      roleForm.description = row.description
      roleForm.status = row.status
      roleDialogVisible.value = true
    }
    
    // 提交角色表单
    const submitRoleForm = async () => {
      if (!roleFormRef.value) return
      
      await roleFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            if (isEdit.value) {
              await updateRole(roleForm)
              ElMessage.success('更新成功')
            } else {
              await addRole(roleForm)
              ElMessage.success('添加成功')
            }
            roleDialogVisible.value = false
            fetchRoleList()
          } catch (error) {
            console.error(isEdit.value ? '更新角色失败' : '添加角色失败', error)
            ElMessage.error(isEdit.value ? '更新角色失败' : '添加角色失败')
          }
        }
      })
    }
    
    // 删除角色
    const handleDeleteRole = async (id) => {
      try {
        await deleteRole(id)
        ElMessage.success('删除成功')
        fetchRoleList()
      } catch (error) {
        console.error('删除角色失败', error)
        ElMessage.error('删除角色失败')
      }
    }
    
    // 设置角色权限
    const handleSetPermission = async (row) => {
      currentRoleId.value = row.id
      await fetchPermissionTree()
      permissionDialogVisible.value = true
      await fetchRolePermissions(row.id)
    }
    
    // 保存角色权限
    const saveRolePermissions = async () => {
      if (!permissionTree.value || !currentRoleId.value) return
      
      try {
        const checkedKeys = permissionTree.value.getCheckedKeys()
        const halfCheckedKeys = permissionTree.value.getHalfCheckedKeys()
        const allKeys = [...checkedKeys, ...halfCheckedKeys]
        
        await updateRolePermissions(currentRoleId.value, allKeys)
        ElMessage.success('权限设置成功')
        permissionDialogVisible.value = false
      } catch (error) {
        console.error('权限设置失败', error)
        ElMessage.error('权限设置失败')
      }
    }
    
    onMounted(() => {
      fetchRoleList()
    })
    
    return {
      loading,
      roleList,
      roleFormRef,
      roleDialogVisible,
      isEdit,
      roleForm,
      roleRules,
      permissionTree,
      permissionTreeData,
      permissionDialogVisible,
      handleAddRole,
      handleEditRole,
      submitRoleForm,
      handleDeleteRole,
      handleSetPermission,
      saveRolePermissions
    }
  }
}
</script>

<style scoped>
.role-manage {
  padding: 20px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dialog-footer {
  margin-top: 20px;
}
</style> 