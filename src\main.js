import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { permission, permissionAll } from '@/directives/permission'
import { initUserPermissions } from '@/utils/permission'

const app = createApp(App)

// 注册权限指令
app.directive('permission', permission)
app.directive('permission-all', permissionAll)

app.use(store)
app.use(router)
app.use(ElementPlus)

// 初始化用户权限
initUserPermissions().then(() => {
  app.mount('#app')
})
