import instance from '../../utils/axios.js'

const violationApi = {
  // 获取查询参数与分页参数
  pageQuery: (params) => {
    // 转换分页参数名称
    const convertedParams = {
      ...params,
      pageNum: params.page,
      pageSize: params.size
    }
    // 删除旧的参数
    delete convertedParams.page
    delete convertedParams.size
    
    return instance.get('/violation/pageQuery', { 
      params: convertedParams
    })
  },

  submit: (ids,remark) => {
    return instance.post('/violation/errorSubmit?ids='+ids+"&remark="+remark);
  },

  successSubmit:(ids) => {
    return instance.post('/violation/successSubmit?ids='+ids);
  },

  removeSubmit:(ids) => {
    return instance.post('/violation/removeSubmit?ids='+ids);
  },

  editSubmit:(ids,sort) => {
    return instance.post('/violation/editSubmit?id='+ids+"&sort="+sort);
  }

}

export { violationApi } 