var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t;import{Z as u,a8 as i,_ as n,ao as d,ah as p,ai as c,aa as v,a9 as m,a3 as f,ab as b,ac as y,af as g,ag as w,am as _,aG as h,aN as V,as as j,o as k,k as z,l as C,m as x,w as O,v as U,at as S,F as P,H as N,G as R,t as A,an as B}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                    *//* empty css                   *//* empty css                      *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                     */import{i as E}from"./index.1c8cd61b.js";import"./RefreshButton.c2d64dc7.js";import{A as $}from"./ActionBar.acef8039.js";const D=e=>{const u=(i=((e,a)=>{for(var l in a||(a={}))r.call(a,l)&&s(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&s(e,l,a[l]);return e})({},e),n={pageNum:e.page,pageSize:e.size},a(i,l(n)));var i,n;return delete u.page,delete u.size,E.get("/violation/pageQuery",{params:u})},G=(e,a)=>E.post("/violation/errorSubmit?ids="+e+"&remark="+a),I=e=>E.post("/violation/successSubmit?ids="+e),T=e=>E.post("/violation/removeSubmit?ids="+e),q=(e,a)=>E.post("/violation/editSubmit?id="+e+"&sort="+a);const F={class:"list-container"},H={class:"search-area"},L={class:"search-area"},Q={class:"pagination-container"},Z={class:"dialog-footer"},J={class:"dialog-footer"},K={__name:"List",setup(e){const a=u([]),l=u(!1),t=u(null),r=u(""),o=i({type:"",name:"",review:"",status:""}),s=i({current:1,size:10,total:0}),B=u([]),E=u([]),K=u([]),M=u([]),W=u(!0),X=u(!0),Y=e=>{B.value=e.map((e=>e.id)),E.value=e.map((e=>e.name)),K.value=e.map((e=>e.review)),M.value=e.map((e=>e.sort)),W.value=1!=e.length,X.value=!e.length},ee=u(!1),ae=u(!1);n((()=>{le()}));const le=async()=>{l.value=!0;try{const e={page:s.current,size:s.size,type:null!==o.type?o.type:void 0,name:o.name||void 0,status:o.status||void 0,review:null!==o.review?o.review:void 0},t=await D(e);200===t.code?(a.value=t.data.list||[],s.total=t.data.total||0):d.error(t.message||"获取数据失败")}catch(e){console.error("获取数据失败:",e),"ECONNABORTED"===e.code?d.error("请求超时，请检查网络连接或联系管理员"):e.response?d.error(`请求失败: ${e.response.status} ${e.response.statusText}`):e.request?d.error("服务器未响应，请稍后再试"):d.error(`请求错误: ${e.message}`),a.value=[],s.total=0}finally{l.value=!1}},te=u([]),re=({row:e,column:a})=>{if(1==e.review)return"shenhe-row"},oe=()=>{le()},se=()=>{s.current=1,le()},ue=()=>{o.type="",o.name="",o.review="",o.status=null,s.current=1,le()},ie=e=>{s.size=e,s.current=1,le()},ne=e=>{s.current=e,le()},de=async()=>{if(!r.value)return void d.error("请填写驳回原因！！！");"200"==(await G(B.value,r.value)).code?(ee.value=!1,d.success("驳回成功"),r.value="",le()):d.error("系统异常，请联系管理员")},pe=async()=>{"200"==(await q(B.value,te.value)).code?(ae.value=!1,d.success("修改成功"),te.value=[],le()):d.error("系统异常，请联系管理员")};return(e,u)=>{const i=p,n=c,E=v,D=m,G=f,q=b,ce=y,ve=g,me=w,fe=_,be=h,ye=V,ge=j;return k(),z("div",F,[C("div",H,[x(q,{inline:!0,model:o},{default:O((()=>[x(E,{label:"违规类型"},{default:O((()=>[x(n,{modelValue:o.type,"onUpdate:modelValue":u[0]||(u[0]=e=>o.type=e),placeholder:"请选择违规类型",clearable:"",style:{"min-width":"150px"}},{default:O((()=>[x(i,{label:"isbn",value:0}),x(i,{label:"书名",value:1}),x(i,{label:"作者",value:2}),x(i,{label:"出版社",value:3})])),_:1},8,["modelValue"])])),_:1}),x(E,{label:"违规内容"},{default:O((()=>[x(D,{modelValue:o.name,"onUpdate:modelValue":u[1]||(u[1]=e=>o.name=e),placeholder:"请输入违规内容",clearable:""},null,8,["modelValue"])])),_:1}),x(E,{label:"审核状态"},{default:O((()=>[x(n,{modelValue:o.review,"onUpdate:modelValue":u[2]||(u[2]=e=>o.review=e),placeholder:"请选择审核状态",clearable:"",style:{"min-width":"150px"}},{default:O((()=>[x(i,{label:"待提交",value:0}),x(i,{label:"待审核",value:1}),x(i,{label:"已审核",value:2}),x(i,{label:"已撤回",value:3}),x(i,{label:"审核失败",value:4})])),_:1},8,["modelValue"])])),_:1}),x(E,{label:"状态"},{default:O((()=>[x(n,{modelValue:o.status,"onUpdate:modelValue":u[3]||(u[3]=e=>o.status=e),placeholder:"请选择状态",clearable:"",style:{"min-width":"150px"}},{default:O((()=>[x(i,{label:"正常",value:0}),x(i,{label:"停用",value:1})])),_:1},8,["modelValue"])])),_:1}),x(E,null,{default:O((()=>[x(G,{type:"primary",onClick:se},{default:O((()=>[U("搜索")])),_:1}),x(G,{onClick:ue},{default:O((()=>[U("重置")])),_:1})])),_:1})])),_:1},8,["model"])]),C("div",L,[x($,{onRefresh:oe},{left:O((()=>[x(G,{type:"success",disabled:X.value,onClick:u[4]||(u[4]=e=>(async()=>{for(var e=0;e<K.value.length;e++)if("1"!=K.value[e])return void d.error("请选择待审核的数据！！！");"200"==(await I(B.value)).code?(d.success("审核成功"),le()):d.error("系统异常，请联系管理员")})())},{default:O((()=>[U("通过")])),_:1},8,["disabled"]),x(G,{type:"danger",disabled:X.value,onClick:u[5]||(u[5]=e=>(()=>{for(var e=0;e<K.value.length;e++)if("1"!=K.value[e])return void d.error("请选择待审核的数据！！！");ee.value=!0})())},{default:O((()=>[U("驳回")])),_:1},8,["disabled"]),x(G,{type:"danger",disabled:X.value,onClick:u[6]||(u[6]=e=>(async()=>{await T(B.value),d.success("删除成功"),le()})())},{default:O((()=>[U("删除")])),_:1},8,["disabled"]),x(G,{type:"warning",disabled:W.value,onClick:u[7]||(u[7]=e=>(ae.value=!0,void(te.value=M.value[0].split(","))))},{default:O((()=>[U("修改")])),_:1},8,["disabled"])])),_:1}),S((k(),P(ve,{ref_key:"tableRef",ref:t,data:a.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:Y,"row-class-name":re},{default:O((()=>[x(ce,{type:"selection",width:"55",align:"center"}),(k(),P(ce,{key:0,label:"主键",align:"center",prop:"id"})),x(ce,{label:"违规平台",align:"center",prop:"sort",width:"250px"},{default:O((e=>[(k(!0),z(N,null,R(e.row.sort.split(","),(e=>(k(),z("span",{key:e},A({0:"拼多多",1:"孔夫子",2:"淘宝",3:"咸鱼"}[e]||"info")+"   ",1)))),128))])),_:1}),x(ce,{label:"违规类型",align:"center",prop:"type"},{default:O((({row:e})=>{return[U(A((a=e.type,{0:"isbn",1:"书名",2:"作者",3:"出版社"}[a]||a)),1)];var a})),_:1}),x(ce,{label:"违规内容",align:"center",prop:"name"}),x(ce,{label:"违规原因",align:"center",prop:"content"}),x(ce,{label:"审核状态",align:"center",prop:"review"},{default:O((({row:e})=>{return[U(A((a=e.review,{0:"待提交",1:"待审核",2:"已审核",3:"已撤回",4:"审核失败"}[a]||"info")),1)];var a})),_:1}),x(ce,{label:"审核意见",align:"center",prop:"remark"}),x(ce,{label:"状态",align:"center",prop:"status"},{default:O((({row:e})=>{return[U(A((a=e.status,{0:"正常",1:"停用"}[a]||"info")),1)];var a})),_:1})])),_:1},8,["data"])),[[ge,l.value]]),C("div",Q,[x(me,{"current-page":s.current,"onUpdate:currentPage":u[8]||(u[8]=e=>s.current=e),"page-size":s.size,"onUpdate:pageSize":u[9]||(u[9]=e=>s.size=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:s.total,onSizeChange:ie,onCurrentChange:ne},null,8,["current-page","page-size","total"])])]),x(fe,{title:"驳回原因",modelValue:ee.value,"onUpdate:modelValue":u[12]||(u[12]=e=>ee.value=e),width:"500px","close-on-click-modal":!1},{footer:O((()=>[C("span",Z,[x(G,{type:"primary",onClick:de},{default:O((()=>[U("提交")])),_:1}),x(G,{onClick:u[11]||(u[11]=e=>ee.value=!1)},{default:O((()=>[U("取消")])),_:1})])])),default:O((()=>[x(D,{modelValue:r.value,"onUpdate:modelValue":u[10]||(u[10]=e=>r.value=e),style:{width:"100%"},rows:10,type:"textarea",placeholder:"请输入驳回原因"},null,8,["modelValue"])])),_:1},8,["modelValue"]),x(fe,{title:"修改违规平台",modelValue:ae.value,"onUpdate:modelValue":u[15]||(u[15]=e=>ae.value=e),width:"500px"},{footer:O((()=>[C("span",J,[x(G,{type:"primary",onClick:pe},{default:O((()=>[U("提交")])),_:1}),x(G,{onClick:u[14]||(u[14]=e=>ae.value=!1)},{default:O((()=>[U("取消")])),_:1})])])),default:O((()=>[x(E,{label:"违规平台",prop:"sort"},{default:O((()=>[x(ye,{modelValue:te.value,"onUpdate:modelValue":u[13]||(u[13]=e=>te.value=e)},{default:O((()=>[(k(!0),z(N,null,R([{label:"拼多多",value:"0"},{label:"孔夫子",value:"1"},{label:"淘宝",value:"2"},{label:"咸鱼",value:"3"}],(e=>(k(),P(be,{key:e.value,label:e.value},{default:O((()=>[U(A(e.label),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["modelValue"])])}},__scopeId:"data-v-6ff71480"};export{K as default};
