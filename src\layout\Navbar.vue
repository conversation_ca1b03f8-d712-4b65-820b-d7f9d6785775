<template>
	<div class="navbar">
		<!-- 系统图标和标题 -->
		<div class="left-panel">
			<el-icon :size="24"><ElementPlus /></el-icon>
			<span class="system-title">{{ global?.system.name }}管理系统</span>
		</div>

		<!-- 右侧功能区 -->
		<div class="right-panel">
			<el-tooltip content="系统设置">
				<el-icon :size="20"><Setting /></el-icon>
			</el-tooltip>

			<el-badge :value="5" class="message-badge">
				<el-tooltip content="消息中心">
					<el-icon :size="20"><Message /></el-icon>
				</el-tooltip>
			</el-badge>

			<el-dropdown>
				<div class="user-avatar">
					<el-avatar :size="32" src="https://example.com/avatar.jpg" />
					<span class="user-name">{{ userName }}</span>
				</div>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item @click="goTo('/user/center')">
							<el-icon><User /></el-icon> 个人中心
						</el-dropdown-item>
						<el-dropdown-item @click="goTo('/log')">
							<el-icon><Document /></el-icon> 日志中心
						</el-dropdown-item>
						<el-dropdown-item divided @click="logout">
							<el-icon><SwitchButton /></el-icon> 退出登录
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { getCurrentInstance } from 'vue'
	import { useRouter } from 'vue-router'
	import { ElementPlus,Setting,Message,User,Document,SwitchButton } from '@element-plus/icons-vue'
	import store from '../store'
	// 获取 全局变量
	const global = getCurrentInstance()?.appContext.config.globalProperties.$global
	// 获取 用户信息
	const admin = JSON.parse(localStorage.getItem('userInfo'))
	// 获取 用户名
	const userName = admin?.username || 'admin'
	// 路由
	const router = useRouter()
	// 跳转
	const goTo = (path:string) => {
		router.push(path)
	}
	// 退出 登录
	const logout = async () => {
		// 弹出框 防误触
		if (confirm('确定要退出吗？')) {
			// 退出逻辑
			await store.dispatch('logout')
			// 跳转 到登录
			router.replace('/login')
		}
	}
</script>

<style scoped>
	.navbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
	}

	.left-panel {
		display: flex;
		align-items: center;
	}

	.system-title {
		margin-left: 10px;
		font-weight: bold;
		font-size: 18px;
	}

	.right-panel {
		display: flex;
		align-items: center;
		gap: 20px;
	}

	.message-badge {
		margin-right: 5px;
	}

	.user-avatar {
		display: flex;
		align-items: center;
		cursor: pointer;
	}
	
	.user-avatar:focus {
	  outline: none !important;
	}

	.user-name {
		margin-left: 8px;
	}
</style>