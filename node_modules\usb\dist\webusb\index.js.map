{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../tsc/webusb/index.ts"], "names": [], "mappings": ";;;AAAA,8BAA8B;AAC9B,mCAAsC;AACtC,mDAA+C;AAgC/C;;GAEG;AACI,MAAM,SAAS,GAAG,GAAQ,EAAE;IAC/B,IAAI,SAAS,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,SAAS,CAAC,GAAG,CAAC;IACzB,CAAC;IAED,OAAO,IAAI,MAAM,EAAE,CAAC;AACxB,CAAC,CAAC;AANW,QAAA,SAAS,aAMpB;AAEF,MAAM,UAAW,SAAQ,KAAK;IAC1B,YAAmB,OAAe,EAAE,IAAY;QAC5C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;CACJ;AAED,MAAa,MAAM;IAMf,YAAoB,UAAsB,EAAE;QAAxB,YAAO,GAAP,OAAO,CAAiB;QAJlC,YAAO,GAAG,IAAI,qBAAY,EAAE,CAAC;QAC7B,iBAAY,GAAkC,IAAI,GAAG,EAAE,CAAC;QACxD,sBAAiB,GAAG,IAAI,GAAG,EAAmB,CAAC;QAGrD,MAAM,qBAAqB,GAAG,KAAK,EAAE,MAAkB,EAAE,EAAE;YACvD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAElD,2DAA2D;YAC3D,IAAI,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;gBAClD,MAAM,KAAK,GAAG;oBACV,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,SAAS;iBACpB,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACxC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,wBAAwB,GAAG,KAAK,EAAE,MAAkB,EAAE,EAAE;YAC1D,4EAA4E;YAC5E,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAEhD,IAAI,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClD,MAAM,KAAK,GAAG;wBACV,IAAI,EAAE,YAAY;wBAClB,MAAM,EAAE,SAAS;qBACpB,CAAC;oBAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBAC3C,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;YACnC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAExD,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;gBACtB,OAAO;YACX,CAAC;YAED,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YACrD,CAAC;iBAAM,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;gBAChC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAC;YACxD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAExD,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;gBACtB,OAAO;YACX,CAAC;YAED,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YACxD,CAAC;iBAAM,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;gBAChC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAGD,IAAW,SAAS,CAAC,EAAoC;QACrD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAChC,CAAC;QAED,IAAI,EAAE,EAAE,CAAC;YACL,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAGD,IAAW,YAAY,CAAC,EAAoC;QACxD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QACnC,CAAC;QAED,IAAI,EAAE,EAAE,CAAC;YACL,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAIM,gBAAgB,CAAC,IAAY,EAAE,QAA0C;QAC5E,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAIM,mBAAmB,CAAC,IAAY,EAAE,QAAsD;QAC3F,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC;IAEM,aAAa,CAAC,MAAa;QAC9B,2BAA2B;QAC3B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,aAAa,CAAC,OAAiC;QACxD,oBAAoB;QACpB,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,IAAI,SAAS,CAAC,8DAA8D,CAAC,CAAC;QACxF,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,IAAI,SAAS,CAAC,6DAA6D,CAAC,CAAC;QACvF,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,IAAI,SAAS,CAAC,2DAA2D,CAAC,CAAC;QACrF,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAAC;QACrG,CAAC;QAED,gBAAgB;QAChB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC7B,sBAAsB;YACtB,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC9C,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;YAC1E,CAAC;YAED,mBAAmB;YACnB,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC3C,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAC;YACvE,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QAE/E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,UAAU,CAAC,qEAAqE,EAAE,eAAe,CAAC,CAAC;QACjH,CAAC;QAED,IAAI,CAAC;YACD,6DAA6D;YAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEjG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,IAAI,UAAU,CAAC,qEAAqE,EAAE,eAAe,CAAC,CAAC;YACjH,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;gBACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,SAAS,EAAE,MAAM,CAAC,WAAW;gBAC7B,YAAY,EAAE,MAAM,CAAC,cAAc;gBACnC,YAAY,EAAE,MAAM,CAAC,cAAc;gBACnC,YAAY,EAAE,MAAM,CAAC,YAAY;aACpC,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,UAAU,CAAC,qEAAqE,EAAE,eAAe,CAAC,CAAC;QACjH,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,UAAU;QACnB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAE1F,8CAA8C;QAC9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAEnD,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,UAA8B;QACpD,IAAI,OAAO,GAAG,GAAG,CAAC,aAAa,EAAE,CAAC;QAElC,qBAAqB;QACrB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAEhD,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAA4B,CAAC;QAElE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAElD,IAAI,SAAS,EAAE,CAAC;gBACZ,qBAAqB,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACjD,CAAC;QACL,CAAC;QAED,0DAA0D;QAC1D,IAAI,CAAC,YAAY,GAAG,qBAAqB,CAAC;QAE1C,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,yDAAyD;IACzD,uEAAuE;IAC/D,KAAK,CAAC,YAAY,CAAC,MAAkB;QACzC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC7B,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;YAChD,CAAC;YAED,IAAI,CAAC;gBACD,MAAM,SAAS,GAAG,MAAM,4BAAY,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;gBACjG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC7C,CAAC;YAAC,MAAM,CAAC;gBACL,wDAAwD;YAC5D,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED,+EAA+E;IACvE,WAAW,CAAC,OAAqB,EAAE,UAA8B;QACrE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,OAAO,CAAC;QACnB,CAAC;QAED,6BAA6B;QAC7B,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACrD,SAAS;YACT,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,gBAAgB,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAE1F,UAAU;YACV,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,gBAAgB,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAE7F,6EAA6E;YAC7E,qEAAqE;YACrE,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;IAED,wBAAwB;IAChB,YAAY,CAAC,MAAiB,EAAE,OAA2B;QAC/D,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACzB,SAAS;YACT,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAEzE,UAAU;YACV,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAE5E,QAAQ;YACR,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBAEnB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;oBACxB,OAAO,KAAK,CAAC;gBACjB,CAAC;gBAED,wBAAwB;gBACxB,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvD,QAAQ;oBACR,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,cAAc;wBAAE,OAAO,KAAK,CAAC;oBAE1F,WAAW;oBACX,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,iBAAiB;wBAAE,OAAO,KAAK,CAAC;oBAEnG,WAAW;oBACX,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,iBAAiB;wBAAE,OAAO,KAAK,CAAC;oBAEnG,OAAO,IAAI,CAAC;gBAChB,CAAC,CAAC,CAAC;gBAEH,IAAI,KAAK,EAAE,CAAC;oBACR,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;YAED,QAAQ;YACR,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAC;YAE9E,WAAW;YACX,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,cAAc;gBAAE,OAAO,KAAK,CAAC;YAEvF,WAAW;YACX,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,cAAc;gBAAE,OAAO,KAAK,CAAC;YAEvF,SAAS;YACT,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,YAAY;gBAAE,OAAO,KAAK,CAAC;YAErF,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,uCAAuC;IAC/B,kBAAkB,CAAC,MAAiB;QACxC,6BAA6B;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YACxF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,2BAA2B;QAC3B,OAAO,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAC1D,UAAU,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ;eACpC,UAAU,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS;eACzC,UAAU,CAAC,SAAS,KAAK,MAAM,CAAC,WAAW;eAC3C,UAAU,CAAC,YAAY,KAAK,MAAM,CAAC,cAAc;eACjD,UAAU,CAAC,YAAY,KAAK,MAAM,CAAC,cAAc;eACjD,UAAU,CAAC,YAAY,KAAK,MAAM,CAAC,YAAY,CACrD,CAAC;IACN,CAAC;CACJ;AAzUD,wBAyUC"}