{"version": 3, "file": "node2.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/node.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  capitalize,\n  isArray,\n  isEmpty,\n  isFunction,\n  isUndefined,\n} from '@element-plus/utils'\nimport type { VNode } from 'vue'\n\nexport type CascaderNodeValue = string | number\nexport type CascaderNodePathValue = CascaderNodeValue[]\nexport type CascaderValue =\n  | CascaderNodeValue\n  | CascaderNodePathValue\n  | (CascaderNodeValue | CascaderNodePathValue)[]\nexport type CascaderConfig = Required<CascaderProps>\nexport type ExpandTrigger = 'click' | 'hover'\nexport type isDisabled = (data: CascaderOption, node: Node) => boolean\nexport type isLeaf = (data: CascaderOption, node: Node) => boolean\nexport type Resolve = (dataList?: CascaderOption[]) => void\nexport type LazyLoad = (node: Node, resolve: Resolve) => void\nexport type RenderLabel = ({\n  node: Node,\n  data: CascaderOption,\n}) => VNode | VNode[]\nexport interface CascaderOption extends Record<string, unknown> {\n  label?: string\n  value?: CascaderNodeValue\n  children?: CascaderOption[]\n  disabled?: boolean\n  leaf?: boolean\n}\n\nexport interface CascaderProps {\n  expandTrigger?: ExpandTrigger\n  multiple?: boolean\n  checkStrictly?: boolean\n  emitPath?: boolean\n  lazy?: boolean\n  lazyLoad?: LazyLoad\n  value?: string\n  label?: string\n  children?: string\n  disabled?: string | isDisabled\n  leaf?: string | isLeaf\n  hoverThreshold?: number\n}\n\nexport type Nullable<T> = null | T\n\ntype ChildrenData = CascaderOption[] | undefined\n\nlet uid = 0\n\nconst calculatePathNodes = (node: Node) => {\n  const nodes = [node]\n  let { parent } = node\n\n  while (parent) {\n    nodes.unshift(parent)\n    parent = parent.parent\n  }\n\n  return nodes\n}\n\nclass Node {\n  readonly uid: number = uid++\n  readonly level: number\n  readonly value: CascaderNodeValue\n  readonly label: string\n  readonly pathNodes: Node[]\n  readonly pathValues: CascaderNodePathValue\n  readonly pathLabels: string[]\n\n  childrenData: ChildrenData\n  children: Node[]\n  text: string\n  loaded: boolean\n  /**\n   * Is it checked\n   *\n   * @default false\n   */\n  checked = false\n  /**\n   * Used to indicate the intermediate state of unchecked and fully checked child nodes\n   *\n   * @default false\n   */\n  indeterminate = false\n  /**\n   * Loading Status\n   *\n   * @default false\n   */\n  loading = false\n\n  constructor(\n    readonly data: Nullable<CascaderOption>,\n    readonly config: CascaderConfig,\n    readonly parent?: Node,\n    readonly root = false\n  ) {\n    const { value: valueKey, label: labelKey, children: childrenKey } = config\n\n    const childrenData = data[childrenKey] as ChildrenData\n    const pathNodes = calculatePathNodes(this)\n\n    this.level = root ? 0 : parent ? parent.level + 1 : 1\n    this.value = data[valueKey] as CascaderNodeValue\n    this.label = data[labelKey] as string\n    this.pathNodes = pathNodes\n    this.pathValues = pathNodes.map((node) => node.value)\n    this.pathLabels = pathNodes.map((node) => node.label)\n    this.childrenData = childrenData\n    this.children = (childrenData || []).map(\n      (child) => new Node(child, config, this)\n    )\n    this.loaded = !config.lazy || this.isLeaf || !isEmpty(childrenData)\n  }\n\n  get isDisabled(): boolean {\n    const { data, parent, config } = this\n    const { disabled, checkStrictly } = config\n    const isDisabled = isFunction(disabled)\n      ? disabled(data, this)\n      : !!data[disabled]\n    return isDisabled || (!checkStrictly && parent?.isDisabled)\n  }\n\n  get isLeaf(): boolean {\n    const { data, config, childrenData, loaded } = this\n    const { lazy, leaf } = config\n    const isLeaf = isFunction(leaf) ? leaf(data, this) : data[leaf]\n\n    return isUndefined(isLeaf)\n      ? lazy && !loaded\n        ? false\n        : !(isArray(childrenData) && childrenData.length)\n      : !!isLeaf\n  }\n\n  get valueByOption() {\n    return this.config.emitPath ? this.pathValues : this.value\n  }\n\n  appendChild(childData: CascaderOption) {\n    const { childrenData, children } = this\n    const node = new Node(childData, this.config, this)\n\n    if (isArray(childrenData)) {\n      childrenData.push(childData)\n    } else {\n      this.childrenData = [childData]\n    }\n\n    children.push(node)\n\n    return node\n  }\n\n  calcText(allLevels: boolean, separator: string) {\n    const text = allLevels ? this.pathLabels.join(separator) : this.label\n    this.text = text\n    return text\n  }\n\n  broadcast(event: string, ...args: unknown[]) {\n    const handlerName = `onParent${capitalize(event)}`\n    this.children.forEach((child) => {\n      if (child) {\n        // bottom up\n        child.broadcast(event, ...args)\n        child[handlerName] && child[handlerName](...args)\n      }\n    })\n  }\n\n  emit(event: string, ...args: unknown[]) {\n    const { parent } = this\n    const handlerName = `onChild${capitalize(event)}`\n    if (parent) {\n      parent[handlerName] && parent[handlerName](...args)\n      parent.emit(event, ...args)\n    }\n  }\n\n  onParentCheck(checked: boolean) {\n    if (!this.isDisabled) {\n      this.setCheckState(checked)\n    }\n  }\n\n  onChildCheck() {\n    const { children } = this\n    const validChildren = children.filter((child) => !child.isDisabled)\n    const checked = validChildren.length\n      ? validChildren.every((child) => child.checked)\n      : false\n\n    this.setCheckState(checked)\n  }\n\n  setCheckState(checked: boolean) {\n    const totalNum = this.children.length\n    const checkedNum = this.children.reduce((c, p) => {\n      const num = p.checked ? 1 : p.indeterminate ? 0.5 : 0\n      return c + num\n    }, 0)\n\n    this.checked =\n      this.loaded &&\n      this.children\n        .filter((child) => !child.isDisabled)\n        .every((child) => child.loaded && child.checked) &&\n      checked\n    this.indeterminate =\n      this.loaded && checkedNum !== totalNum && checkedNum > 0\n  }\n\n  doCheck(checked: boolean) {\n    if (this.checked === checked) return\n\n    const { checkStrictly, multiple } = this.config\n\n    if (checkStrictly || !multiple) {\n      this.checked = checked\n    } else {\n      // bottom up to unify the calculation of the indeterminate state\n      this.broadcast('check', checked)\n      this.setCheckState(checked)\n      this.emit('check')\n    }\n  }\n}\n\nexport default Node\n"], "names": [], "mappings": ";;;;AAOA,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,MAAM,kBAAkB,GAAG,CAAC,IAAI,KAAK;AACrC,EAAE,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AACxB,EAAE,OAAO,MAAM,EAAE;AACjB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1B,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AACF,MAAM,IAAI,CAAC;AACX,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,GAAG,KAAK,EAAE;AAClD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;AAC/E,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AAC3C,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACvF,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACxE,GAAG;AACH,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AAC1C,IAAI,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,MAAM,CAAC;AAC/C,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtF,IAAI,OAAO,UAAU,IAAI,CAAC,aAAa,KAAK,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACzF,GAAG;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AACxD,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AAClC,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACtH,GAAG;AACH,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/D,GAAG;AACH,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAC5C,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxD,IAAI,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;AAC/B,MAAM,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACnC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC,SAAS,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE;AACjC,IAAI,MAAM,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1E,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,SAAS,CAAC,KAAK,EAAE,GAAG,IAAI,EAAE;AAC5B,IAAI,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACrC,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AACxC,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC1D,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,EAAE;AACvB,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AAC5B,IAAI,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC1D,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,aAAa,CAAC,OAAO,EAAE;AACzB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAC9B,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACxE,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AACjG,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,aAAa,CAAC,OAAO,EAAE;AACzB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1C,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACtD,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,aAAa,GAAG,GAAG,GAAG,CAAC,CAAC;AAC5D,MAAM,OAAO,CAAC,GAAG,GAAG,CAAC;AACrB,KAAK,EAAE,CAAC,CAAC,CAAC;AACV,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC;AAChJ,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,IAAI,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC;AAClF,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO;AAChC,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;AACpD,IAAI,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE;AACpC,MAAM,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACvC,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzB,KAAK;AACL,GAAG;AACH;;;;"}