{"version": 3, "file": "step.mjs", "sources": ["../../../../../../packages/components/tour/src/step.vue"], "sourcesContent": ["<template>\n  <button\n    v-if=\"mergedShowClose\"\n    aria-label=\"Close\"\n    :class=\"ns.e('closebtn')\"\n    type=\"button\"\n    @click=\"onClose\"\n  >\n    <el-icon :class=\"ns.e('close')\">\n      <component :is=\"mergedCloseIcon\" />\n    </el-icon>\n  </button>\n  <header :class=\"[ns.e('header'), { 'show-close': showClose }]\">\n    <slot name=\"header\">\n      <span role=\"heading\" :class=\"ns.e('title')\">\n        {{ title }}\n      </span>\n    </slot>\n  </header>\n  <div :class=\"ns.e('body')\">\n    <slot>\n      <span>{{ description }}</span>\n    </slot>\n  </div>\n  <footer :class=\"ns.e('footer')\">\n    <div :class=\"ns.b('indicators')\">\n      <component\n        :is=\"tourSlots.indicators\"\n        v-if=\"tourSlots.indicators\"\n        :current=\"current\"\n        :total=\"total\"\n      />\n      <template v-else>\n        <span\n          v-for=\"(item, index) in total\"\n          :key=\"item\"\n          :class=\"[ns.b('indicator'), index === current ? 'is-active' : '']\"\n        />\n      </template>\n    </div>\n    <div :class=\"ns.b('buttons')\">\n      <el-button\n        v-if=\"current > 0\"\n        size=\"small\"\n        :type=\"mergedType\"\n        v-bind=\"filterButtonProps(prevButtonProps)\"\n        @click=\"onPrev\"\n      >\n        {{ prevButtonProps?.children ?? t('el.tour.previous') }}\n      </el-button>\n      <el-button\n        v-if=\"current <= total - 1\"\n        size=\"small\"\n        :type=\"mergedType === 'primary' ? 'default' : 'primary'\"\n        v-bind=\"filterButtonProps(nextButtonProps)\"\n        @click=\"onNext\"\n      >\n        {{\n          nextButtonProps?.children ??\n          (current === total - 1 ? t('el.tour.finish') : t('el.tour.next'))\n        }}\n      </el-button>\n    </div>\n  </footer>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, watch } from 'vue'\nimport { omit } from 'lodash-unified'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CloseComponents } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport { tourStepEmits, tourStepProps } from './step'\nimport { tourKey } from './helper'\n\nimport type { TourBtnProps } from './types'\n\ndefineOptions({\n  name: 'ElTourStep',\n})\n\nconst props = defineProps(tourStepProps)\nconst emit = defineEmits(tourStepEmits)\n\nconst { Close } = CloseComponents\n\nconst { t } = useLocale()\n\nconst {\n  currentStep,\n  current,\n  total,\n  showClose,\n  closeIcon,\n  mergedType,\n  ns,\n  slots: tourSlots,\n  updateModelValue,\n  onClose: tourOnClose,\n  onFinish: tourOnFinish,\n  onChange,\n} = inject(tourKey)!\n\nwatch(\n  props,\n  (val) => {\n    currentStep.value = val\n  },\n  {\n    immediate: true,\n  }\n)\n\nconst mergedShowClose = computed(() => props.showClose ?? showClose.value)\nconst mergedCloseIcon = computed(\n  () => props.closeIcon ?? closeIcon.value ?? Close\n)\n\nconst filterButtonProps = (btnProps?: TourBtnProps) => {\n  if (!btnProps) return\n  return omit(btnProps, ['children', 'onClick'])\n}\n\nconst onPrev = () => {\n  current.value -= 1\n  if (props.prevButtonProps?.onClick) {\n    props.prevButtonProps?.onClick()\n  }\n  onChange()\n}\n\nconst onNext = () => {\n  if (current.value >= total.value - 1) {\n    onFinish()\n  } else {\n    current.value += 1\n  }\n  if (props.nextButtonProps?.onClick) {\n    props.nextButtonProps.onClick()\n  }\n  onChange()\n}\n\nconst onFinish = () => {\n  onClose()\n  tourOnFinish()\n}\n\nconst onClose = () => {\n  updateModelValue(false)\n  tourOnClose()\n  emit('close')\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCA8Ec,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,OAAU,GAAA,eAAA,CAAA;AAElB,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AAExB,IAAM,MAAA;AAAA,MACJ,WAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,SAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,EAAA;AAAA,MACA,KAAO,EAAA,SAAA;AAAA,MACP,gBAAA;AAAA,MACA,OAAS,EAAA,WAAA;AAAA,MACT,QAAU,EAAA,YAAA;AAAA,MACV,QAAA;AAAA,KACF,GAAI,OAAO,OAAO,CAAA,CAAA;AAElB,IAAA,KAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACE,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAAA,KAAA,EACC;AACC,MAAA,SAAA,EAAA,IAAoB;AAAA,KACtB,CAAA,CAAA;AAAA,IACA,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,IACa,EAAA,CAAA;AAAA,MACb,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,MAAM,kBAAkB,QAAS,CAAA,MAAM;AACvC,MAAA,IAAM,EAAkB,EAAA,EAAA,CAAA;AAAA,MACtB,OAAM,CAAA,EAAA,GAAmB,CAAA,EAAA,GAAA,KAAA,CAAA,SAAA,KAAmB,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA;AAAA,KAC9C,CAAA,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,QAA4B,KAAA;AACrD,MAAA,IAAI,CAAC,QAAU;AACf,QAAA,OAAY;AAAiC,MAC/C,OAAA,IAAA,CAAA,QAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,MAAiB,GAAA,MAAA;AACjB,MAAI,IAAA,EAAA,EAAA,EAAM;AACR,MAAA,OAAA,CAAM;AAAyB,MACjC,IAAA,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA;AACA,QAAS,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA,CAAA;AAAA,OACX;AAEA,MAAA;AACE,KAAA,CAAA;AACE,IAAS,MAAA,MAAA,GAAA,MAAA;AAAA,MACX,IAAO,EAAA,CAAA;AACL,MAAA,IAAA,OAAiB,CAAA,KAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AAAA,QACnB,QAAA,EAAA,CAAA;AACA,OAAI,MAAA;AACF,QAAA;AAA8B,OAChC;AACA,MAAS,IAAA,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA;AAAA,QACX,KAAA,CAAA,eAAA,CAAA,OAAA,EAAA,CAAA;AAEA,OAAA;AACE,MAAQ,QAAA,EAAA,CAAA;AACR,KAAa,CAAA;AAAA,IACf,MAAA,QAAA,GAAA,MAAA;AAEA,MAAA;AACE,MAAA,YAAA,EAAA,CAAA;AACA,KAAY,CAAA;AACZ,IAAA,MAAA,OAAY,GAAA,MAAA;AAAA,MACd,gBAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}