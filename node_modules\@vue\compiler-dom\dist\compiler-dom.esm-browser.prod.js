/**
* @vue/compiler-dom v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
function e(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const t={},n=()=>{},s=()=>!1,i=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),o=Object.assign,r=Array.isArray,a=e=>"string"==typeof e,c=e=>"symbol"==typeof e,l=e=>null!==e&&"object"==typeof e,h=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),d=e("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),p=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},u=/-(\w)/g,f=p((e=>e.replace(u,((e,t)=>t?t.toUpperCase():"")))),m=p((e=>e.charAt(0).toUpperCase()+e.slice(1))),_=p((e=>e?`on${m(e)}`:""));function E(e,t=0,n=e.length){let s=e.split(/(\r?\n)/);const i=s.filter(((e,t)=>t%2==1));s=s.filter(((e,t)=>t%2==0));let o=0;const r=[];for(let a=0;a<s.length;a++)if(o+=s[a].length+(i[a]&&i[a].length||0),o>=t){for(let e=a-2;e<=a+2||n>o;e++){if(e<0||e>=s.length)continue;const c=e+1;r.push(`${c}${" ".repeat(Math.max(3-String(c).length,0))}|  ${s[e]}`);const l=s[e].length,h=i[e]&&i[e].length||0;if(e===a){const e=t-(o-(l+h)),s=Math.max(1,n>o?l-e:n-t);r.push("   |  "+" ".repeat(e)+"^".repeat(s))}else if(e>a){if(n>o){const e=Math.max(Math.min(n-o,l),1);r.push("   |  "+"^".repeat(e))}o+=l+h}}break}return r.join("\n")}const S=/;(?![^(]*\))/g,g=/:([^]+)/,T=/\/\*[^]*?\*\//g;const N=e("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),I=e("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),y=e("annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics"),O=e("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),A=Symbol(""),b=Symbol(""),v=Symbol(""),C=Symbol(""),x=Symbol(""),R=Symbol(""),L=Symbol(""),M=Symbol(""),P=Symbol(""),D=Symbol(""),k=Symbol(""),V=Symbol(""),X=Symbol(""),w=Symbol(""),U=Symbol(""),F=Symbol(""),B=Symbol(""),$=Symbol(""),H=Symbol(""),G=Symbol(""),q=Symbol(""),J=Symbol(""),j=Symbol(""),W=Symbol(""),K=Symbol(""),Y=Symbol(""),Q=Symbol(""),z=Symbol(""),Z=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),se=Symbol(""),ie=Symbol(""),oe=Symbol(""),re=Symbol(""),ae=Symbol(""),ce=Symbol(""),le=Symbol(""),he={[A]:"Fragment",[b]:"Teleport",[v]:"Suspense",[C]:"KeepAlive",[x]:"BaseTransition",[R]:"openBlock",[L]:"createBlock",[M]:"createElementBlock",[P]:"createVNode",[D]:"createElementVNode",[k]:"createCommentVNode",[V]:"createTextVNode",[X]:"createStaticVNode",[w]:"resolveComponent",[U]:"resolveDynamicComponent",[F]:"resolveDirective",[B]:"resolveFilter",[$]:"withDirectives",[H]:"renderList",[G]:"renderSlot",[q]:"createSlots",[J]:"toDisplayString",[j]:"mergeProps",[W]:"normalizeClass",[K]:"normalizeStyle",[Y]:"normalizeProps",[Q]:"guardReactiveProps",[z]:"toHandlers",[Z]:"camelize",[ee]:"capitalize",[te]:"toHandlerKey",[ne]:"setBlockTracking",[se]:"pushScopeId",[ie]:"popScopeId",[oe]:"withCtx",[re]:"unref",[ae]:"isRef",[ce]:"withMemo",[le]:"isMemoSame"};function de(e){Object.getOwnPropertySymbols(e).forEach((t=>{he[t]=e[t]}))}const pe={HTML:0,0:"HTML",SVG:1,1:"SVG",MATH_ML:2,2:"MATH_ML"},ue={ROOT:0,0:"ROOT",ELEMENT:1,1:"ELEMENT",TEXT:2,2:"TEXT",COMMENT:3,3:"COMMENT",SIMPLE_EXPRESSION:4,4:"SIMPLE_EXPRESSION",INTERPOLATION:5,5:"INTERPOLATION",ATTRIBUTE:6,6:"ATTRIBUTE",DIRECTIVE:7,7:"DIRECTIVE",COMPOUND_EXPRESSION:8,8:"COMPOUND_EXPRESSION",IF:9,9:"IF",IF_BRANCH:10,10:"IF_BRANCH",FOR:11,11:"FOR",TEXT_CALL:12,12:"TEXT_CALL",VNODE_CALL:13,13:"VNODE_CALL",JS_CALL_EXPRESSION:14,14:"JS_CALL_EXPRESSION",JS_OBJECT_EXPRESSION:15,15:"JS_OBJECT_EXPRESSION",JS_PROPERTY:16,16:"JS_PROPERTY",JS_ARRAY_EXPRESSION:17,17:"JS_ARRAY_EXPRESSION",JS_FUNCTION_EXPRESSION:18,18:"JS_FUNCTION_EXPRESSION",JS_CONDITIONAL_EXPRESSION:19,19:"JS_CONDITIONAL_EXPRESSION",JS_CACHE_EXPRESSION:20,20:"JS_CACHE_EXPRESSION",JS_BLOCK_STATEMENT:21,21:"JS_BLOCK_STATEMENT",JS_TEMPLATE_LITERAL:22,22:"JS_TEMPLATE_LITERAL",JS_IF_STATEMENT:23,23:"JS_IF_STATEMENT",JS_ASSIGNMENT_EXPRESSION:24,24:"JS_ASSIGNMENT_EXPRESSION",JS_SEQUENCE_EXPRESSION:25,25:"JS_SEQUENCE_EXPRESSION",JS_RETURN_STATEMENT:26,26:"JS_RETURN_STATEMENT"},fe={ELEMENT:0,0:"ELEMENT",COMPONENT:1,1:"COMPONENT",SLOT:2,2:"SLOT",TEMPLATE:3,3:"TEMPLATE"},me={NOT_CONSTANT:0,0:"NOT_CONSTANT",CAN_SKIP_PATCH:1,1:"CAN_SKIP_PATCH",CAN_HOIST:2,2:"CAN_HOIST",CAN_STRINGIFY:3,3:"CAN_STRINGIFY"},_e={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function Ee(e,t=""){return{type:0,source:t,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:_e}}function Se(e,t,n,s,i,o,r,a=!1,c=!1,l=!1,h=_e){return e&&(a?(e.helper(R),e.helper(Ve(e.inSSR,l))):e.helper(ke(e.inSSR,l)),r&&e.helper($)),{type:13,tag:t,props:n,children:s,patchFlag:i,dynamicProps:o,directives:r,isBlock:a,disableTracking:c,isComponent:l,loc:h}}function ge(e,t=_e){return{type:17,loc:t,elements:e}}function Te(e,t=_e){return{type:15,loc:t,properties:e}}function Ne(e,t){return{type:16,loc:_e,key:a(e)?Ie(e,!0):e,value:t}}function Ie(e,t=!1,n=_e,s=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:s}}function ye(e,t){return{type:5,loc:t,content:a(e)?Ie(e,!1,t):e}}function Oe(e,t=_e){return{type:8,loc:t,children:e}}function Ae(e,t=[],n=_e){return{type:14,loc:n,callee:e,arguments:t}}function be(e,t=void 0,n=!1,s=!1,i=_e){return{type:18,params:e,returns:t,newline:n,isSlot:s,loc:i}}function ve(e,t,n,s=!0){return{type:19,test:e,consequent:t,alternate:n,newline:s,loc:_e}}function Ce(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:_e}}function xe(e){return{type:21,body:e,loc:_e}}function Re(e){return{type:22,elements:e,loc:_e}}function Le(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:_e}}function Me(e,t){return{type:24,left:e,right:t,loc:_e}}function Pe(e){return{type:25,expressions:e,loc:_e}}function De(e){return{type:26,returns:e,loc:_e}}function ke(e,t){return e||t?P:D}function Ve(e,t){return e||t?L:M}function Xe(e,{helper:t,removeHelper:n,inSSR:s}){e.isBlock||(e.isBlock=!0,n(ke(s,e.isComponent)),t(R),t(Ve(s,e.isComponent)))}const we=new Uint8Array([123,123]),Ue=new Uint8Array([125,125]);function Fe(e){return e>=97&&e<=122||e>=65&&e<=90}function Be(e){return 32===e||10===e||9===e||12===e||13===e}function $e(e){return 47===e||62===e||Be(e)}function He(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}const Ge={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])};const qe={COMPILER_IS_ON_ELEMENT:"COMPILER_IS_ON_ELEMENT",COMPILER_V_BIND_SYNC:"COMPILER_V_BIND_SYNC",COMPILER_V_BIND_OBJECT_ORDER:"COMPILER_V_BIND_OBJECT_ORDER",COMPILER_V_ON_NATIVE:"COMPILER_V_ON_NATIVE",COMPILER_V_IF_V_FOR_PRECEDENCE:"COMPILER_V_IF_V_FOR_PRECEDENCE",COMPILER_NATIVE_TEMPLATE:"COMPILER_NATIVE_TEMPLATE",COMPILER_INLINE_TEMPLATE:"COMPILER_INLINE_TEMPLATE",COMPILER_FILTERS:"COMPILER_FILTERS"},Je={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTERS:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function je(e,{compatConfig:t}){const n=t&&t[e];return"MODE"===e?n||3:n}function We(e,t){const n=je("MODE",t),s=je(e,t);return 3===n?!0===s:!1!==s}function Ke(e,t,n,...s){return We(e,t)}function Ye(e,t,n,...s){if("suppress-warning"===je(e,t))return;const{message:i,link:o}=Je[e],r=`(deprecation ${e}) ${"function"==typeof i?i(...s):i}${o?`\n  Details: ${o}`:""}`,a=new SyntaxError(r);a.code=e,n&&(a.loc=n),t.onWarn(a)}function Qe(e){throw e}function ze(e){}function Ze(e,t,n,s){const i=new SyntaxError(String(`https://vuejs.org/error-reference/#compiler-${e}`));return i.code=e,i.loc=t,i}const et={ABRUPT_CLOSING_OF_EMPTY_COMMENT:0,0:"ABRUPT_CLOSING_OF_EMPTY_COMMENT",CDATA_IN_HTML_CONTENT:1,1:"CDATA_IN_HTML_CONTENT",DUPLICATE_ATTRIBUTE:2,2:"DUPLICATE_ATTRIBUTE",END_TAG_WITH_ATTRIBUTES:3,3:"END_TAG_WITH_ATTRIBUTES",END_TAG_WITH_TRAILING_SOLIDUS:4,4:"END_TAG_WITH_TRAILING_SOLIDUS",EOF_BEFORE_TAG_NAME:5,5:"EOF_BEFORE_TAG_NAME",EOF_IN_CDATA:6,6:"EOF_IN_CDATA",EOF_IN_COMMENT:7,7:"EOF_IN_COMMENT",EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT:8,8:"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT",EOF_IN_TAG:9,9:"EOF_IN_TAG",INCORRECTLY_CLOSED_COMMENT:10,10:"INCORRECTLY_CLOSED_COMMENT",INCORRECTLY_OPENED_COMMENT:11,11:"INCORRECTLY_OPENED_COMMENT",INVALID_FIRST_CHARACTER_OF_TAG_NAME:12,12:"INVALID_FIRST_CHARACTER_OF_TAG_NAME",MISSING_ATTRIBUTE_VALUE:13,13:"MISSING_ATTRIBUTE_VALUE",MISSING_END_TAG_NAME:14,14:"MISSING_END_TAG_NAME",MISSING_WHITESPACE_BETWEEN_ATTRIBUTES:15,15:"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES",NESTED_COMMENT:16,16:"NESTED_COMMENT",UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME:17,17:"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME",UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE:18,18:"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE",UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME:19,19:"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME",UNEXPECTED_NULL_CHARACTER:20,20:"UNEXPECTED_NULL_CHARACTER",UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME:21,21:"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME",UNEXPECTED_SOLIDUS_IN_TAG:22,22:"UNEXPECTED_SOLIDUS_IN_TAG",X_INVALID_END_TAG:23,23:"X_INVALID_END_TAG",X_MISSING_END_TAG:24,24:"X_MISSING_END_TAG",X_MISSING_INTERPOLATION_END:25,25:"X_MISSING_INTERPOLATION_END",X_MISSING_DIRECTIVE_NAME:26,26:"X_MISSING_DIRECTIVE_NAME",X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END:27,27:"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END",X_V_IF_NO_EXPRESSION:28,28:"X_V_IF_NO_EXPRESSION",X_V_IF_SAME_KEY:29,29:"X_V_IF_SAME_KEY",X_V_ELSE_NO_ADJACENT_IF:30,30:"X_V_ELSE_NO_ADJACENT_IF",X_V_FOR_NO_EXPRESSION:31,31:"X_V_FOR_NO_EXPRESSION",X_V_FOR_MALFORMED_EXPRESSION:32,32:"X_V_FOR_MALFORMED_EXPRESSION",X_V_FOR_TEMPLATE_KEY_PLACEMENT:33,33:"X_V_FOR_TEMPLATE_KEY_PLACEMENT",X_V_BIND_NO_EXPRESSION:34,34:"X_V_BIND_NO_EXPRESSION",X_V_ON_NO_EXPRESSION:35,35:"X_V_ON_NO_EXPRESSION",X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET:36,36:"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET",X_V_SLOT_MIXED_SLOT_USAGE:37,37:"X_V_SLOT_MIXED_SLOT_USAGE",X_V_SLOT_DUPLICATE_SLOT_NAMES:38,38:"X_V_SLOT_DUPLICATE_SLOT_NAMES",X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN:39,39:"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN",X_V_SLOT_MISPLACED:40,40:"X_V_SLOT_MISPLACED",X_V_MODEL_NO_EXPRESSION:41,41:"X_V_MODEL_NO_EXPRESSION",X_V_MODEL_MALFORMED_EXPRESSION:42,42:"X_V_MODEL_MALFORMED_EXPRESSION",X_V_MODEL_ON_SCOPE_VARIABLE:43,43:"X_V_MODEL_ON_SCOPE_VARIABLE",X_V_MODEL_ON_PROPS:44,44:"X_V_MODEL_ON_PROPS",X_INVALID_EXPRESSION:45,45:"X_INVALID_EXPRESSION",X_KEEP_ALIVE_INVALID_CHILDREN:46,46:"X_KEEP_ALIVE_INVALID_CHILDREN",X_PREFIX_ID_NOT_SUPPORTED:47,47:"X_PREFIX_ID_NOT_SUPPORTED",X_MODULE_MODE_NOT_SUPPORTED:48,48:"X_MODULE_MODE_NOT_SUPPORTED",X_CACHE_HANDLER_NOT_SUPPORTED:49,49:"X_CACHE_HANDLER_NOT_SUPPORTED",X_SCOPE_ID_NOT_SUPPORTED:50,50:"X_SCOPE_ID_NOT_SUPPORTED",X_VNODE_HOOKS:51,51:"X_VNODE_HOOKS",X_V_BIND_INVALID_SAME_NAME_ARGUMENT:52,52:"X_V_BIND_INVALID_SAME_NAME_ARGUMENT",__EXTEND_POINT__:53,53:"__EXTEND_POINT__"},tt={0:"Illegal comment.",1:"CDATA section is allowed only in XML context.",2:"Duplicate attribute.",3:"End tag cannot have attributes.",4:"Illegal '/' in tags.",5:"Unexpected EOF in tag.",6:"Unexpected EOF in CDATA section.",7:"Unexpected EOF in comment.",8:"Unexpected EOF in script.",9:"Unexpected EOF in tag.",10:"Incorrectly closed comment.",11:"Incorrectly opened comment.",12:"Illegal tag name. Use '&lt;' to print '<'.",13:"Attribute value was expected.",14:"End tag name was expected.",15:"Whitespace was expected.",16:"Unexpected '\x3c!--' in comment.",17:"Attribute name cannot contain U+0022 (\"), U+0027 ('), and U+003C (<).",18:"Unquoted attribute value cannot contain U+0022 (\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).",19:"Attribute name cannot start with '='.",21:"'<?' is allowed only in XML context.",20:"Unexpected null character.",22:"Illegal '/' in tags.",23:"Invalid end tag.",24:"Element is missing end tag.",25:"Interpolation end sign was not found.",27:"End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.",26:"Legal directive name was expected.",28:"v-if/v-else-if is missing expression.",29:"v-if/else branches must use unique keys.",30:"v-else/v-else-if has no adjacent v-if or v-else-if.",31:"v-for is missing expression.",32:"v-for has invalid expression.",33:"<template v-for> key should be placed on the <template> tag.",34:"v-bind is missing expression.",52:"v-bind with same-name shorthand only allows static argument.",35:"v-on is missing expression.",36:"Unexpected custom directive on <slot> outlet.",37:"Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.",38:"Duplicate slot names found. ",39:"Extraneous children found when component already has explicitly named default slot. These children will be ignored.",40:"v-slot can only be used on components or <template> tags.",41:"v-model is missing expression.",42:"v-model value must be a valid JavaScript member expression.",43:"v-model cannot be used on v-for or v-slot scope variables because they are not writable.",44:"v-model cannot be used on a prop, because local prop bindings are not writable.\nUse a v-bind binding combined with a v-on listener that emits update:x event instead.",45:"Error parsing JavaScript expression: ",46:"<KeepAlive> expects exactly one child component.",51:"@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.",47:'"prefixIdentifiers" option is not supported in this build of compiler.',48:"ES module mode is not supported in this build of compiler.",49:'"cacheHandlers" option is only supported when the "prefixIdentifiers" option is enabled.',50:'"scopeId" option is only supported in module mode.',53:""};function nt(e,t,n=!1,s=[],i=Object.create(null)){}function st(e,t,n){return!1}function it(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){const n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1}function ot(e){let t=e.length;for(;t--;){const n=e[t];if("NewExpression"===n.type)return!0;if("MemberExpression"!==n.type)break}return!1}function rt(e,t){for(const n of e.params)for(const e of ct(n))t(e)}function at(e,t){for(const n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(const e of n.declarations)for(const n of ct(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}else if("ForOfStatement"===n.type||"ForInStatement"===n.type||"ForStatement"===n.type){const e="ForStatement"===n.type?n.init:n.left;if(e&&"VariableDeclaration"===e.type)for(const n of e.declarations)for(const e of ct(n.id))t(e)}}function ct(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(const s of e.properties)ct("RestElement"===s.type?s.argument:s.value,t);break;case"ArrayPattern":e.elements.forEach((e=>{e&&ct(e,t)}));break;case"RestElement":ct(e.argument,t);break;case"AssignmentPattern":ct(e.left,t)}return t}const lt=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),ht=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed,dt=(e,t)=>ht(t)&&t.key===e,pt=["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"];function ut(e){return pt.includes(e.type)?ut(e.expression):e}const ft=e=>4===e.type&&e.isStatic;function mt(e){switch(e){case"Teleport":case"teleport":return b;case"Suspense":case"suspense":return v;case"KeepAlive":case"keep-alive":return C;case"BaseTransition":case"base-transition":return x}}const _t=/^\d|[^\$\w]/,Et=e=>!_t.test(e),St=/[A-Za-z_$\xA0-\uFFFF]/,gt=/[\.\?\w$\xA0-\uFFFF]/,Tt=/\s+[.[]\s*|\s*[.[]\s+/g,Nt=e=>{e=e.trim().replace(Tt,(e=>e.trim()));let t=0,n=[],s=0,i=0,o=null;for(let r=0;r<e.length;r++){const a=e.charAt(r);switch(t){case 0:if("["===a)n.push(t),t=1,s++;else if("("===a)n.push(t),t=2,i++;else if(!(0===r?St:gt).test(a))return!1;break;case 1:"'"===a||'"'===a||"`"===a?(n.push(t),t=3,o=a):"["===a?s++:"]"===a&&(--s||(t=n.pop()));break;case 2:if("'"===a||'"'===a||"`"===a)n.push(t),t=3,o=a;else if("("===a)i++;else if(")"===a){if(r===e.length-1)return!1;--i||(t=n.pop())}break;case 3:a===o&&(t=n.pop(),o=null)}}return!s&&!i},It=n,yt=Nt;function Ot(e,t,n=t.length){return At({offset:e.offset,line:e.line,column:e.column},t,n)}function At(e,t,n=t.length){let s=0,i=-1;for(let o=0;o<n;o++)10===t.charCodeAt(o)&&(s++,i=o);return e.offset+=n,e.line+=s,e.column=-1===i?e.column+n:n-i,e}function bt(e,t){if(!e)throw new Error(t||"unexpected compiler condition")}function vt(e,t,n=!1){for(let s=0;s<e.props.length;s++){const i=e.props[s];if(7===i.type&&(n||i.exp)&&(a(t)?i.name===t:t.test(i.name)))return i}}function Ct(e,t,n=!1,s=!1){for(let i=0;i<e.props.length;i++){const o=e.props[i];if(6===o.type){if(n)continue;if(o.name===t&&(o.value||s))return o}else if("bind"===o.name&&(o.exp||s)&&xt(o.arg,t))return o}}function xt(e,t){return!(!e||!ft(e)||e.content!==t)}function Rt(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function Lt(e){return 5===e.type||2===e.type}function Mt(e){return 7===e.type&&"slot"===e.name}function Pt(e){return 1===e.type&&3===e.tagType}function Dt(e){return 1===e.type&&2===e.tagType}const kt=new Set([Y,Q]);function Vt(e,t=[]){if(e&&!a(e)&&14===e.type){const n=e.callee;if(!a(n)&&kt.has(n))return Vt(e.arguments[0],t.concat(e))}return[e,t]}function Xt(e,t,n){let s,i,o=13===e.type?e.props:e.arguments[2],r=[];if(o&&!a(o)&&14===o.type){const e=Vt(o);o=e[0],r=e[1],i=r[r.length-1]}if(null==o||a(o))s=Te([t]);else if(14===o.type){const e=o.arguments[0];a(e)||15!==e.type?o.callee===z?s=Ae(n.helper(j),[Te([t]),o]):o.arguments.unshift(Te([t])):wt(t,e)||e.properties.unshift(t),!s&&(s=o)}else 15===o.type?(wt(t,o)||o.properties.unshift(t),s=o):(s=Ae(n.helper(j),[Te([t]),o]),i&&i.callee===Q&&(i=r[r.length-2]));13===e.type?i?i.arguments[0]=s:e.props=s:i?i.arguments[0]=s:e.arguments[2]=s}function wt(e,t){let n=!1;if(4===e.key.type){const s=e.key.content;n=t.properties.some((e=>4===e.key.type&&e.key.content===s))}return n}function Ut(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}function Ft(e,t){if(!e||0===Object.keys(t).length)return!1;switch(e.type){case 1:for(let n=0;n<e.props.length;n++){const s=e.props[n];if(7===s.type&&(Ft(s.arg,t)||Ft(s.exp,t)))return!0}return e.children.some((e=>Ft(e,t)));case 11:return!!Ft(e.source,t)||e.children.some((e=>Ft(e,t)));case 9:return e.branches.some((e=>Ft(e,t)));case 10:return!!Ft(e.condition,t)||e.children.some((e=>Ft(e,t)));case 4:return!e.isStatic&&Et(e.content)&&!!t[e.content];case 8:return e.children.some((e=>l(e)&&Ft(e,t)));case 5:case 12:return Ft(e.content,t);default:return!1}}function Bt(e){return 14===e.type&&e.callee===ce?e.arguments[1].returns:e}const $t=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Ht={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:s,isPreTag:s,isCustomElement:s,onError:Qe,onWarn:ze,comments:!1,prefixIdentifiers:!1};let Gt=Ht,qt=null,Jt="",jt=null,Wt=null,Kt="",Yt=-1,Qt=-1,zt=0,Zt=!1,en=null;const tn=[],nn=new class{constructor(e,t){this.stack=e,this.cbs=t,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=we,this.delimiterClose=Ue,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return 2===this.mode&&0===this.stack.length}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=we,this.delimiterClose=Ue}getPos(e){let t=1,n=e+1;for(let s=this.newlines.length-1;s>=0;s--){const i=this.newlines[s];if(e>i){t=s+2,n=e-i;break}}return{column:n,line:t,offset:e}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(e){60===e?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e))}stateInterpolationOpen(e){if(e===this.delimiterOpen[this.delimiterIndex])if(this.delimiterIndex===this.delimiterOpen.length-1){const e=this.index+1-this.delimiterOpen.length;e>this.sectionStart&&this.cbs.ontext(this.sectionStart,e),this.state=3,this.sectionStart=e}else this.delimiterIndex++;else this.inRCDATA?(this.state=32,this.stateInRCDATA(e)):(this.state=1,this.stateText(e))}stateInterpolation(e){e===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(e))}stateInterpolationClose(e){e===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.state=this.inRCDATA?32:1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(e))}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?$e(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.inRCDATA=!1;this.sequenceIndex=0,this.state=6,this.stateInTagName(e)}stateInRCDATA(e){if(this.sequenceIndex===this.currentSequence.length){if(62===e||Be(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.sectionStart=t+2,this.stateInClosingTagName(e),void(this.inRCDATA=!1)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===Ge.TitleEnd||this.currentSequence===Ge.TextareaEnd&&!this.inSFCRoot?e===this.delimiterOpen[0]&&(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e)):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=Number(60===e)}stateCDATASequence(e){e===Ge.Cdata[this.sequenceIndex]?++this.sequenceIndex===Ge.Cdata.length&&(this.state=28,this.currentSequence=Ge.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length;){const t=this.buffer.charCodeAt(this.index);if(10===t&&this.newlines.push(this.index),t===e)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===Ge.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(e,t){this.enterRCDATA(e,t),this.state=31}enterRCDATA(e,t){this.inRCDATA=!0,this.currentSequence=e,this.sequenceIndex=t}stateBeforeTagName(e){33===e?(this.state=22,this.sectionStart=this.index+1):63===e?(this.state=24,this.sectionStart=this.index+1):Fe(e)?(this.sectionStart=this.index,this.state=0===this.mode?6:this.inSFCRoot?34:this.inXML?6:116===e?30:115===e?29:6):47===e?this.state=8:(this.state=1,this.stateText(e))}stateInTagName(e){$e(e)&&this.handleTagName(e)}stateInSFCRootTagName(e){if($e(e)){const t=this.buffer.slice(this.sectionStart,this.index);"template"!==t&&this.enterRCDATA(He("</"+t),0),this.handleTagName(e)}}handleTagName(e){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)}stateBeforeClosingTagName(e){Be(e)||(62===e?(this.state=1,this.sectionStart=this.index+1):(this.state=Fe(e)?9:27,this.sectionStart=this.index))}stateInClosingTagName(e){(62===e||Be(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){62===e&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(e){62===e?(this.cbs.onopentagend(this.index),this.state=this.inRCDATA?32:1,this.sectionStart=this.index+1):47===e?this.state=7:60===e&&47===this.peek()?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):Be(e)||this.handleAttrStart(e)}handleAttrStart(e){118===e&&45===this.peek()?(this.state=13,this.sectionStart=this.index):46===e||58===e||64===e||35===e?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(e){62===e?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):Be(e)||(this.state=11,this.stateBeforeAttrName(e))}stateInAttrName(e){(61===e||$e(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(e))}stateInDirName(e){61===e||$e(e)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(e)):58===e?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):46===e&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(e){61===e||$e(e)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(e)):91===e?this.state=15:46===e&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(e){93===e?this.state=14:(61===e||$e(e))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(e))}stateInDirModifier(e){61===e||$e(e)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(e)):46===e&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(e){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(e)}stateAfterAttrName(e){61===e?this.state=18:47===e||62===e?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)):Be(e)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(e))}stateBeforeAttrValue(e){34===e?(this.state=19,this.sectionStart=this.index+1):39===e?(this.state=20,this.sectionStart=this.index+1):Be(e)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(e))}handleInAttrValue(e,t){(e===t||this.fastForwardTo(t))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(34===t?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(e){this.handleInAttrValue(e,34)}stateInAttrValueSingleQuotes(e){this.handleInAttrValue(e,39)}stateInAttrValueNoQuotes(e){Be(e)||62===e?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(e)):39!==e&&60!==e&&61!==e&&96!==e||this.cbs.onerr(18,this.index)}stateBeforeDeclaration(e){91===e?(this.state=26,this.sequenceIndex=0):this.state=45===e?25:23}stateInDeclaration(e){(62===e||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(62===e||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(e){45===e?(this.state=28,this.currentSequence=Ge.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(e){(62===e||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){e===Ge.ScriptEnd[3]?this.startSpecial(Ge.ScriptEnd,4):e===Ge.StyleEnd[3]?this.startSpecial(Ge.StyleEnd,4):(this.state=6,this.stateInTagName(e))}stateBeforeSpecialT(e){e===Ge.TitleEnd[3]?this.startSpecial(Ge.TitleEnd,4):e===Ge.TextareaEnd[3]?this.startSpecial(Ge.TextareaEnd,4):(this.state=6,this.stateInTagName(e))}startEntity(){}stateInEntity(){}parse(e){for(this.buffer=e;this.index<this.buffer.length;){const e=this.buffer.charCodeAt(this.index);switch(10===e&&this.newlines.push(this.index),this.state){case 1:this.stateText(e);break;case 2:this.stateInterpolationOpen(e);break;case 3:this.stateInterpolation(e);break;case 4:this.stateInterpolationClose(e);break;case 31:this.stateSpecialStartSequence(e);break;case 32:this.stateInRCDATA(e);break;case 26:this.stateCDATASequence(e);break;case 19:this.stateInAttrValueDoubleQuotes(e);break;case 12:this.stateInAttrName(e);break;case 13:this.stateInDirName(e);break;case 14:this.stateInDirArg(e);break;case 15:this.stateInDynamicDirArg(e);break;case 16:this.stateInDirModifier(e);break;case 28:this.stateInCommentLike(e);break;case 27:this.stateInSpecialComment(e);break;case 11:this.stateBeforeAttrName(e);break;case 6:this.stateInTagName(e);break;case 34:this.stateInSFCRootTagName(e);break;case 9:this.stateInClosingTagName(e);break;case 5:this.stateBeforeTagName(e);break;case 17:this.stateAfterAttrName(e);break;case 20:this.stateInAttrValueSingleQuotes(e);break;case 18:this.stateBeforeAttrValue(e);break;case 8:this.stateBeforeClosingTagName(e);break;case 10:this.stateAfterClosingTagName(e);break;case 29:this.stateBeforeSpecialS(e);break;case 30:this.stateBeforeSpecialT(e);break;case 21:this.stateInAttrValueNoQuotes(e);break;case 7:this.stateInSelfClosingTag(e);break;case 23:this.stateInDeclaration(e);break;case 22:this.stateBeforeDeclaration(e);break;case 25:this.stateBeforeComment(e);break;case 24:this.stateInProcessingInstruction(e);break;case 33:this.stateInEntity()}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(1===this.state||32===this.state&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):19!==this.state&&20!==this.state&&21!==this.state||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length;this.sectionStart>=e||(28===this.state?this.currentSequence===Ge.CdataEnd?this.cbs.oncdata(this.sectionStart,e):this.cbs.oncomment(this.sectionStart,e):6===this.state||11===this.state||18===this.state||17===this.state||12===this.state||13===this.state||14===this.state||15===this.state||16===this.state||20===this.state||19===this.state||21===this.state||9===this.state||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){}}(tn,{onerr:yn,ontext(e,t){cn(rn(e,t),e,t)},ontextentity(e,t,n){cn(e,t,n)},oninterpolation(e,t){if(Zt)return cn(rn(e,t),e,t);let n=e+nn.delimiterOpen.length,s=t-nn.delimiterClose.length;for(;Be(Jt.charCodeAt(n));)n++;for(;Be(Jt.charCodeAt(s-1));)s--;let i=rn(n,s);i.includes("&")&&(i=Gt.decodeEntities(i,!1)),Sn({type:5,content:In(i,!1,gn(n,s)),loc:gn(e,t)})},onopentagname(e,t){const n=rn(e,t);jt={type:1,tag:n,ns:Gt.getNamespace(n,tn[0],Gt.ns),tagType:0,props:[],children:[],loc:gn(e-1,t),codegenNode:void 0}},onopentagend(e){an(e)},onclosetag(e,t){const n=rn(e,t);if(!Gt.isVoidTag(n)){let s=!1;for(let e=0;e<tn.length;e++){if(tn[e].tag.toLowerCase()===n.toLowerCase()){s=!0;for(let n=0;n<=e;n++){ln(tn.shift(),t,n<e)}break}}s||hn(e,60)}},onselfclosingtag(e){var t;const n=jt.tag;jt.isSelfClosing=!0,an(e),(null==(t=tn[0])?void 0:t.tag)===n&&ln(tn.shift(),e)},onattribname(e,t){Wt={type:6,name:rn(e,t),nameLoc:gn(e,t),value:void 0,loc:gn(e)}},ondirname(e,t){const n=rn(e,t),s="."===n||":"===n?"bind":"@"===n?"on":"#"===n?"slot":n.slice(2);if(Zt||""===s)Wt={type:6,name:n,nameLoc:gn(e,t),value:void 0,loc:gn(e)};else if(Wt={type:7,name:s,rawName:n,exp:void 0,arg:void 0,modifiers:"."===n?["prop"]:[],loc:gn(e)},"pre"===s){Zt=nn.inVPre=!0,en=jt;const e=jt.props;for(let t=0;t<e.length;t++)7===e[t].type&&(e[t]=Nn(e[t]))}},ondirarg(e,t){if(e===t)return;const n=rn(e,t);if(Zt)Wt.name+=n,Tn(Wt.nameLoc,t);else{const s="["!==n[0];Wt.arg=In(s?n:n.slice(1,-1),s,gn(e,t),s?3:0)}},ondirmodifier(e,t){const n=rn(e,t);if(Zt)Wt.name+="."+n,Tn(Wt.nameLoc,t);else if("slot"===Wt.name){const e=Wt.arg;e&&(e.content+="."+n,Tn(e.loc,t))}else Wt.modifiers.push(n)},onattribdata(e,t){Kt+=rn(e,t),Yt<0&&(Yt=e),Qt=t},onattribentity(e,t,n){Kt+=e,Yt<0&&(Yt=t),Qt=n},onattribnameend(e){const t=rn(Wt.loc.start.offset,e);7===Wt.type&&(Wt.rawName=t),jt.props.some((e=>(7===e.type?e.rawName:e.name)===t))},onattribend(e,t){if(jt&&Wt){if(Tn(Wt.loc,t),0!==e)if(Kt.includes("&")&&(Kt=Gt.decodeEntities(Kt,!0)),6===Wt.type)"class"===Wt.name&&(Kt=En(Kt).trim()),Wt.value={type:2,content:Kt,loc:1===e?gn(Yt,Qt):gn(Yt-1,Qt+1)},nn.inSFCRoot&&"template"===jt.tag&&"lang"===Wt.name&&Kt&&"html"!==Kt&&nn.enterRCDATA(He("</template"),0);else{let e=0;Wt.exp=In(Kt,!1,gn(Yt,Qt),0,e),"for"===Wt.name&&(Wt.forParseResult=function(e){const t=e.loc,n=e.content,s=n.match($t);if(!s)return;const[,i,o]=s,r=(e,n,s=!1)=>{const i=t.start.offset+n;return In(e,!1,gn(i,i+e.length),0,s?1:0)},a={source:r(o.trim(),n.indexOf(o,i.length)),value:void 0,key:void 0,index:void 0,finalized:!1};let c=i.trim().replace(on,"").trim();const l=i.indexOf(c),h=c.match(sn);if(h){c=c.replace(sn,"").trim();const e=h[1].trim();let t;if(e&&(t=n.indexOf(e,l+c.length),a.key=r(e,t,!0)),h[2]){const s=h[2].trim();s&&(a.index=r(s,n.indexOf(s,a.key?t+e.length:l+c.length),!0))}}c&&(a.value=r(c,l,!0));return a}(Wt.exp));let t=-1;"bind"===Wt.name&&(t=Wt.modifiers.indexOf("sync"))>-1&&Ke("COMPILER_V_BIND_SYNC",Gt,0)&&(Wt.name="model",Wt.modifiers.splice(t,1))}7===Wt.type&&"pre"===Wt.name||jt.props.push(Wt)}Kt="",Yt=Qt=-1},oncomment(e,t){Gt.comments&&Sn({type:3,content:rn(e,t),loc:gn(e-4,t+3)})},onend(){const e=Jt.length;for(let t=0;t<tn.length;t++)ln(tn[t],e-1)},oncdata(e,t){0!==tn[0].ns&&cn(rn(e,t),e,t)},onprocessinginstruction(e){0===(tn[0]?tn[0].ns:Gt.ns)&&yn(21,e-1)}}),sn=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,on=/^\(|\)$/g;function rn(e,t){return Jt.slice(e,t)}function an(e){nn.inSFCRoot&&(jt.innerLoc=gn(e+1,e+1)),Sn(jt);const{tag:t,ns:n}=jt;0===n&&Gt.isPreTag(t)&&zt++,Gt.isVoidTag(t)?ln(jt,e):(tn.unshift(jt),1!==n&&2!==n||(nn.inXML=!0)),jt=null}function cn(e,t,n){var s;{const t=null==(s=tn[0])?void 0:s.tag;"script"!==t&&"style"!==t&&e.includes("&")&&(e=Gt.decodeEntities(e,!1))}const i=tn[0]||qt,o=i.children[i.children.length-1];2===(null==o?void 0:o.type)?(o.content+=e,Tn(o.loc,n)):i.children.push({type:2,content:e,loc:gn(t,n)})}function ln(e,t,n=!1){Tn(e.loc,n?hn(t,60):t+1),nn.inSFCRoot&&(e.innerLoc.end=o({},e.children.length?e.children[e.children.length-1].loc.end:e.innerLoc.start),e.innerLoc.source=rn(e.innerLoc.start.offset,e.innerLoc.end.offset));const{tag:s,ns:i}=e;Zt||("slot"===s?e.tagType=2:pn(e)?e.tagType=3:function({tag:e,props:t}){var n;if(Gt.isCustomElement(e))return!1;if("component"===e||(s=e.charCodeAt(0),s>64&&s<91)||mt(e)||(null==(n=Gt.isBuiltInComponent)?void 0:n.call(Gt,e))||Gt.isNativeTag&&!Gt.isNativeTag(e))return!0;var s;for(let i=0;i<t.length;i++){const e=t[i];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(Ke("COMPILER_IS_ON_ELEMENT",Gt))return!0}}else if("bind"===e.name&&xt(e.arg,"is")&&Ke("COMPILER_IS_ON_ELEMENT",Gt))return!0}return!1}(e)&&(e.tagType=1)),nn.inRCDATA||(e.children=fn(e.children,e.tag)),0===i&&Gt.isPreTag(s)&&zt--,en===e&&(Zt=nn.inVPre=!1,en=null),nn.inXML&&0===(tn[0]?tn[0].ns:Gt.ns)&&(nn.inXML=!1);{const t=e.props;if(!nn.inSFCRoot&&We("COMPILER_NATIVE_TEMPLATE",Gt)&&"template"===e.tag&&!pn(e)){const t=tn[0]||qt,n=t.children.indexOf(e);t.children.splice(n,1,...e.children)}const n=t.find((e=>6===e.type&&"inline-template"===e.name));n&&Ke("COMPILER_INLINE_TEMPLATE",Gt)&&e.children.length&&(n.value={type:2,content:rn(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:n.loc})}}function hn(e,t){let n=e;for(;Jt.charCodeAt(n)!==t&&n>=0;)n--;return n}const dn=new Set(["if","else","else-if","for","slot"]);function pn({tag:e,props:t}){if("template"===e)for(let n=0;n<t.length;n++)if(7===t[n].type&&dn.has(t[n].name))return!0;return!1}const un=/\r\n/g;function fn(e,t){var n,s;const i="preserve"!==Gt.whitespace;let o=!1;for(let r=0;r<e.length;r++){const t=e[r];if(2===t.type)if(zt)t.content=t.content.replace(un,"\n");else if(mn(t.content)){const a=null==(n=e[r-1])?void 0:n.type,c=null==(s=e[r+1])?void 0:s.type;!a||!c||i&&(3===a&&(3===c||1===c)||1===a&&(3===c||1===c&&_n(t.content)))?(o=!0,e[r]=null):t.content=" "}else i&&(t.content=En(t.content))}if(zt&&t&&Gt.isPreTag(t)){const t=e[0];t&&2===t.type&&(t.content=t.content.replace(/^\r?\n/,""))}return o?e.filter(Boolean):e}function mn(e){for(let t=0;t<e.length;t++)if(!Be(e.charCodeAt(t)))return!1;return!0}function _n(e){for(let t=0;t<e.length;t++){const n=e.charCodeAt(t);if(10===n||13===n)return!0}return!1}function En(e){let t="",n=!1;for(let s=0;s<e.length;s++)Be(e.charCodeAt(s))?n||(t+=" ",n=!0):(t+=e[s],n=!1);return t}function Sn(e){(tn[0]||qt).children.push(e)}function gn(e,t){return{start:nn.getPos(e),end:null==t?t:nn.getPos(t),source:null==t?t:rn(e,t)}}function Tn(e,t){e.end=nn.getPos(t),e.source=rn(e.start.offset,t)}function Nn(e){const t={type:6,name:e.rawName,nameLoc:gn(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){const n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}function In(e,t=!1,n,s=0,i=0){return Ie(e,t,n,s)}function yn(e,t,n){Gt.onError(Ze(e,gn(t,t)))}function On(e,t){if(nn.reset(),jt=null,Wt=null,Kt="",Yt=-1,Qt=-1,tn.length=0,Jt=e,Gt=o({},Ht),t){let e;for(e in t)null!=t[e]&&(Gt[e]=t[e])}nn.mode="html"===Gt.parseMode?1:"sfc"===Gt.parseMode?2:0,nn.inXML=1===Gt.ns||2===Gt.ns;const n=null==t?void 0:t.delimiters;n&&(nn.delimiterOpen=He(n[0]),nn.delimiterClose=He(n[1]));const s=qt=Ee([],e);return nn.parse(Jt),s.loc=gn(0,e.length),s.children=fn(s.children),qt=null,s}function An(e,t){vn(e,t,bn(e,e.children[0]))}function bn(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!Dt(t)}function vn(e,t,n=!1){const{children:s}=e,i=s.length;let o=0;for(let r=0;r<s.length;r++){const e=s[r];if(1===e.type&&0===e.tagType){const s=n?0:Cn(e,t);if(s>0){if(s>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),o++;continue}}else{const n=e.codegenNode;if(13===n.type){const s=Pn(n);if((!s||512===s||1===s)&&Ln(e,t)>=2){const s=Mn(e);s&&(n.props=t.hoist(s))}n.dynamicProps&&(n.dynamicProps=t.hoist(n.dynamicProps))}}}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,vn(e,t),n&&t.scopes.vSlot--}else if(11===e.type)vn(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)vn(e.branches[n],t,1===e.branches[n].children.length)}if(o&&t.transformHoist&&t.transformHoist(s,t,e),o&&o===i&&1===e.type&&0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&r(e.codegenNode.children)){const n=t.hoist(ge(e.codegenNode.children));t.hmr&&(n.content=`[...${n.content}]`),e.codegenNode.children=n}}function Cn(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const s=n.get(e);if(void 0!==s)return s;const i=e.codegenNode;if(13!==i.type)return 0;if(i.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag)return 0;if(Pn(i))return n.set(e,0),0;{let s=3;const o=Ln(e,t);if(0===o)return n.set(e,0),0;o<s&&(s=o);for(let i=0;i<e.children.length;i++){const o=Cn(e.children[i],t);if(0===o)return n.set(e,0),0;o<s&&(s=o)}if(s>1)for(let i=0;i<e.props.length;i++){const o=e.props[i];if(7===o.type&&"bind"===o.name&&o.exp){const i=Cn(o.exp,t);if(0===i)return n.set(e,0),0;i<s&&(s=i)}}if(i.isBlock){for(let t=0;t<e.props.length;t++){if(7===e.props[t].type)return n.set(e,0),0}t.removeHelper(R),t.removeHelper(Ve(t.inSSR,i.isComponent)),i.isBlock=!1,t.helper(ke(t.inSSR,i.isComponent))}return n.set(e,s),s}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return Cn(e.content,t);case 4:return e.constType;case 8:let o=3;for(let n=0;n<e.children.length;n++){const s=e.children[n];if(a(s)||c(s))continue;const i=Cn(s,t);if(0===i)return 0;i<o&&(o=i)}return o}}const xn=new Set([W,K,Y,Q]);function Rn(e,t){if(14===e.type&&!a(e.callee)&&xn.has(e.callee)){const n=e.arguments[0];if(4===n.type)return Cn(n,t);if(14===n.type)return Rn(n,t)}return 0}function Ln(e,t){let n=3;const s=Mn(e);if(s&&15===s.type){const{properties:e}=s;for(let s=0;s<e.length;s++){const{key:i,value:o}=e[s],r=Cn(i,t);if(0===r)return r;let a;if(r<n&&(n=r),a=4===o.type?Cn(o,t):14===o.type?Rn(o,t):0,0===a)return a;a<n&&(n=a)}}return n}function Mn(e){const t=e.codegenNode;if(13===t.type)return t.props}function Pn(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function Dn(e,{filename:s="",prefixIdentifiers:i=!1,hoistStatic:o=!1,hmr:r=!1,cacheHandlers:c=!1,nodeTransforms:l=[],directiveTransforms:h={},transformHoist:d=null,isBuiltInComponent:p=n,isCustomElement:u=n,expressionPlugins:_=[],scopeId:E=null,slotted:S=!0,ssr:g=!1,inSSR:T=!1,ssrCssVars:N="",bindingMetadata:I=t,inline:y=!1,isTS:O=!1,onError:A=Qe,onWarn:b=ze,compatConfig:v}){const C=s.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),x={filename:s,selfName:C&&m(f(C[1])),prefixIdentifiers:i,hoistStatic:o,hmr:r,cacheHandlers:c,nodeTransforms:l,directiveTransforms:h,transformHoist:d,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:_,scopeId:E,slotted:S,ssr:g,inSSR:T,ssrCssVars:N,bindingMetadata:I,inline:y,isTS:O,onError:A,onWarn:b,compatConfig:v,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new WeakMap,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=x.helpers.get(e)||0;return x.helpers.set(e,t+1),e},removeHelper(e){const t=x.helpers.get(e);if(t){const n=t-1;n?x.helpers.set(e,n):x.helpers.delete(e)}},helperString:e=>`_${he[x.helper(e)]}`,replaceNode(e){x.parent.children[x.childIndex]=x.currentNode=e},removeNode(e){const t=e?x.parent.children.indexOf(e):x.currentNode?x.childIndex:-1;e&&e!==x.currentNode?x.childIndex>t&&(x.childIndex--,x.onNodeRemoved()):(x.currentNode=null,x.onNodeRemoved()),x.parent.children.splice(t,1)},onNodeRemoved:n,addIdentifiers(e){},removeIdentifiers(e){},hoist(e){a(e)&&(e=Ie(e)),x.hoists.push(e);const t=Ie(`_hoisted_${x.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>Ce(x.cached++,e,t)};return x.filters=new Set,x}function kn(e,t){const n=Dn(e,t);Vn(e,n),t.hoistStatic&&An(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:s}=e;if(1===s.length){const n=s[0];if(bn(e,n)&&n.codegenNode){const s=n.codegenNode;13===s.type&&Xe(s,t),e.codegenNode=s}else e.codegenNode=n}else if(s.length>1){let s=64;e.codegenNode=Se(t,n(A),void 0,e.children,s+"",void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function Vn(e,t){t.currentNode=e;const{nodeTransforms:n}=t,s=[];for(let o=0;o<n.length;o++){const i=n[o](e,t);if(i&&(r(i)?s.push(...i):s.push(i)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(k);break;case 5:t.ssr||t.helper(J);break;case 9:for(let n=0;n<e.branches.length;n++)Vn(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const s=()=>{n--};for(;n<e.children.length;n++){const i=e.children[n];a(i)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=s,Vn(i,t))}}(e,t)}t.currentNode=e;let i=s.length;for(;i--;)s[i]()}function Xn(e,t){const n=a(e)?t=>t===e:t=>e.test(t);return(e,s)=>{if(1===e.type){const{props:i}=e;if(3===e.tagType&&i.some(Mt))return;const o=[];for(let r=0;r<i.length;r++){const a=i[r];if(7===a.type&&n(a.name)){i.splice(r,1),r--;const n=t(e,a,s);n&&o.push(n)}}return o}}}const wn="/*#__PURE__*/",Un=e=>`${he[e]}: _${he[e]}`;function Fn(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:s=!1,filename:i="template.vue.html",scopeId:o=null,optimizeImports:r=!1,runtimeGlobalName:a="Vue",runtimeModuleName:c="vue",ssrRuntimeModuleName:l="vue/server-renderer",ssr:h=!1,isTS:d=!1,inSSR:p=!1}){const u={mode:t,prefixIdentifiers:n,sourceMap:s,filename:i,scopeId:o,optimizeImports:r,runtimeGlobalName:a,runtimeModuleName:c,ssrRuntimeModuleName:l,ssr:h,isTS:d,inSSR:p,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${he[e]}`,push(e,t=-2,n){u.code+=e},indent(){f(++u.indentLevel)},deindent(e=!1){e?--u.indentLevel:f(--u.indentLevel)},newline(){f(u.indentLevel)}};function f(e){u.push("\n"+"  ".repeat(e),0)}return u}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:s,push:i,prefixIdentifiers:o,indent:r,deindent:a,newline:c,ssr:l}=n,h=Array.from(e.helpers),d=h.length>0,p=!o&&"module"!==s;!function(e,t){const{push:n,newline:s,runtimeGlobalName:i}=t,o=i,r=Array.from(e.helpers);if(r.length>0&&(n(`const _Vue = ${o}\n`,-1),e.hoists.length)){n(`const { ${[P,D,k,V,X].filter((e=>r.includes(e))).map(Un).join(", ")} } = _Vue\n`,-1)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:s}=t;s();for(let i=0;i<e.length;i++){const o=e[i];o&&(n(`const _hoisted_${i+1} = `),Gn(o,t),s())}t.pure=!1})(e.hoists,t),s(),n("return ")}(e,n);if(i(`function ${l?"ssrRender":"render"}(${(l?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),r(),p&&(i("with (_ctx) {"),r(),d&&(i(`const { ${h.map(Un).join(", ")} } = _Vue\n`,-1),c())),e.components.length&&(Bn(e.components,"component",n),(e.directives.length||e.temps>0)&&c()),e.directives.length&&(Bn(e.directives,"directive",n),e.temps>0&&c()),e.filters&&e.filters.length&&(c(),Bn(e.filters,"filter",n),c()),e.temps>0){i("let ");for(let t=0;t<e.temps;t++)i(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(i("\n",0),c()),l||i("return "),e.codegenNode?Gn(e.codegenNode,n):i("null"),p&&(a(),i("}")),a(),i("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function Bn(e,t,{helper:n,push:s,newline:i,isTS:o}){const r=n("filter"===t?B:"component"===t?w:F);for(let a=0;a<e.length;a++){let n=e[a];const c=n.endsWith("__self");c&&(n=n.slice(0,-6)),s(`const ${Ut(n,t)} = ${r}(${JSON.stringify(n)}${c?", true":""})${o?"!":""}`),a<e.length-1&&i()}}function $n(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),Hn(e,t,n),n&&t.deindent(),t.push("]")}function Hn(e,t,n=!1,s=!0){const{push:i,newline:o}=t;for(let c=0;c<e.length;c++){const l=e[c];a(l)?i(l,-3):r(l)?$n(l,t):Gn(l,t),c<e.length-1&&(n?(s&&i(","),o()):s&&i(", "))}}function Gn(e,t){if(a(e))t.push(e,-3);else if(c(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:Gn(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),-3,e)}(e,t);break;case 4:qn(e,t);break;case 5:!function(e,t){const{push:n,helper:s,pure:i}=t;i&&n(wn);n(`${s(J)}(`),Gn(e.content,t),n(")")}(e,t);break;case 8:Jn(e,t);break;case 3:!function(e,t){const{push:n,helper:s,pure:i}=t;i&&n(wn);n(`${s(k)}(${JSON.stringify(e.content)})`,-3,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:s,pure:i}=t,{tag:o,props:r,children:a,patchFlag:c,dynamicProps:l,directives:h,isBlock:d,disableTracking:p,isComponent:u}=e;h&&n(s($)+"(");d&&n(`(${s(R)}(${p?"true":""}), `);i&&n(wn);const f=d?Ve(t.inSSR,u):ke(t.inSSR,u);n(s(f)+"(",-2,e),Hn(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([o,r,a,c,l]),t),n(")"),d&&n(")");h&&(n(", "),Gn(h,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:s,pure:i}=t,o=a(e.callee)?e.callee:s(e.callee);i&&n(wn);n(o+"(",-2,e),Hn(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:s,deindent:i,newline:o}=t,{properties:r}=e;if(!r.length)return void n("{}",-2,e);const a=r.length>1||!1;n(a?"{":"{ "),a&&s();for(let c=0;c<r.length;c++){const{key:e,value:s}=r[c];jn(e,t),n(": "),Gn(s,t),c<r.length-1&&(n(","),o())}a&&i(),n(a?"}":" }")}(e,t);break;case 17:!function(e,t){$n(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:s,deindent:i}=t,{params:o,returns:a,body:c,newline:l,isSlot:h}=e;h&&n(`_${he[oe]}(`);n("(",-2,e),r(o)?Hn(o,t):o&&Gn(o,t);n(") => "),(l||c)&&(n("{"),s());a?(l&&n("return "),r(a)?$n(a,t):Gn(a,t)):c&&Gn(c,t);(l||c)&&(i(),n("}"));h&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:s,alternate:i,newline:o}=e,{push:r,indent:a,deindent:c,newline:l}=t;if(4===n.type){const e=!Et(n.content);e&&r("("),qn(n,t),e&&r(")")}else r("("),Gn(n,t),r(")");o&&a(),t.indentLevel++,o||r(" "),r("? "),Gn(s,t),t.indentLevel--,o&&l(),o||r(" "),r(": ");const h=19===i.type;h||t.indentLevel++;Gn(i,t),h||t.indentLevel--;o&&c(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:s,indent:i,deindent:o,newline:r}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(i(),n(`${s(ne)}(-1),`),r());n(`_cache[${e.index}] = `),Gn(e.value,t),e.isVNode&&(n(","),r(),n(`${s(ne)}(1),`),r(),n(`_cache[${e.index}]`),o());n(")")}(e,t);break;case 21:Hn(e.body,t,!0,!1)}}function qn(e,t){const{content:n,isStatic:s}=e;t.push(s?JSON.stringify(n):n,-3,e)}function Jn(e,t){for(let n=0;n<e.children.length;n++){const s=e.children[n];a(s)?t.push(s,-3):Gn(s,t)}}function jn(e,t){const{push:n}=t;if(8===e.type)n("["),Jn(e,t),n("]");else if(e.isStatic){n(Et(e.content)?e.content:JSON.stringify(e.content),-2,e)}else n(`[${e.content}]`,-3,e)}const Wn=(e,t)=>{if(5===e.type)e.content=Kn(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){const s=e.props[n];if(7===s.type&&"for"!==s.name){const e=s.exp,n=s.arg;!e||4!==e.type||"on"===s.name&&n||(s.exp=Kn(e,t,"slot"===s.name)),n&&4===n.type&&!n.isStatic&&(s.arg=Kn(n,t))}}};function Kn(e,t,n=!1,s=!1,i=Object.create(t.identifiers)){return e}function Yn(e){return a(e)?e:4===e.type?e.content:e.children.map(Yn).join("")}const Qn=Xn(/^(if|else|else-if)$/,((e,t,n)=>zn(e,t,n,((e,t,s)=>{const i=n.parent.children;let o=i.indexOf(e),r=0;for(;o-- >=0;){const e=i[o];e&&9===e.type&&(r+=e.branches.length)}return()=>{if(s)e.codegenNode=es(t,r,n);else{const s=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);s.alternate=es(t,r+e.branches.length-1,n)}}}))));function zn(e,t,n,s){if(!("else"===t.name||t.exp&&t.exp.content.trim())){const s=t.exp?t.exp.loc:e.loc;n.onError(Ze(28,t.loc)),t.exp=Ie("true",!1,s)}if("if"===t.name){const i=Zn(e,t),o={type:9,loc:e.loc,branches:[i]};if(n.replaceNode(o),s)return s(o,i,!0)}else{const i=n.parent.children;let o=i.indexOf(e);for(;o-- >=-1;){const r=i[o];if(r&&3===r.type)n.removeNode(r);else{if(!r||2!==r.type||r.content.trim().length){if(r&&9===r.type){"else-if"===t.name&&void 0===r.branches[r.branches.length-1].condition&&n.onError(Ze(30,e.loc)),n.removeNode();const i=Zn(e,t);r.branches.push(i);const o=s&&s(r,i,!1);Vn(i,n),o&&o(),n.currentNode=null}else n.onError(Ze(30,e.loc));break}n.removeNode(r)}}}}function Zn(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!vt(e,"for")?e.children:[e],userKey:Ct(e,"key"),isTemplateIf:n}}function es(e,t,n){return e.condition?ve(e.condition,ts(e,t,n),Ae(n.helper(k),['""',"true"])):ts(e,t,n)}function ts(e,t,n){const{helper:s}=n,i=Ne("key",Ie(`${t}`,!1,_e,2)),{children:o}=e,r=o[0];if(1!==o.length||1!==r.type){if(1===o.length&&11===r.type){const e=r.codegenNode;return Xt(e,i,n),e}{let t=64;return Se(n,s(A),Te([i]),o,t+"",void 0,void 0,!0,!1,!1,e.loc)}}{const e=r.codegenNode,t=Bt(e);return 13===t.type&&Xe(t,n),Xt(t,i,n),e}}const ns=Xn("for",((e,t,n)=>{const{helper:s,removeHelper:i}=n;return ss(e,t,n,(t=>{const o=Ae(s(H),[t.source]),r=Pt(e),a=vt(e,"memo"),c=Ct(e,"key"),l=c&&(6===c.type?Ie(c.value.content,!0):c.exp),h=c?Ne("key",l):null,d=4===t.source.type&&t.source.constType>0,p=d?64:c?128:256;return t.codegenNode=Se(n,s(A),void 0,o,p+"",void 0,void 0,!0,!d,!1,e.loc),()=>{let c;const{children:p}=t,u=1!==p.length||1!==p[0].type,f=Dt(e)?e:r&&1===e.children.length&&Dt(e.children[0])?e.children[0]:null;if(f?(c=f.codegenNode,r&&h&&Xt(c,h,n)):u?c=Se(n,s(A),h?Te([h]):void 0,e.children,"64",void 0,void 0,!0,void 0,!1):(c=p[0].codegenNode,r&&h&&Xt(c,h,n),c.isBlock!==!d&&(c.isBlock?(i(R),i(Ve(n.inSSR,c.isComponent))):i(ke(n.inSSR,c.isComponent))),c.isBlock=!d,c.isBlock?(s(R),s(Ve(n.inSSR,c.isComponent))):s(ke(n.inSSR,c.isComponent))),a){const e=be(os(t.parseResult,[Ie("_cached")]));e.body=xe([Oe(["const _memo = (",a.exp,")"]),Oe(["if (_cached",...l?[" && _cached.key === ",l]:[],` && ${n.helperString(le)}(_cached, _memo)) return _cached`]),Oe(["const _item = ",c]),Ie("_item.memo = _memo"),Ie("return _item")]),o.arguments.push(e,Ie("_cache"),Ie(String(n.cached++)))}else o.arguments.push(be(os(t.parseResult),c,!0))}}))}));function ss(e,t,n,s){if(!t.exp)return void n.onError(Ze(31,t.loc));const i=t.forParseResult;if(!i)return void n.onError(Ze(32,t.loc));is(i);const{scopes:o}=n,{source:r,value:a,key:c,index:l}=i,h={type:11,loc:t.loc,source:r,valueAlias:a,keyAlias:c,objectIndexAlias:l,parseResult:i,children:Pt(e)?e.children:[e]};n.replaceNode(h),o.vFor++;const d=s&&s(h);return()=>{o.vFor--,d&&d()}}function is(e,t){e.finalized||(e.finalized=!0)}function os({value:e,key:t,index:n},s=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||Ie("_".repeat(t+1),!1)))}([e,t,n,...s])}const rs=Ie("undefined",!1),as=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=vt(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},cs=(e,t)=>{let n;if(Pt(e)&&e.props.some(Mt)&&(n=vt(e,"for"))){const e=n.forParseResult;if(e){is(e);const{value:n,key:s,index:i}=e,{addIdentifiers:o,removeIdentifiers:r}=t;return n&&o(n),s&&o(s),i&&o(i),()=>{n&&r(n),s&&r(s),i&&r(i)}}}},ls=(e,t,n,s)=>be(e,n,!1,!0,n.length?n[0].loc:s);function hs(e,t,n=ls){t.helper(oe);const{children:s,loc:i}=e,o=[],r=[];let a=t.scopes.vSlot>0||t.scopes.vFor>0;const c=vt(e,"slot",!0);if(c){const{arg:e,exp:t}=c;e&&!ft(e)&&(a=!0),o.push(Ne(e||Ie("default",!0),n(t,void 0,s,i)))}let l=!1,h=!1;const d=[],p=new Set;let u=0;for(let _=0;_<s.length;_++){const e=s[_];let i;if(!Pt(e)||!(i=vt(e,"slot",!0))){3!==e.type&&d.push(e);continue}if(c){t.onError(Ze(37,i.loc));break}l=!0;const{children:f,loc:m}=e,{arg:E=Ie("default",!0),exp:S,loc:g}=i;let T;ft(E)?T=E?E.content:"default":a=!0;const N=vt(e,"for"),I=n(S,N,f,m);let y,O;if(y=vt(e,"if"))a=!0,r.push(ve(y.exp,ds(E,I,u++),rs));else if(O=vt(e,/^else(-if)?$/,!0)){let e,n=_;for(;n--&&(e=s[n],3===e.type););if(e&&Pt(e)&&vt(e,"if")){s.splice(_,1),_--;let e=r[r.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=O.exp?ve(O.exp,ds(E,I,u++),rs):ds(E,I,u++)}else t.onError(Ze(30,O.loc))}else if(N){a=!0;const e=N.forParseResult;e?(is(e),r.push(Ae(t.helper(H),[e.source,be(os(e),ds(E,I),!0)]))):t.onError(Ze(32,N.loc))}else{if(T){if(p.has(T)){t.onError(Ze(38,g));continue}p.add(T),"default"===T&&(h=!0)}o.push(Ne(E,I))}}if(!c){const e=(e,s)=>{const o=n(e,void 0,s,i);return t.compatConfig&&(o.isNonScopedSlot=!0),Ne("default",o)};l?d.length&&d.some((e=>us(e)))&&(h?t.onError(Ze(39,d[0].loc)):o.push(e(void 0,d))):o.push(e(void 0,s))}const f=a?2:ps(e.children)?3:1;let m=Te(o.concat(Ne("_",Ie(f+"",!1))),i);return r.length&&(m=Ae(t.helper(q),[m,ge(r)])),{slots:m,hasDynamicSlots:a}}function ds(e,t,n){const s=[Ne("name",e),Ne("fn",t)];return null!=n&&s.push(Ne("key",Ie(String(n),!0))),Te(s)}function ps(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||ps(n.children))return!0;break;case 9:if(ps(n.branches))return!0;break;case 10:case 11:if(ps(n.children))return!0}}return!1}function us(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():us(e.content))}const fs=new WeakMap,ms=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:s}=e,i=1===e.tagType;let o=i?_s(e,t):`"${n}"`;const r=l(o)&&o.callee===U;let a,c,h,d,p,u,f=0,m=r||o===b||o===v||!i&&("svg"===n||"foreignObject"===n);if(s.length>0){const n=Es(e,t,void 0,i,r);a=n.props,f=n.patchFlag,p=n.dynamicPropNames;const s=n.directives;u=s&&s.length?ge(s.map((e=>Ts(e,t)))):void 0,n.shouldUseBlock&&(m=!0)}if(e.children.length>0){o===C&&(m=!0,f|=1024);if(i&&o!==b&&o!==C){const{slots:n,hasDynamicSlots:s}=hs(e,t);c=n,s&&(f|=1024)}else if(1===e.children.length&&o!==b){const n=e.children[0],s=n.type,i=5===s||8===s;i&&0===Cn(n,t)&&(f|=1),c=i||2===s?n:e.children}else c=e.children}0!==f&&(h=String(f),p&&p.length&&(d=function(e){let t="[";for(let n=0,s=e.length;n<s;n++)t+=JSON.stringify(e[n]),n<s-1&&(t+=", ");return t+"]"}(p))),e.codegenNode=Se(t,o,a,c,h,d,u,!!m,!1,i,e.loc)};function _s(e,t,n=!1){let{tag:s}=e;const i=Ns(s),o=Ct(e,"is");if(o)if(i||We("COMPILER_IS_ON_ELEMENT",t)){const e=6===o.type?o.value&&Ie(o.value.content,!0):o.exp;if(e)return Ae(t.helper(U),[e])}else 6===o.type&&o.value.content.startsWith("vue:")&&(s=o.value.content.slice(4));const r=mt(s)||t.isBuiltInComponent(s);return r?(n||t.helper(r),r):(t.helper(w),t.components.add(s),Ut(s,"component"))}function Es(e,t,n=e.props,s,o,r=!1){const{tag:a,loc:l,children:p}=e;let u=[];const f=[],m=[],_=p.length>0;let E=!1,S=0,g=!1,T=!1,N=!1,I=!1,y=!1,O=!1;const A=[],b=e=>{u.length&&(f.push(Te(Ss(u),l)),u=[]),e&&f.push(e)},v=({key:e,value:n})=>{if(ft(e)){const r=e.content,a=i(r);if(!a||s&&!o||"onclick"===r.toLowerCase()||"onUpdate:modelValue"===r||h(r)||(I=!0),a&&h(r)&&(O=!0),a&&14===n.type&&(n=n.arguments[0]),20===n.type||(4===n.type||8===n.type)&&Cn(n,t)>0)return;"ref"===r?g=!0:"class"===r?T=!0:"style"===r?N=!0:"key"===r||A.includes(r)||A.push(r),!s||"class"!==r&&"style"!==r||A.includes(r)||A.push(r)}else y=!0};for(let i=0;i<n.length;i++){const o=n[i];if(6===o.type){const{loc:e,name:n,nameLoc:s,value:i}=o;let r=!0;if("ref"===n&&(g=!0,t.scopes.vFor>0&&u.push(Ne(Ie("ref_for",!0),Ie("true")))),"is"===n&&(Ns(a)||i&&i.content.startsWith("vue:")||We("COMPILER_IS_ON_ELEMENT",t)))continue;u.push(Ne(Ie(n,!0,s),Ie(i?i.content:"",r,i?i.loc:e)))}else{const{name:n,arg:i,exp:h,loc:p,modifiers:g}=o,T="bind"===n,N="on"===n;if("slot"===n){s||t.onError(Ze(40,p));continue}if("once"===n||"memo"===n)continue;if("is"===n||T&&xt(i,"is")&&(Ns(a)||We("COMPILER_IS_ON_ELEMENT",t)))continue;if(N&&r)continue;if((T&&xt(i,"key")||N&&_&&xt(i,"vue:before-update"))&&(E=!0),T&&xt(i,"ref")&&t.scopes.vFor>0&&u.push(Ne(Ie("ref_for",!0),Ie("true"))),!i&&(T||N)){if(y=!0,h)if(T){if(b(),We("COMPILER_V_BIND_OBJECT_ORDER",t)){f.unshift(h);continue}f.push(h)}else b({type:14,loc:p,callee:t.helper(z),arguments:s?[h]:[h,"true"]});else t.onError(Ze(T?34:35,p));continue}T&&g.includes("prop")&&(S|=32);const I=t.directiveTransforms[n];if(I){const{props:n,needRuntime:s}=I(o,e,t);!r&&n.forEach(v),N&&i&&!ft(i)?b(Te(n,l)):u.push(...n),s&&(m.push(o),c(s)&&fs.set(o,s))}else d(n)||(m.push(o),_&&(E=!0))}}let C;if(f.length?(b(),C=f.length>1?Ae(t.helper(j),f,l):f[0]):u.length&&(C=Te(Ss(u),l)),y?S|=16:(T&&!s&&(S|=2),N&&!s&&(S|=4),A.length&&(S|=8),I&&(S|=32)),E||0!==S&&32!==S||!(g||O||m.length>0)||(S|=512),!t.inSSR&&C)switch(C.type){case 15:let e=-1,n=-1,s=!1;for(let t=0;t<C.properties.length;t++){const i=C.properties[t].key;ft(i)?"class"===i.content?e=t:"style"===i.content&&(n=t):i.isHandlerKey||(s=!0)}const i=C.properties[e],o=C.properties[n];s?C=Ae(t.helper(Y),[C]):(i&&!ft(i.value)&&(i.value=Ae(t.helper(W),[i.value])),o&&(N||4===o.value.type&&"["===o.value.content.trim()[0]||17===o.value.type)&&(o.value=Ae(t.helper(K),[o.value])));break;case 14:break;default:C=Ae(t.helper(Y),[Ae(t.helper(Q),[C])])}return{props:C,directives:m,patchFlag:S,dynamicPropNames:A,shouldUseBlock:E}}function Ss(e){const t=new Map,n=[];for(let s=0;s<e.length;s++){const o=e[s];if(8===o.key.type||!o.key.isStatic){n.push(o);continue}const r=o.key.content,a=t.get(r);a?("style"===r||"class"===r||i(r))&&gs(a,o):(t.set(r,o),n.push(o))}return n}function gs(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=ge([e.value,t.value],e.loc)}function Ts(e,t){const n=[],s=fs.get(e);s?n.push(t.helperString(s)):(t.helper(F),t.directives.add(e.name),n.push(Ut(e.name,"directive")));const{loc:i}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=Ie("true",!1,i);n.push(Te(e.modifiers.map((e=>Ne(e,t))),i))}return ge(n,e.loc)}function Ns(e){return"component"===e||"Component"===e}const Is=(e,t)=>{if(Dt(e)){const{children:n,loc:s}=e,{slotName:i,slotProps:o}=ys(e,t),r=[t.prefixIdentifiers?"_ctx.$slots":"$slots",i,"{}","undefined","true"];let a=2;o&&(r[2]=o,a=3),n.length&&(r[3]=be([],n,!1,!1,s),a=4),t.scopeId&&!t.slotted&&(a=5),r.splice(a),e.codegenNode=Ae(t.helper(G),r,s)}};function ys(e,t){let n,s='"default"';const i=[];for(let o=0;o<e.props.length;o++){const t=e.props[o];if(6===t.type)t.value&&("name"===t.name?s=JSON.stringify(t.value.content):(t.name=f(t.name),i.push(t)));else if("bind"===t.name&&xt(t.arg,"name")){if(t.exp)s=t.exp;else if(t.arg&&4===t.arg.type){const e=f(t.arg.content);s=t.exp=Ie(e,!1,t.arg.loc)}}else"bind"===t.name&&t.arg&&ft(t.arg)&&(t.arg.content=f(t.arg.content)),i.push(t)}if(i.length>0){const{props:s,directives:o}=Es(e,t,i,!1,!1);n=s,o.length&&t.onError(Ze(36,o[0].loc))}return{slotName:s,slotProps:n}}const Os=/^\s*([\w$_]+|(async\s*)?\([^)]*?\))\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,As=(e,t,n,s)=>{const{loc:i,modifiers:o,arg:r}=e;let a;if(4===r.type)if(r.isStatic){let e=r.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`);a=Ie(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?_(f(e)):`on:${e}`,!0,r.loc)}else a=Oe([`${n.helperString(te)}(`,r,")"]);else a=r,a.children.unshift(`${n.helperString(te)}(`),a.children.push(")");let c=e.exp;c&&!c.content.trim()&&(c=void 0);let l=n.cacheHandlers&&!c&&!n.inVOnce;if(c){const e=yt(c.content),t=!(e||Os.test(c.content)),n=c.content.includes(";");(t||l&&e)&&(c=Oe([`${t?"$event":"(...args)"} => ${n?"{":"("}`,c,n?"}":")"]))}let h={props:[Ne(a,c||Ie("() => {}",!1,i))]};return s&&(h=s(h)),l&&(h.props[0].value=n.cache(h.props[0].value)),h.props.forEach((e=>e.key.isHandlerKey=!0)),h},bs=(e,t,n)=>{const{modifiers:s,loc:i}=e,o=e.arg;let{exp:r}=e;if(r&&4===r.type&&!r.content.trim()&&(r=void 0),!r){if(4!==o.type||!o.isStatic)return n.onError(Ze(52,o.loc)),{props:[Ne(o,Ie("",!0,i))]};const t=f(o.content);r=e.exp=Ie(t,!1,o.loc)}return 4!==o.type?(o.children.unshift("("),o.children.push(') || ""')):o.isStatic||(o.content=`${o.content} || ""`),s.includes("camel")&&(4===o.type?o.content=o.isStatic?f(o.content):`${n.helperString(Z)}(${o.content})`:(o.children.unshift(`${n.helperString(Z)}(`),o.children.push(")"))),n.inSSR||(s.includes("prop")&&vs(o,"."),s.includes("attr")&&vs(o,"^")),{props:[Ne(o,r)]}},vs=(e,t)=>{4===e.type?e.content=e.isStatic?t+e.content:`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},Cs=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let s,i=!1;for(let e=0;e<n.length;e++){const t=n[e];if(Lt(t)){i=!0;for(let i=e+1;i<n.length;i++){const o=n[i];if(!Lt(o)){s=void 0;break}s||(s=n[e]=Oe([t],t.loc)),s.children.push(" + ",o),n.splice(i,1),i--}}}if(i&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const s=n[e];if(Lt(s)||8===s.type){const i=[];2===s.type&&" "===s.content||i.push(s),t.ssr||0!==Cn(s,t)||i.push("1"),n[e]={type:12,content:s,loc:s.loc,codegenNode:Ae(t.helper(V),i)}}}}},xs=new WeakSet,Rs=(e,t)=>{if(1===e.type&&vt(e,"once",!0)){if(xs.has(e)||t.inVOnce||t.inSSR)return;return xs.add(e),t.inVOnce=!0,t.helper(ne),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},Ls=(e,t,n)=>{const{exp:s,arg:i}=e;if(!s)return n.onError(Ze(41,e.loc)),Ms();const o=s.loc.source,r=4===s.type?s.content:o,a=n.bindingMetadata[o];if("props"===a||"props-aliased"===a)return Ms();if(!r.trim()||!yt(r))return n.onError(Ze(42,s.loc)),Ms();const c=i||Ie("modelValue",!0),l=i?ft(i)?`onUpdate:${f(i.content)}`:Oe(['"onUpdate:" + ',i]):"onUpdate:modelValue";let h;h=Oe([`${n.isTS?"($event: any)":"$event"} => ((`,s,") = $event)"]);const d=[Ne(c,e.exp),Ne(l,h)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(Et(e)?e:JSON.stringify(e))+": true")).join(", "),n=i?ft(i)?`${i.content}Modifiers`:Oe([i,' + "Modifiers"']):"modelModifiers";d.push(Ne(n,Ie(`{ ${t} }`,!1,e.loc,2)))}return Ms(d)};function Ms(e=[]){return{props:e}}const Ps=/[\w).+\-_$\]]/,Ds=(e,t)=>{We("COMPILER_FILTERS",t)&&(5===e.type&&ks(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&ks(e.exp,t)})))};function ks(e,t){if(4===e.type)Vs(e,t);else for(let n=0;n<e.children.length;n++){const s=e.children[n];"object"==typeof s&&(4===s.type?Vs(s,t):8===s.type?ks(e,t):5===s.type&&ks(s.content,t))}}function Vs(e,t){const n=e.content;let s,i,o,r,a=!1,c=!1,l=!1,h=!1,d=0,p=0,u=0,f=0,m=[];for(o=0;o<n.length;o++)if(i=s,s=n.charCodeAt(o),a)39===s&&92!==i&&(a=!1);else if(c)34===s&&92!==i&&(c=!1);else if(l)96===s&&92!==i&&(l=!1);else if(h)47===s&&92!==i&&(h=!1);else if(124!==s||124===n.charCodeAt(o+1)||124===n.charCodeAt(o-1)||d||p||u){switch(s){case 34:c=!0;break;case 39:a=!0;break;case 96:l=!0;break;case 40:u++;break;case 41:u--;break;case 91:p++;break;case 93:p--;break;case 123:d++;break;case 125:d--}if(47===s){let e,t=o-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&Ps.test(e)||(h=!0)}}else void 0===r?(f=o+1,r=n.slice(0,o).trim()):_();function _(){m.push(n.slice(f,o).trim()),f=o+1}if(void 0===r?r=n.slice(0,o).trim():0!==f&&_(),m.length){for(o=0;o<m.length;o++)r=Xs(r,m[o],t);e.content=r}}function Xs(e,t,n){n.helper(B);const s=t.indexOf("(");if(s<0)return n.filters.add(t),`${Ut(t,"filter")}(${e})`;{const i=t.slice(0,s),o=t.slice(s+1);return n.filters.add(i),`${Ut(i,"filter")}(${e}${")"!==o?","+o:o}`}}const ws=new WeakSet,Us=(e,t)=>{if(1===e.type){const n=vt(e,"memo");if(!n||ws.has(e))return;return ws.add(e),()=>{const s=e.codegenNode||t.currentNode.codegenNode;s&&13===s.type&&(1!==e.tagType&&Xe(s,t),e.codegenNode=Ae(t.helper(ce),[n.exp,be(void 0,s),"_cache",String(t.cached++)]))}}};function Fs(e){return[[Rs,Qn,Us,ns,Ds,Is,ms,as,Cs],{on:As,bind:bs,model:Ls}]}function Bs(e,t={}){const n=t.onError||Qe,s="module"===t.mode;!0===t.prefixIdentifiers?n(Ze(47)):s&&n(Ze(48));t.cacheHandlers&&n(Ze(49)),t.scopeId&&!s&&n(Ze(50));const i=o({},t,{prefixIdentifiers:!1}),r=a(e)?On(e,i):e,[c,l]=Fs();return kn(r,o({},i,{nodeTransforms:[...c,...t.nodeTransforms||[]],directiveTransforms:o({},l,t.directiveTransforms||{})})),Fn(r,i)}const $s={DATA:"data",PROPS:"props",PROPS_ALIASED:"props-aliased",SETUP_LET:"setup-let",SETUP_CONST:"setup-const",SETUP_REACTIVE_CONST:"setup-reactive-const",SETUP_MAYBE_REF:"setup-maybe-ref",SETUP_REF:"setup-ref",OPTIONS:"options",LITERAL_CONST:"literal-const"},Hs=()=>({props:[]}),Gs=Symbol(""),qs=Symbol(""),Js=Symbol(""),js=Symbol(""),Ws=Symbol(""),Ks=Symbol(""),Ys=Symbol(""),Qs=Symbol(""),zs=Symbol(""),Zs=Symbol("");let ei;de({[Gs]:"vModelRadio",[qs]:"vModelCheckbox",[Js]:"vModelText",[js]:"vModelSelect",[Ws]:"vModelDynamic",[Ks]:"withModifiers",[Ys]:"withKeys",[Qs]:"vShow",[zs]:"Transition",[Zs]:"TransitionGroup"});const ti={parseMode:"html",isVoidTag:O,isNativeTag:e=>N(e)||I(e)||y(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return ei||(ei=document.createElement("div")),t?(ei.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,ei.children[0].getAttribute("foo")):(ei.innerHTML=e,ei.textContent)},isBuiltInComponent:e=>"Transition"===e||"transition"===e?zs:"TransitionGroup"===e||"transition-group"===e?Zs:void 0,getNamespace(e,t,n){let s=t?t.ns:n;if(t&&2===s)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(s=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(s=0);else t&&1===s&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(s=0));if(0===s){if("svg"===e)return 1;if("math"===e)return 2}return s}},ni=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:Ie("style",!0,t.loc),exp:si(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},si=(e,t)=>{const n=function(e){const t={};return e.replace(T,"").split(S).forEach((e=>{if(e){const n=e.split(g);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return Ie(JSON.stringify(n),!1,t,3)};function ii(e,t){return Ze(e,t)}const oi={X_V_HTML_NO_EXPRESSION:53,53:"X_V_HTML_NO_EXPRESSION",X_V_HTML_WITH_CHILDREN:54,54:"X_V_HTML_WITH_CHILDREN",X_V_TEXT_NO_EXPRESSION:55,55:"X_V_TEXT_NO_EXPRESSION",X_V_TEXT_WITH_CHILDREN:56,56:"X_V_TEXT_WITH_CHILDREN",X_V_MODEL_ON_INVALID_ELEMENT:57,57:"X_V_MODEL_ON_INVALID_ELEMENT",X_V_MODEL_ARG_ON_ELEMENT:58,58:"X_V_MODEL_ARG_ON_ELEMENT",X_V_MODEL_ON_FILE_INPUT_ELEMENT:59,59:"X_V_MODEL_ON_FILE_INPUT_ELEMENT",X_V_MODEL_UNNECESSARY_VALUE:60,60:"X_V_MODEL_UNNECESSARY_VALUE",X_V_SHOW_NO_EXPRESSION:61,61:"X_V_SHOW_NO_EXPRESSION",X_TRANSITION_INVALID_CHILDREN:62,62:"X_TRANSITION_INVALID_CHILDREN",X_IGNORED_SIDE_EFFECT_TAG:63,63:"X_IGNORED_SIDE_EFFECT_TAG",__EXTEND_POINT__:64,64:"__EXTEND_POINT__"},ri={53:"v-html is missing expression.",54:"v-html will override element children.",55:"v-text is missing expression.",56:"v-text will override element children.",57:"v-model can only be used on <input>, <textarea> and <select> elements.",58:"v-model argument is not supported on plain elements.",59:"v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.",60:"Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.",61:"v-show is missing expression.",62:"<Transition> expects exactly one child element or component.",63:"Tags with side effect (<script> and <style>) are ignored in client component templates."},ai=e("passive,once,capture"),ci=e("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),li=e("left,right"),hi=e("onkeyup,onkeydown,onkeypress",!0),di=(e,t)=>ft(e)&&"onclick"===e.content.toLowerCase()?Ie(t,!0):4!==e.type?Oe(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,pi=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},ui=[ni],fi={cloak:Hs,html:(e,t,n)=>{const{exp:s,loc:i}=e;return s||n.onError(ii(53,i)),t.children.length&&(n.onError(ii(54,i)),t.children.length=0),{props:[Ne(Ie("innerHTML",!0,i),s||Ie("",!0))]}},text:(e,t,n)=>{const{exp:s,loc:i}=e;return s||n.onError(ii(55,i)),t.children.length&&(n.onError(ii(56,i)),t.children.length=0),{props:[Ne(Ie("textContent",!0),s?Cn(s,n)>0?s:Ae(n.helperString(J),[s],i):Ie("",!0))]}},model:(e,t,n)=>{const s=Ls(e,t,n);if(!s.props.length||1===t.tagType)return s;e.arg&&n.onError(ii(58,e.arg.loc));const{tag:i}=t,o=n.isCustomElement(i);if("input"===i||"textarea"===i||"select"===i||o){let r=Js,a=!1;if("input"===i||o){const s=Ct(t,"type");if(s){if(7===s.type)r=Ws;else if(s.value)switch(s.value.content){case"radio":r=Gs;break;case"checkbox":r=qs;break;case"file":a=!0,n.onError(ii(59,e.loc))}}else Rt(t)&&(r=Ws)}else"select"===i&&(r=js);a||(s.needRuntime=n.helper(r))}else n.onError(ii(57,e.loc));return s.props=s.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),s},on:(e,t,n)=>As(e,t,n,(t=>{const{modifiers:s}=e;if(!s.length)return t;let{key:i,value:o}=t.props[0];const{keyModifiers:r,nonKeyModifiers:a,eventOptionModifiers:c}=((e,t,n,s)=>{const i=[],o=[],r=[];for(let a=0;a<t.length;a++){const s=t[a];"native"===s&&Ke("COMPILER_V_ON_NATIVE",n)||ai(s)?r.push(s):li(s)?ft(e)?hi(e.content)?i.push(s):o.push(s):(i.push(s),o.push(s)):ci(s)?o.push(s):i.push(s)}return{keyModifiers:i,nonKeyModifiers:o,eventOptionModifiers:r}})(i,s,n);if(a.includes("right")&&(i=di(i,"onContextmenu")),a.includes("middle")&&(i=di(i,"onMouseup")),a.length&&(o=Ae(n.helper(Ks),[o,JSON.stringify(a)])),!r.length||ft(i)&&!hi(i.content)||(o=Ae(n.helper(Ys),[o,JSON.stringify(r)])),c.length){const e=c.map(m).join("");i=ft(i)?Ie(`${i.content}${e}`,!0):Oe(["(",i,`) + "${e}"`])}return{props:[Ne(i,o)]}})),show:(e,t,n)=>{const{exp:s,loc:i}=e;return s||n.onError(ii(61,i)),{props:[],needRuntime:n.helper(Qs)}}};function mi(e,t={}){return Bs(e,o({},ti,t,{nodeTransforms:[pi,...ui,...t.nodeTransforms||[]],directiveTransforms:o({},fi,t.directiveTransforms||{}),transformHoist:null}))}function _i(e,t={}){return On(e,o({},ti,t))}export{x as BASE_TRANSITION,$s as BindingTypes,Z as CAMELIZE,ee as CAPITALIZE,L as CREATE_BLOCK,k as CREATE_COMMENT,M as CREATE_ELEMENT_BLOCK,D as CREATE_ELEMENT_VNODE,q as CREATE_SLOTS,X as CREATE_STATIC,V as CREATE_TEXT,P as CREATE_VNODE,qe as CompilerDeprecationTypes,me as ConstantTypes,fi as DOMDirectiveTransforms,oi as DOMErrorCodes,ri as DOMErrorMessages,ui as DOMNodeTransforms,fe as ElementTypes,et as ErrorCodes,A as FRAGMENT,Q as GUARD_REACTIVE_PROPS,le as IS_MEMO_SAME,ae as IS_REF,C as KEEP_ALIVE,j as MERGE_PROPS,W as NORMALIZE_CLASS,Y as NORMALIZE_PROPS,K as NORMALIZE_STYLE,pe as Namespaces,ue as NodeTypes,R as OPEN_BLOCK,ie as POP_SCOPE_ID,se as PUSH_SCOPE_ID,H as RENDER_LIST,G as RENDER_SLOT,w as RESOLVE_COMPONENT,F as RESOLVE_DIRECTIVE,U as RESOLVE_DYNAMIC_COMPONENT,B as RESOLVE_FILTER,ne as SET_BLOCK_TRACKING,v as SUSPENSE,b as TELEPORT,J as TO_DISPLAY_STRING,z as TO_HANDLERS,te as TO_HANDLER_KEY,zs as TRANSITION,Zs as TRANSITION_GROUP,pt as TS_NODE_TYPES,re as UNREF,qs as V_MODEL_CHECKBOX,Ws as V_MODEL_DYNAMIC,Gs as V_MODEL_RADIO,js as V_MODEL_SELECT,Js as V_MODEL_TEXT,Ys as V_ON_WITH_KEYS,Ks as V_ON_WITH_MODIFIERS,Qs as V_SHOW,oe as WITH_CTX,$ as WITH_DIRECTIVES,ce as WITH_MEMO,Ot as advancePositionWithClone,At as advancePositionWithMutation,bt as assert,Bs as baseCompile,On as baseParse,Ts as buildDirectiveArgs,Es as buildProps,hs as buildSlots,Ke as checkCompatEnabled,mi as compile,Xe as convertToBlock,ge as createArrayExpression,Me as createAssignmentExpression,xe as createBlockStatement,Ce as createCacheExpression,Ae as createCallExpression,Ze as createCompilerError,Oe as createCompoundExpression,ve as createConditionalExpression,ii as createDOMCompilerError,os as createForLoopParams,be as createFunctionExpression,Le as createIfStatement,ye as createInterpolation,Te as createObjectExpression,Ne as createObjectProperty,De as createReturnStatement,Ee as createRoot,Pe as createSequenceExpression,Ie as createSimpleExpression,Xn as createStructuralDirectiveTransform,Re as createTemplateLiteral,Dn as createTransformContext,Se as createVNodeCall,tt as errorMessages,ct as extractIdentifiers,vt as findDir,Ct as findProp,$t as forAliasRE,Fn as generate,E as generateCodeFrame,Fs as getBaseTransformPreset,Cn as getConstantType,Bt as getMemoedVNodeCall,Ve as getVNodeBlockHelper,ke as getVNodeHelper,Rt as hasDynamicKeyVBind,Ft as hasScopeRef,he as helperNameMap,Xt as injectProp,mt as isCoreComponent,lt as isFunctionType,it as isInDestructureAssignment,ot as isInNewExpression,yt as isMemberExpression,Nt as isMemberExpressionBrowser,It as isMemberExpressionNode,st as isReferencedIdentifier,Et as isSimpleIdentifier,Dt as isSlotOutlet,xt as isStaticArgOf,ft as isStaticExp,ht as isStaticProperty,dt as isStaticPropertyKey,Pt as isTemplateNode,Lt as isText,Mt as isVSlot,_e as locStub,Hs as noopDirectiveTransform,_i as parse,ti as parserOptions,Kn as processExpression,ss as processFor,zn as processIf,ys as processSlotOutlet,de as registerRuntimeHelpers,_s as resolveComponentType,Yn as stringifyExpression,Ut as toValidAssetId,as as trackSlotScopes,cs as trackVForSlotScopes,kn as transform,bs as transformBind,ms as transformElement,Wn as transformExpression,Ls as transformModel,As as transformOn,ni as transformStyle,Vn as traverseNode,ut as unwrapTSNode,at as walkBlockDeclarations,rt as walkFunctionParams,nt as walkIdentifiers,Ye as warnDeprecation};
