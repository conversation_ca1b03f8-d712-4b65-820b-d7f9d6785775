import instance from '../../utils/axios.js'

/**
 * 货区管理相关接口
 */
const depotApi = {
  /**
   * 获取货区列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 请求Promise
   */
  getDepotList: (params) => instance.get('/depot/list', { params }),
  
  /**
   * 创建货区
   * @param {Object} data - 货区信息
   * @returns {Promise} - 请求Promise
   */
  createDepot: (data) => instance.post('/depot/create', data),
  
  /**
   * 更新货区
   * @param {Object} data - 货区信息
   * @returns {Promise} - 请求Promise
   */
  updateDepot: (data) => instance.put('/depot/update', data),
  
  /**
   * 删除货区
   * @param {Number|String} id - 货区ID
   * @returns {Promise} - 请求Promise
   */
  deleteDepot: (id) => instance.delete(`/depot/delete/${id}`),
  
  /**
   * 获取货区详情
   * @param {Number|String} id - 货区ID
   * @returns {Promise} - 请求Promise
   */
  getDepotById: (id) => instance.get(`/depot/get/${id}`),
  
  /**
   * 根据用户ID获取货区
   * @param {Number|String} userId - 用户ID
   * @returns {Promise} - 请求Promise
   */
  getDepotsByUserId: (userId) => instance.get(`/depot/user/${userId}`),
  
  /**
   * 根据货区编码获取货区
   * @param {String} code - 货区编码
   * @returns {Promise} - 请求Promise
   */
  getDepotByCode: (code) => instance.get(`/depot/code/${code}`),
  
  /**
   * 根据一级货区ID获取二级货架信息
   * @param {Number|String} id - 一级货区ID
   * @returns {Promise} - 请求Promise
   */
  getShelvesByDepotId: (id) => instance.get(`/depot/shelves/${id}`),
  
  /**
   * 根据二级货架ID获取三级货位信息
   * @param {Number|String} id - 二级货架ID
   * @returns {Promise} - 请求Promise
   */
  getFreightByShelveId: (id) => instance.get(`/depot/freight/${id}`)
}

// 导出模块
export { depotApi } 