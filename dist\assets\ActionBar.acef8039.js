import{R as s}from"./RefreshButton.c2d64dc7.js";import{o as a,k as e,l as t,ap as o,F as r,an as i}from"./vendor.9a6f3141.js";const n={class:"action-bar"},c={class:"action-left"},f={class:"action-right"},d=Object.assign({name:"ActionBar"},{__name:"ActionBar",props:{showRefresh:{type:Boolean,default:!0}},emits:["refresh"],setup(d,{emit:h}){const l=h,p=()=>{l("refresh")};return(h,l)=>(a(),e("div",n,[t("div",c,[o(h.$slots,"left",{},void 0,!0)]),t("div",f,[o(h.$slots,"right",{},(()=>[d.showRefresh?(a(),r(s,{key:0,onRefresh:p})):i("",!0)]),!0)])]))}});d.__scopeId="data-v-01186ab0";var h=d;export{h as A};
