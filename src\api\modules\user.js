import instance from '../../utils/axios.js'

// 用户相关API
const userApi = {
  // 获取用户列表
  getUserList: () => instance.get('/user/list'),
  
  // 获取单个用户信息
  getUserById: (id) => instance.get(`/user/get/${id}`),
  
  // 注册/添加用户
  register: (data) => instance.post('/user/register', data),
  
  // 更新用户信息
  updateUser: (data) => instance.put('/user/update', data),
  
  // 删除用户
  deleteUser: (id) => instance.delete(`/user/delete/${id}`),
  
}; 

// 导出模块
export { userApi };