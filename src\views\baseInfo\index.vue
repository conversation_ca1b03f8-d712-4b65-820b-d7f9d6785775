<template>
	<div class="list-container">
		<!-- 搜索区域 -->
		<div class="search-area">
			<div class="search-row">
				<div class="search-item">
					<span class="search-label">书名</span>
					<el-input v-model="searchForm.bookName" placeholder="请输入书名" clearable />
				</div>
				
				<div class="search-item">
					<span class="search-label">isbn</span>
					<el-input v-model="searchForm.isbn" placeholder="请输入isbn" clearable />
				</div>
				
				<div class="search-item">
					<span class="search-label">作者</span>
					<el-input v-model="searchForm.author" placeholder="请输入作者" clearable />
				</div>
				
				<div class="search-item">
					<span class="search-label">出版社</span>
					<el-input v-model="searchForm.publisher" placeholder="请输入出版社" clearable />
				</div>
			</div>
			
			<div class="search-row">
				<div class="search-item">
					<span class="search-label">书图片</span>
					<el-select v-model="searchForm.hasImage" placeholder="请选择图片状态" clearable>
						<el-option label="请选择图片状态" :value="null" />
						<el-option label="有图片" :value="1" />
						<el-option label="无图片" :value="0" />
					</el-select>
				</div>
				
				<div class="search-item date-range-item">
					<span class="search-label">出版时间范围</span>
					<el-date-picker
						v-model="searchForm.timeRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD"
						:shortcuts="dateShortcuts"
					/>
				</div>
				
				<div class="search-item">
					<span class="search-label">违规信息筛选</span>
					<el-select 
						v-model="searchForm.violationTypes" 
						placeholder="请选择违规条件" 
						clearable 
						multiple 
						style="width: 240px"
						collapse-tags
						collapse-tags-tooltip
					>
						<el-option label="违规书号" :value="1" />
						<el-option label="套装书" :value="2" />
						<el-option label="一号多书" :value="3" />
						<el-option label="违规出版社" :value="4" />
						<el-option label="违规作者" :value="5" />
					</el-select>
				</div>
				
				<div class="search-item btn-item">
					<el-button type="primary" @click="handleNormalSearch" class="search-btn">
						<el-icon><Search /></el-icon>
						搜索
					</el-button>
				</div>
			</div>
			
			<div class="search-row" :class="{ 'advanced-search-hidden': !showAdvancedSearch }">
				<div class="search-item">
					<span class="search-label">销量</span>
					<el-select v-model="searchForm.inventoryType" placeholder="请选择销量类型" clearable @change="handleSaleTypeChange">
						<el-option label="请选择销量类型" :value="null" />
						<el-option label="7天销量" :value="7" />
						<el-option label="15天销量" :value="15" />
						<el-option label="30天销量" :value="30" />
						<el-option label="60天销量" :value="60" />
						<el-option label="90天销量" :value="90" />
						<el-option label="180天销量" :value="180" />
						<el-option label="365天销量" :value="365" />
						<el-option label="今年销量" :value="0" />
					</el-select>
				</div>
				
				<div class="search-item number-range-item">
					<span class="search-label">已售</span>
					<el-input-number v-model="searchForm.minInventory" :min="0" placeholder="最小值" controls-position="right" />
					<span class="range-separator">至</span>
					<el-input-number v-model="searchForm.maxInventory" :min="0" placeholder="最大值" controls-position="right" />
				</div>
				
				<div class="search-item number-range-item">
					<span class="search-label">在售</span>
					<el-input-number v-model="searchForm.minSelling" :min="0" placeholder="最小值" controls-position="right" />
					<span class="range-separator">至</span>
					<el-input-number v-model="searchForm.maxSelling" :min="0" placeholder="最大值" controls-position="right" />
				</div>
				
				<div class="search-item btn-item">
					<el-button @click="resetSearch" class="reset-btn">
						<el-icon><Refresh /></el-icon>
						重置
					</el-button>
					<el-button type="primary" @click="handleAdvancedSearch" plain class="adv-search-btn">高级搜索</el-button>
				</div>
			</div>
			
			<div class="toggle-advanced-search">
				<el-button type="text" @click="toggleAdvancedSearch">
					{{ showAdvancedSearch ? '收起高级搜索' : '展开高级搜索' }}
					<el-icon :class="{ 'rotate-icon': showAdvancedSearch }"><ArrowDown /></el-icon>
				</el-button>
			</div>
		</div>

		<!-- 操作按钮 -->
		<div class="action-bar">
			<div class="action-left">
				<el-button type="primary" @click="handleAdd">新增</el-button>
				<el-button type="primary" @click="handleModify">修改</el-button>
				<el-button type="danger" @click="handleBatchDelete">删除</el-button>
				<el-button type="primary" @click="handleConfig">违规设置</el-button>
				<el-button type="primary" @click="handlePriceConfig">核价</el-button>
			</div>
			<div class="action-right">
				<RefreshButton @refresh="refreshData" />
			</div>
		</div>

		<!-- 数据表格 -->
		<el-table
			ref="tableRef"
			:data="tableData"
			border
			style="width: 100%"
			@selection-change="handleSelectionChange"
			row-key="id"
			v-loading="loading"
			:header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', textAlign: 'center' }"
			height="500"
			max-height="500"
		>
			<el-table-column type="selection" align="center" width="50" />
			<el-table-column prop="bookName" align="center" label="书名" show-overflow-tooltip>
				<template #default="{ row }">
					<div ref="textRef" class="ellipsis-text">
						<el-tooltip 
							:content="row.bookName || '-'" 
							placement="top" 
							:disabled="!isTextEllipsis($event)"
							:enterable="false"
						>
							<span class="ellipsis-text">{{ row.bookName || '-' }}</span>
						</el-tooltip>
					</div>
				</template>
			</el-table-column>
			<el-table-column align="center" label="书图片" width="80">
				<template #default="{ row }">
					<el-image 
						v-if="row.bookPic && row.bookPic !== '0'" 
						:src="getBookImageUrl(row)" 
						:preview-src-list="previewList"
						:initial-index="getPreviewIndex(row)"
						style="width: 40px; height: 60px; object-fit: cover;" 
						@click="handlePreview(row)"
						fit="cover"
						preview-teleported
					/>
					<span v-else class="no-image">无图</span>
				</template>
			</el-table-column>
			<el-table-column prop="isbn" align="center" label="isbn" width="120" show-overflow-tooltip>
				<template #default="{ row }">
					<div ref="textRef" class="ellipsis-text">
						<el-tooltip 
							:content="row.isbn || '-'" 
							placement="top" 
							:disabled="!isTextEllipsis($event)"
							:enterable="false"
						>
							<span class="ellipsis-text">{{ row.isbn || '-' }}</span>
						</el-tooltip>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="author" align="center" label="作者" show-overflow-tooltip>
				<template #default="{ row }">
					<div ref="textRef" class="ellipsis-text">
						<el-tooltip 
							:content="row.author || '-'" 
							placement="top" 
							:disabled="!isTextEllipsis($event)"
							:enterable="false"
						>
							<span class="ellipsis-text">{{ row.author || '-' }}</span>
						</el-tooltip>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="publisher" align="center" label="出版社" show-overflow-tooltip>
				<template #default="{ row }">
					<div ref="textRef" class="ellipsis-text">
						<el-tooltip 
							:content="row.publisher || '-'" 
							placement="top" 
							:disabled="!isTextEllipsis($event)"
							:enterable="false"
						>
							<span class="ellipsis-text">{{ row.publisher || '-' }}</span>
						</el-tooltip>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="publicationTime" align="center" label="出版时间" width="90">
				<template #default="{ row }">
					{{ row.publicationTime || '-' }}
				</template>
			</el-table-column>
			<el-table-column prop="bindingLayout" align="center" label="装帧" width="80">
				<template #default="{ row }">
					{{ row.bindingLayout || '-' }}
				</template>
			</el-table-column>
			<el-table-column prop="fixPrice" align="center" label="定价" width="80">
				<template #default="{ row }">
					{{ row.fixPrice ? (row.fixPrice / 100).toFixed(2) : '-' }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="全部销量" width="90">
				<template #default="{ row }">
					{{ row.totalSale || 0 }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="销量详情" width="100">
				<template #default="{ row }">
					<el-popover
						placement="right"
						trigger="hover"
						:width="220"
						popper-class="sales-popover"
					>
						<template #default>
							<div class="sales-detail">
								<div class="sales-item"><span class="sales-label">7天销量:</span> {{ row.daySale7 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">15天销量:</span> {{ row.daySale15 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">30天销量:</span> {{ row.daySale30 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">60天销量:</span> {{ row.daySale60 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">90天销量:</span> {{ row.daySale90 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">180天销量:</span> {{ row.daySale180 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">365天销量:</span> {{ row.daySale365 || 0 }}</div>
								<div class="sales-item"><span class="sales-label">今年销量:</span> {{ row.thisYearSale || 0 }}</div>
								<div class="sales-item"><span class="sales-label">去年销量:</span> {{ row.lastYearSale || 0 }}</div>
								<div class="sales-item"><span class="sales-label">总销量:</span> {{ row.totalSale || 0 }}</div>
							</div>
						</template>
						<template #reference>
							<div class="sales-trigger">
								<span>{{ row.sale7Days || 0 }}</span>
								<el-icon><ArrowRight /></el-icon>
							</div>
						</template>
					</el-popover>
				</template>
			</el-table-column>
			<el-table-column align="center" label="在售" width="80">
				<template #default="{ row }">
					{{ row.sellCount || 0 }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="违规类型" width="100">
				<template #default="{ row }">
					<template v-if="hasViolation(row)">
						<el-tooltip placement="top" :content="getViolationText(row)">
							<el-tag type="danger" size="small" effect="plain">违规</el-tag>
						</el-tooltip>
					</template>
					<template v-else>
						<span class="normal-text">正常</span>
					</template>
				</template>
			</el-table-column>
			<el-table-column align="center" label="操作" width="100">
				<template #default="{ row }">
					<el-button type="primary" size="small" text @click="handleEdit(row)">编辑</el-button>
					<el-button type="danger" size="small" text @click="handleDelete(row)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-container">
			<el-pagination
				:current-page="pagination.current"
				:page-size="pagination.size"
				:page-sizes="[10, 20, 50, 100]"
				:layout="'total, sizes, prev, pager, next, jumper'"
				:total="pagination.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 新增/编辑对话框 -->
		<el-dialog
			:title="dialogType === 'add' ? '新增图书信息' : '编辑图书信息'"
			v-model="dialogVisible"
			width="800px"
			:close-on-click-modal="false"
		>
			<el-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				label-width="120px"
				label-position="right"
			>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="书名" prop="bookName">
							<el-input v-model="formData.bookName" placeholder="请输入书名" maxlength="400" show-word-limit />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="ISBN" prop="isbn">
							<el-input v-model="formData.isbn" placeholder="请输入ISBN" maxlength="30" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
				</el-row>
				
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="作者" prop="author">
							<el-input v-model="formData.author" placeholder="请输入作者" maxlength="400" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="出版社" prop="publisher">
							<el-input v-model="formData.publisher" placeholder="请输入出版社" maxlength="200" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
				</el-row>
				
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="出版时间" prop="publicationTime">
							<el-date-picker
								v-model="formData.publicationTime"
								type="date"
								placeholder="选择出版时间"
								value-format="YYYY-MM"
								:disabled="dialogType === 'edit'"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="装帧" prop="bindingLayout">
							<el-input v-model="formData.bindingLayout" placeholder="请输入装帧" maxlength="30" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
				</el-row>
				
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="定价" prop="fixPrice">
							<el-input-number v-model="formData.fixPrice" :min="0" :precision="2" :step="1" placeholder="请输入定价" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="页数" prop="pages">
							<el-input-number v-model="formData.pages" :min="0" :precision="0" :step="1" placeholder="请输入页数" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
				</el-row>
				
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="编辑" prop="editor">
							<el-input v-model="formData.editor" placeholder="请输入编辑" maxlength="30" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="版次" prop="edition">
							<el-input v-model="formData.edition" placeholder="请输入版次" maxlength="50" :disabled="dialogType === 'edit'" />
						</el-form-item>
					</el-col>
				</el-row>
				
				<el-form-item label="书图片" prop="bookPic">
					<el-upload
						class="book-pic-uploader"
						:action="uploadUrl"
						:show-file-list="false"
						:on-success="handleUploadSuccess"
						:before-upload="beforeBookPicUpload"
						:disabled="dialogType === 'edit'"
					>
						<img v-if="formData.bookPic && formData.bookPic !== '0'" :src="formData.bookPic" class="book-pic" />
						<el-icon v-else class="book-pic-uploader-icon" :class="{'disabled-uploader': dialogType === 'edit'}"><Plus /></el-icon>
					</el-upload>
					<div v-if="dialogType === 'edit'" class="edit-disabled-tip">编辑模式下不允许更改图片</div>
				</el-form-item>
				
				<el-form-item label="内容简介" prop="content">
					<el-input
						v-model="formData.content"
						type="textarea"
						:rows="4"
						placeholder="请输入内容简介"
						maxlength="500"
						show-word-limit
						:disabled="dialogType === 'edit'"
					/>
				</el-form-item>
				
				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="formData.remark"
						type="textarea"
						:rows="3"
						placeholder="请输入备注"
						maxlength="500"
						show-word-limit
						:disabled="dialogType === 'edit'"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
				</span>
			</template>
		</el-dialog>
		
		<!-- 违规设置对话框 -->
		<el-dialog
			title="设置违规类型"
			v-model="configDialogVisible"
			width="500px"
			:close-on-click-modal="false"
			class="violation-dialog"
		>
			<div class="violation-config">
				<el-row :gutter="20">
					<el-col :span="12">
						<div class="violation-item">
							<el-checkbox v-model="violationConfig.vioBook">
								<span class="violation-label">违规书号</span>
							</el-checkbox>
						</div>
					</el-col>
					<el-col :span="12">
						<div class="violation-item">
							<el-checkbox v-model="violationConfig.bookSet">
								<span class="violation-label">套装书</span>
							</el-checkbox>
						</div>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<div class="violation-item">
							<el-checkbox v-model="violationConfig.onenumMbooks">
								<span class="violation-label">一号多书</span>
							</el-checkbox>
						</div>
					</el-col>
					<el-col :span="12">
						<div class="violation-item">
							<el-checkbox v-model="violationConfig.illPublisher">
								<span class="violation-label">违规出版社</span>
							</el-checkbox>
						</div>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<div class="violation-item">
							<el-checkbox v-model="violationConfig.illAuthor">
								<span class="violation-label">违规作者</span>
							</el-checkbox>
						</div>
					</el-col>
				</el-row>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="configDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleViolationSubmit" :loading="configSubmitLoading">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { bookBaseInfoApi } from '@/api/modules/bookBaseInfo'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowRight, Search, Refresh, ArrowDown } from '@element-plus/icons-vue'
import RefreshButton from '@/components/RefreshButton.vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableRef = ref(null)

// 多选数据
const multipleSelection = ref([])

// 搜索表单
const searchForm = reactive({
	bookName: '',
	isbn: '',
	author: '',
	publisher: '',
	timeRange: [],
	violationTypes: [],
	inventoryType: null,
	saleSelect: null,  // 新增销量选择字段
	minInventory: 0,
	maxInventory: 999999,
	minSelling: 0,
	maxSelling: 999999,
	hasImage: null
})
const currentPreviewImg = ref('') // 仅存储当前点击的图片

// 图片预览相关
const previewList = ref([])
const currentPreviewIndex = ref(0)

// 处理图片预览
const handlePreview = (row) => {
  // 收集所有有效的图片URL
  previewList.value = tableData.value
    .filter(item => item.bookPic && item.bookPic !== '0')
    .map(item => getBookImageUrl(item))
  
  // 设置当前预览图片的索引
  currentPreviewIndex.value = previewList.value.findIndex(url => url === getBookImageUrl(row))
}

// 获取预览索引
const getPreviewIndex = (row) => {
  if (!row.bookPic || row.bookPic === '0') return 0
  const imageUrl = getBookImageUrl(row)
  return previewList.value.findIndex(url => url === imageUrl)
}

// 日期快捷选项
const dateShortcuts = [
	{
		text: '最近一年',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setFullYear(start.getFullYear() - 1)
			return [start, end]
		},
	},
	{
		text: '最近三年',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setFullYear(start.getFullYear() - 3)
			return [start, end]
		},
	},
	{
		text: '最近五年',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setFullYear(start.getFullYear() - 5)
			return [start, end]
		},
	},
]

// 分页配置
const pagination = reactive({
	current: 1,
	size: 20, // 默认每页20条
	total: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const submitLoading = ref(false)

// 违规设置对话框
const configDialogVisible = ref(false)
const configSubmitLoading = ref(false)
const violationConfig = reactive({
	vioBook: false,
	bookSet: false,
	onenumMbooks: false,
	illPublisher: false,
	illAuthor: false
})

// 上传URL
const uploadUrl = ref('/api/upload') // 替换为实际的上传API

// 表单数据
const formData = reactive({
	id: null,
	tenantId: '000000',
	bookName: '',
	isbn: '',
	author: '',
	publisher: '',
	bookPic: '',
	editor: '',
	bindingLayout: '',
	edition: '',
	publicationTime: '',
	pages: '',
	fixPrice: '',
	content: '',
	remark: '',
	vioBook: 0,
	bookSet: 0,
	onenumMbooks: 0,
	illPublisher: 0,
	illAuthor: 0
})

// 表单验证规则
const formRules = {
	bookName: [
		{ required: true, message: '请输入书名', trigger: 'blur' },
		{ max: 400, message: '书名长度不能超过400个字符', trigger: 'blur' }
	],
	isbn: [
		{ required: true, message: '请输入ISBN', trigger: 'blur' },
		{ max: 30, message: 'ISBN长度不能超过30个字符', trigger: 'blur' }
	],
	author: [
		{ required: true, message: '请输入作者', trigger: 'blur' },
		{ max: 400, message: '作者长度不能超过400个字符', trigger: 'blur' }
	],
	publisher: [
		{ required: true, message: '请输入出版社', trigger: 'blur' },
		{ max: 200, message: '出版社长度不能超过200个字符', trigger: 'blur' }
	],
	publicationTime: [
		{ required: true, message: '请选择出版时间', trigger: 'change' },
	]
}

// 高级搜索控制
const showAdvancedSearch = ref(false)

// 切换高级搜索显示状态
const toggleAdvancedSearch = () => {
	showAdvancedSearch.value = !showAdvancedSearch.value
}

// 初始化
onMounted(() => {
	fetchData()
})

// 处理普通搜索
const handleNormalSearch = () => {
	pagination.current = 1 // 重置到第一页
	
	// 清空高级搜索相关参数
	searchForm.inventoryType = null
	searchForm.saleSelect = null
	searchForm.minInventory = 0
	searchForm.maxInventory = 999999
	searchForm.minSelling = 0
	searchForm.maxSelling = 999999
	
	// 使用普通搜索模式
	fetchData(false)
}

// 处理高级搜索
const handleAdvancedSearch = () => {
	pagination.current = 1 // 重置到第一页
	fetchData(true)
}

// 刷新数据
const refreshData = () => {
	// 保持当前搜索模式
	fetchData(showAdvancedSearch.value)
}

// 获取表格数据
const fetchData = async (isAdvancedSearch = false) => {
	loading.value = true
	try {
		// 构建请求参数
		const params = {
			pageNum: pagination.current,
			pageSize: pagination.size,
			// 查询条件参数
			bookName: searchForm.bookName || undefined,
			isbn: searchForm.isbn || undefined,
			author: searchForm.author || undefined,
			publisher: searchForm.publisher || undefined,
			// 添加默认违规参数
			vioBook: 0,
			bookSet: 0,
			onenumMbooks: 0,
			illPublisher: 0,
			illAuthor: 0
		}
		
		// 添加时间范围参数
		if (searchForm.timeRange && searchForm.timeRange.length === 2) {
			params.startTime = searchForm.timeRange[0]
			params.endTime = searchForm.timeRange[1]
		}
		
		// 添加图片筛选条件
		if (searchForm.hasImage !== null) {
			params.bookPic = searchForm.hasImage
		}
		
		// 添加违规类型
		if (searchForm.violationTypes.length > 0) {
			const violationMap = {
				1: 'vioBook',
				2: 'bookSet',
				3: 'onenumMbooks',
				4: 'illPublisher',
				5: 'illAuthor'
			}
			searchForm.violationTypes.forEach(type => {
				params[violationMap[type]] = 1
			})
		}
		
		// 只在高级搜索模式下添加销量、已售和在售筛选条件
		if (isAdvancedSearch) {
			// 添加销量筛选条件
			if (searchForm.inventoryType !== null) {
				params.inventoryType = searchForm.inventoryType
				params.saleSelect = searchForm.saleSelect !== null ? searchForm.saleSelect : searchForm.inventoryType
				

			}
							// 添加已售数量范围 - 映射到BookNumberRageBo的min1和max1
			if (searchForm.minInventory > 0 || searchForm.maxInventory < 999999) {
				params.min1 = searchForm.minInventory.toString()
				params.max1 = searchForm.maxInventory.toString()
			}
			// 添加在售筛选条件 - 映射到BookNumberRageBo的min2和max2
			if (searchForm.minSelling > 0 || searchForm.maxSelling < 999999) {
				params.min2 = searchForm.minSelling.toString()
				params.max2 = searchForm.maxSelling.toString()
			}
		}
		
		const res = await bookBaseInfoApi.getBookBaseInfoList(params)
		
		if (res.code === 200 && res.data) {
			tableData.value = res.data.list || []
			pagination.total = res.data.total || 0
			pagination.current = res.data.pageNum || 1
			pagination.pages = res.data.pages || 1
		}
	} catch (error) {
		console.error('获取数据失败:', error)
		ElMessage.error('获取数据失败')
	} finally {
		loading.value = false
	}
}

// 重置搜索
const resetSearch = () => {
	searchForm.bookName = ''
	searchForm.isbn = ''
	searchForm.author = ''
	searchForm.publisher = ''
	searchForm.timeRange = []
	searchForm.violationTypes = []
	searchForm.inventoryType = null
	searchForm.saleSelect = null
	searchForm.minInventory = 0
	searchForm.maxInventory = 999999
	searchForm.minSelling = 0
	searchForm.maxSelling = 999999
	searchForm.hasImage = null
	pagination.current = 1 // 重置到第一页
	fetchData(showAdvancedSearch.value)
}

// 分页大小变化
const handleSizeChange = (size) => {
	pagination.size = size
	pagination.current = 1 // 切换每页条数时，重置到第一页
	fetchData(showAdvancedSearch.value)
}

// 页码变化
const handleCurrentChange = (current) => {
	pagination.current = current
	fetchData(showAdvancedSearch.value)
}

// 多选处理
const handleSelectionChange = (val) => {
	multipleSelection.value = val
}

// 新增
const handleAdd = () => {
	resetForm()
	dialogType.value = 'add'
	dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
	resetForm()
	dialogType.value = 'edit'
	
	// 创建一个深拷贝，避免直接引用原对象
	const rowCopy = JSON.parse(JSON.stringify(row))
	
	// 特殊处理价格字段 - 后端存储为分，界面显示为元
	if (rowCopy.fixPrice) {
		rowCopy.fixPrice = rowCopy.fixPrice / 100
	}
	
	// 将数据赋值给表单
	Object.keys(formData).forEach(key => {
		if (key in rowCopy) {
			formData[key] = rowCopy[key]
		}
	})
	
	dialogVisible.value = true
}

// 批量修改
const handleModify = () => {
	if (multipleSelection.value.length === 0) {
		ElMessage.warning('请至少选择一条记录')
		return
	}
	ElMessage.info('批量修改功能开发中')
}

// 批量删除
const handleBatchDelete = () => {
	if (multipleSelection.value.length === 0) {
		ElMessage.warning('请至少选择一条记录')
		return
	}
	
	ElMessageBox.confirm(`确定要删除选中的${multipleSelection.value.length}条记录吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			const ids = multipleSelection.value.map(item => item.id)
			await bookBaseInfoApi.batchDeleteBookBaseInfo(ids)
			ElMessage.success('删除成功')
			fetchData()
		} catch (error) {
			console.error('删除失败:', error)
			ElMessage.error('删除失败')
		}
	}).catch(() => {})
}

// 删除
const handleDelete = (row) => {
	ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			await bookBaseInfoApi.deleteBookBaseInfo(row.id)
			ElMessage.success('删除成功')
			fetchData()
		} catch (error) {
			console.error('删除失败:', error)
			ElMessage.error('删除失败')
		}
	}).catch(() => {})
}

// 配置违规
const handleConfig = () => {
	if (multipleSelection.value.length === 0) {
		ElMessage.warning('请至少选择一条记录')
		return
	}
	
	// 重置违规配置
	Object.keys(violationConfig).forEach(key => {
		violationConfig[key] = false
	})
	
	configDialogVisible.value = true
}

// 提交违规设置
const handleViolationSubmit = async () => {
	configSubmitLoading.value = true
	try {
		const ids = multipleSelection.value.map(item => item.id)
		
		// 将布尔值转换为0/1
		const params = {
			ids: ids,
			vioBook: violationConfig.vioBook ? 1 : 0,
			bookSet: violationConfig.bookSet ? 1 : 0,
			onenumMbooks: violationConfig.onenumMbooks ? 1 : 0,
			illPublisher: violationConfig.illPublisher ? 1 : 0,
			illAuthor: violationConfig.illAuthor ? 1 : 0
		}
		
		// 调用API更新违规设置
		await bookBaseInfoApi.updateViolationConfig(params)
		ElMessage.success('违规设置更新成功')
		configDialogVisible.value = false
		fetchData()
	} catch (error) {
		console.error('更新违规设置失败:', error)
		ElMessage.error('更新违规设置失败')
	} finally {
		configSubmitLoading.value = false
	}
}

// 转价
const handlePriceConfig = () => {
	ElMessage.info('转价功能开发中')
}

// 销量类型变化
const handleSaleTypeChange = (value) => {
	if (value === 0) { // 今年销量
		searchForm.saleSelect = null
	} else { // 其他销量
		searchForm.saleSelect = value
	}
}

// 图片上传前验证
const beforeBookPicUpload = (file) => {
	const isImage = file.type.startsWith('image/')
	const isLt2M = file.size / 1024 / 1024 < 2

	if (!isImage) {
		ElMessage.error('上传封面图片只能是图片格式!')
	}
	if (!isLt2M) {
		ElMessage.error('上传封面图片大小不能超过 2MB!')
	}
	return isImage && isLt2M
}

// 图片上传成功
const handleUploadSuccess = (res, file) => {
	if (res.code === 200) {
		formData.bookPic = res.data.url
	} else {
		ElMessage.error('上传失败: ' + res.message)
	}
}

// 处理提交表单
const handleSubmit = async () => {
	if (!formRef.value) return
	
	await formRef.value.validate(async (valid) => {
		if (valid) {
			submitLoading.value = true
			
			try {
				// 创建一个新对象用于提交
				const submitData = JSON.parse(JSON.stringify(formData))
				
				// 特殊处理价格 - 转换为分
				if (submitData.fixPrice) {
					submitData.fixPrice = Math.round(submitData.fixPrice * 100)
				}
				
				if (dialogType.value === 'add') {
					await bookBaseInfoApi.addBookBaseInfo(submitData)
					ElMessage.success('添加成功')
				} else {
					await bookBaseInfoApi.updateBookBaseInfo(submitData)
					ElMessage.success('更新成功')
				}
				
				dialogVisible.value = false
				fetchData()
			} catch (error) {
				console.error('提交失败:', error)
				ElMessage.error('提交失败: ' + (error.message || '未知错误'))
			} finally {
				submitLoading.value = false
			}
		}
	})
}

// 重置表单
const resetForm = () => {
	if (formRef.value) {
		formRef.value.resetFields()
	}
	Object.assign(formData, {
		id: null,
		tenantId: '000000',
		bookName: '',
		isbn: '',
		author: '',
		publisher: '',
		bookPic: '',
		editor: '',
		bindingLayout: '',
		edition: '',
		publicationTime: '',
		pages: '',
		fixPrice: '',
		content: '',
		remark: '',
		vioBook: 0,
		bookSet: 0,
		onenumMbooks: 0,
		illPublisher: 0,
		illAuthor: 0
	})
}

// 判断是否有违规标记
const hasViolation = (row) => {
	return row.vioBook === 1 || row.bookSet === 1 || row.onenumMbooks === 1 || 
		   row.illPublisher === 1 || row.illAuthor === 1
}

// 获取违规类型文本
const getViolationText = (row) => {
  const types = [];
  if (row.vioBook === 1) types.push('违规书号');
  if (row.bookSet === 1) types.push('套装书');
  if (row.onenumMbooks === 1) types.push('一号多书');
  if (row.illPublisher === 1) types.push('违规出版社');
  if (row.illAuthor === 1) types.push('违规作者');
  return types.join('，');
}

// 图片获取函数
const getBookImageUrl = (row) => {
  if (!row.bookPic) return '';
  
  // 校验图片地址 - 同步实现
  const imageUrlOne = 'https://static.buzhiyushu.cn/images/';
  const imageUrlTwo = 'https://book.goods.img.buzhiyushu.cn/';
  
  // 直接返回拼接的图片URL，不再做存在性检查
  if (row.bookPic) {
    const image = imageUrlOne + row.bookPic;
    return image;
  } else if (row.bookName && row.isbn) {
    // 使用书名md5和isbn拼接备用URL
    const hashedTitle = md5(row.bookName);
    return imageUrlTwo + hashedTitle.charAt(0) + "/" + row.isbn + "_01.jpg";
  }
  
  return '';
}

// 检测文本是否溢出
const isTextEllipsis = (event) => {
  if (!event || !event.target) return false
  
  const element = event.target
  return element.scrollWidth > element.offsetWidth
}
</script>

<style scoped>
.list-container {
	padding: 20px;
}

.search-area {
	margin-bottom: 20px;
	padding: 15px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
	position: relative;
}

.search-row {
	display: flex;
	margin-bottom: 10px;
	flex-wrap: wrap;
}

.search-item {
	display: flex;
	align-items: center;
	margin-right: 15px;
	margin-bottom: 5px;
}

.search-label {
	width: 90px;
	text-align: right;
	padding-right: 10px;
	color: #606266;
	font-size: 14px;
}

.search-item .el-input {
	width: 220px;
}

.search-item .el-select {
	width: 220px;
}

.date-range-item .el-date-editor {
	width: 320px;
}

.number-range-item .el-input-number {
	width: 100px;
}

.range-separator {
	margin: 0 5px;
	color: #606266;
}

.btn-item {
	margin-left: auto;
}

.search-btn,
.reset-btn,
.adv-search-btn {
	margin-left: 5px;
}

.action-bar {
	margin-bottom: 15px;
	background-color: #fff;
	padding: 10px;
	border-radius: 4px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
	display: flex;
	justify-content: space-between;
}

.action-left {
	display: flex;
	gap: 10px;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.book-pic-uploader {
	text-align: center;
}

.book-pic {
	width: 100px;
	height: 140px;
	display: block;
	object-fit: cover;
}

.book-pic-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 100px;
	height: 140px;
	line-height: 140px;
	text-align: center;
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
}

.book-pic-uploader-icon:hover {
	border-color: #409EFF;
}

.ellipsis-text {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.no-image {
	color: #909399;
}

.normal-text {
	color: #606266;
}

/* 单行省略文本 */
.ellipsis-text {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
  scrollbar-width: thin;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  border-radius: 3px;
  background: #c0c4cc;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  border-radius: 3px;
  background: #f5f7fa;
}

:deep(.el-table--scrollable-y .el-table__body-wrapper) {
  overflow-y: auto;
}

:deep(.el-table th) {
  height: 40px;
  padding: 8px 0;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 8px;
  height: 50px;
}

/* 无图片样式 */
.no-image {
  color: #909399;
  font-size: 12px;
}

/* 操作按钮样式 */
:deep(.el-button.is-text) {
  margin: 0 4px;
  padding: 0 6px;
}

/* 正常文本样式 */
.normal-text {
  color: #67C23A;
}

/* 修改表格斑马纹颜色 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

/* 适配图片布局 */
:deep(.el-table .cell) {
  padding: 0 8px;
}

/* 调整按钮区域 */
.action-bar {
  margin-bottom: 15px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 15px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* 增加图片预览时的z-index确保在最顶层 */
:deep(.el-image-viewer__wrapper) {
  z-index: 2100 !important;
}

/* 销量详情弹出框样式 */
.sales-detail {
  padding: 5px 0;
}

.sales-item {
  display: flex;
  justify-content: space-between;
  margin: 4px 0;
  font-size: 13px;
}

.sales-label {
  color: #606266;
  margin-right: 10px;
}

.sales-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  cursor: pointer;
  color: #409EFF;
}

/* 为弹出框添加自定义样式 */
:deep(.sales-popover) {
  max-width: 220px;
  padding: 8px 12px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
}

.disabled-uploader {
  background-color: #f5f7fa;
  cursor: not-allowed !important;
  color: #c0c4cc !important;
  border-color: #e4e7ed !important;
}

.disabled-uploader:hover {
  border-color: #e4e7ed !important;
}

/* 编辑模式下的上传区域样式 */
:deep(.el-upload.is-disabled) {
  cursor: not-allowed;
}

:deep(.el-upload.is-disabled:hover) {
  border-color: #e4e7ed !important;
}

.edit-disabled-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 违规设置对话框样式 */
.violation-config {
  padding: 20px 0;
}

.violation-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.violation-label {
  font-size: 14px;
  margin-left: 4px;
}

:deep(.violation-dialog .el-dialog__header) {
  padding: 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f9f9;
}

:deep(.violation-dialog .el-dialog__body) {
  padding: 20px 25px;
}

:deep(.violation-dialog .el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #f9f9f9;
}

:deep(.violation-dialog .el-checkbox__inner) {
  border-color: #dcdfe6;
}

:deep(.violation-dialog .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409EFF;
  border-color: #409EFF;
}

:deep(.violation-dialog .el-checkbox__label) {
  font-weight: normal;
}

/* 高级搜索相关样式 */
.toggle-advanced-search {
	text-align: center;
	margin-top: 5px;
	border-top: 1px dashed #ebeef5;
	padding-top: 8px;
}

.advanced-search-hidden {
	display: none;
}

.rotate-icon {
	transform: rotate(180deg);
	transition: transform 0.3s;
}
</style> 