import instance from '../../utils/axios.js'

// 邀请码相关API
const invitationApi = {
  // 获取邀请码列表
  getInviteCodes: async (params = {}) => {
    // 构造查询参数
    const queryParams = new URLSearchParams();
    
    // 添加分页参数
    if (params.pageNum) queryParams.append('pageNum', params.pageNum);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    
    // 添加搜索参数
    if (params.userId) queryParams.append('userId', params.userId);
    if (params.phonenumber) queryParams.append('phonenumber', params.phonenumber);
    
    const url = `/inviteCodes/erp/getInviteCodes?${queryParams.toString()}`;
    const response = await instance.get(url);
    // 确保返回的数据格式正确
    console.log('API原始响应:', response);
    return response;
  },
  
  // 根据邀请人ID获取被邀请人列表
  getInviteesByInviter: (inviterId) => {
    return instance.get(`/inviteCodes/inviteRelations/inviter?inviterId=${inviterId}`)
  }
};

// 导出API
export { invitationApi }; 