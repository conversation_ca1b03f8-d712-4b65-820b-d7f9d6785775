<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Configuration">
    <!--Will be overridden by value from command line, if provided-->
    <PlatformToolset Condition="$(VisualStudioVersion)=='12.0'">v120</PlatformToolset>
    <PlatformToolset Condition="$(VisualStudioVersion)=='14.0'">v140</PlatformToolset>
    <PlatformToolset Condition="$(VisualStudioVersion)=='15.0'">v141</PlatformToolset>
    <PlatformToolset Condition="$(VisualStudioVersion)=='16.0'">v142</PlatformToolset>
    <PlatformToolset Condition="$(VisualStudioVersion)=='17.0'">v143</PlatformToolset>
    <!--We may need the equivalent of PlatformToolsetVersion before it's ready, so create it ourself-->
    <LibusbPlatformToolsetVersion>$(PlatformToolset.Substring(1))</LibusbPlatformToolsetVersion>
    <CharacterSet>Unicode</CharacterSet>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <!-- To use ASAN, just uncomment this. For simplicity, you should run VS/windbg/etc
    (including the built executables themselves) after using vcvarsall or similar to setup
    environment, as ASAN needs access to libs and executables in the toolchain paths.
    -->
    <!--<EnableASAN>true</EnableASAN>-->
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile Condition="$(Configuration.StartsWith('Debug'))">
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <ClCompile Condition="$(Configuration.StartsWith('Release'))">
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Label="Globals" Condition="'$(LibusbPlatformToolsetVersion)'&lt;'142'">
    <!--
      WindowsSDKDesktopARMSupport and WindowsSDKDesktopARM64Support are
      required to enable downlevel Windows SDKs to build "desktop" arm/arm64
      binaries.
      -->
    <WindowsSDKDesktopARMSupport>true</WindowsSDKDesktopARMSupport>
    <WindowsSDKDesktopARM64Support>true</WindowsSDKDesktopARM64Support>
    <!--
      WindowsTargetPlatformVersion must be set to explicit value for older
      PlatformToolsets. Fetch it from environment variable.
      vs2017/arm64 needs to be special cased to select 10.x SDK instead of 8.x
    -->
    <WindowsTargetPlatformVersion Condition="'$(LibusbPlatformToolsetVersion)'=='141' And $(Platform)=='ARM64'">10.0.19041.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformVersion Condition="'$(LibusbPlatformToolsetVersion)'!='141' Or $(Platform)!='ARM64'">$(WindowsSDKVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <PropertyGroup Label="Globals" Condition="'$(LibusbPlatformToolsetVersion)'&gt;='142'">
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
</Project>