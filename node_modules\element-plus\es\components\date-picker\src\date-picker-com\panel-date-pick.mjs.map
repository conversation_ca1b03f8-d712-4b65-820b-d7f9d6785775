{"version": 3, "file": "panel-date-pick.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-date-pick.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      dpNs.b(),\n      {\n        'has-sidebar': $slots.sidebar || hasShortcuts,\n        'has-time': showTime,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div v-if=\"showTime\" :class=\"dpNs.e('time-header')\">\n          <span :class=\"dpNs.e('editor-wrap')\">\n            <el-input\n              :placeholder=\"t('el.datepicker.selectDate')\"\n              :model-value=\"visibleDate\"\n              size=\"small\"\n              :validate-event=\"false\"\n              @input=\"(val) => (userInputDate = val)\"\n              @change=\"handleVisibleDateChange\"\n            />\n          </span>\n          <span\n            v-click-outside=\"handleTimePickClose\"\n            :class=\"dpNs.e('editor-wrap')\"\n          >\n            <el-input\n              :placeholder=\"t('el.datepicker.selectTime')\"\n              :model-value=\"visibleTime\"\n              size=\"small\"\n              :validate-event=\"false\"\n              @focus=\"onTimePickerInputFocus\"\n              @input=\"(val) => (userInputTime = val)\"\n              @change=\"handleVisibleTimeChange\"\n            />\n            <time-pick-panel\n              :visible=\"timePickerVisible\"\n              :format=\"timeFormat\"\n              :parsed-value=\"innerDate\"\n              @pick=\"handleTimePick\"\n            />\n          </span>\n        </div>\n        <div\n          v-show=\"currentView !== 'time'\"\n          :class=\"[\n            dpNs.e('header'),\n            (currentView === 'year' || currentView === 'month') &&\n              dpNs.e('header--bordered'),\n          ]\"\n        >\n          <span :class=\"dpNs.e('prev-btn')\">\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.prevYear`)\"\n              class=\"d-arrow-left\"\n              :class=\"ppNs.e('icon-btn')\"\n              @click=\"moveByYear(false)\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-show=\"currentView === 'date'\"\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.prevMonth`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"arrow-left\"\n              @click=\"moveByMonth(false)\"\n            >\n              <slot name=\"prev-month\">\n                <el-icon><arrow-left /></el-icon>\n              </slot>\n            </button>\n          </span>\n          <span\n            role=\"button\"\n            :class=\"dpNs.e('header-label')\"\n            aria-live=\"polite\"\n            tabindex=\"0\"\n            @keydown.enter=\"showPicker('year')\"\n            @click=\"showPicker('year')\"\n            >{{ yearLabel }}</span\n          >\n          <span\n            v-show=\"currentView === 'date'\"\n            role=\"button\"\n            aria-live=\"polite\"\n            tabindex=\"0\"\n            :class=\"[\n              dpNs.e('header-label'),\n              { active: currentView === 'month' },\n            ]\"\n            @keydown.enter=\"showPicker('month')\"\n            @click=\"showPicker('month')\"\n            >{{ t(`el.datepicker.month${month + 1}`) }}</span\n          >\n          <span :class=\"dpNs.e('next-btn')\">\n            <button\n              v-show=\"currentView === 'date'\"\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextMonth`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"arrow-right\"\n              @click=\"moveByMonth(true)\"\n            >\n              <slot name=\"next-month\">\n                <el-icon><arrow-right /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextYear`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              @click=\"moveByYear(true)\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n          </span>\n        </div>\n        <div :class=\"ppNs.e('content')\" @keydown=\"handleKeydownTable\">\n          <date-table\n            v-if=\"currentView === 'date'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :parsed-value=\"parsedValue\"\n            :disabled-date=\"disabledDate\"\n            :cell-class-name=\"cellClassName\"\n            @pick=\"handleDatePick\"\n          />\n          <year-table\n            v-if=\"currentView === 'year'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :disabled-date=\"disabledDate\"\n            :parsed-value=\"parsedValue\"\n            @pick=\"handleYearPick\"\n          />\n          <month-table\n            v-if=\"currentView === 'month'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :parsed-value=\"parsedValue\"\n            :disabled-date=\"disabledDate\"\n            @pick=\"handleMonthPick\"\n          />\n        </div>\n      </div>\n    </div>\n    <div v-show=\"footerVisible\" :class=\"ppNs.e('footer')\">\n      <el-button\n        v-show=\"!isMultipleType && showNow\"\n        text\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"disabledNow\"\n        @click=\"changeToNow\"\n      >\n        {{ t('el.datepicker.now') }}\n      </el-button>\n      <el-button\n        plain\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"disabledConfirm\"\n        @click=\"onConfirm\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  ref,\n  toRef,\n  useAttrs,\n  useSlots,\n  watch,\n} from 'vue'\nimport dayjs from 'dayjs'\nimport ElButton from '@element-plus/components/button'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElInput from '@element-plus/components/input'\nimport {\n  TimePickPanel,\n  extractDateFormat,\n  extractTimeFormat,\n} from '@element-plus/components/time-picker'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { isArray, isFunction } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  DArrowLeft,\n  DArrowRight,\n} from '@element-plus/icons-vue'\nimport { TOOLTIP_INJECTION_KEY } from '@element-plus/components/tooltip'\nimport { panelDatePickProps } from '../props/panel-date-pick'\nimport {\n  correctlyParseUserInput,\n  getValidDateOfMonth,\n  getValidDateOfYear,\n} from '../utils'\nimport DateTable from './basic-date-table.vue'\nimport MonthTable from './basic-month-table.vue'\nimport YearTable from './basic-year-table.vue'\n\nimport type { SetupContext } from 'vue'\nimport type { ConfigType, Dayjs } from 'dayjs'\nimport type { PanelDatePickProps } from '../props/panel-date-pick'\nimport type {\n  DateTableEmits,\n  DatesPickerEmits,\n  MonthsPickerEmits,\n  WeekPickerEmits,\n  YearsPickerEmits,\n} from '../props/basic-date-table'\n\ntype DatePickType = PanelDatePickProps['type']\n// todo\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst timeWithinRange = (_: ConfigType, __: any, ___: string) => true\nconst props = defineProps(panelDatePickProps)\nconst contextEmit = defineEmits(['pick', 'set-picker-option', 'panel-change'])\nconst ppNs = useNamespace('picker-panel')\nconst dpNs = useNamespace('date-picker')\nconst attrs = useAttrs()\nconst slots = useSlots()\n\nconst { t, lang } = useLocale()\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst isDefaultFormat = inject('ElIsDefaultFormat') as any\nconst popper = inject(TOOLTIP_INJECTION_KEY)\nconst { shortcuts, disabledDate, cellClassName, defaultTime } = pickerBase.props\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\n\nconst currentViewRef = ref<{ focus: () => void }>()\n\nconst innerDate = ref(dayjs().locale(lang.value))\n\nconst isChangeToNow = ref(false)\n\nlet isShortcut = false\n\nconst defaultTimeD = computed(() => {\n  return dayjs(defaultTime).locale(lang.value)\n})\n\nconst month = computed(() => {\n  return innerDate.value.month()\n})\n\nconst year = computed(() => {\n  return innerDate.value.year()\n})\n\nconst selectableRange = ref([])\nconst userInputDate = ref<string | null>(null)\nconst userInputTime = ref<string | null>(null)\n// todo update to disableHour\nconst checkDateWithinRange = (date: ConfigType) => {\n  return selectableRange.value.length > 0\n    ? timeWithinRange(date, selectableRange.value, props.format || 'HH:mm:ss')\n    : true\n}\nconst formatEmit = (emitDayjs: Dayjs) => {\n  if (\n    defaultTime &&\n    !visibleTime.value &&\n    !isChangeToNow.value &&\n    !isShortcut\n  ) {\n    return defaultTimeD.value\n      .year(emitDayjs.year())\n      .month(emitDayjs.month())\n      .date(emitDayjs.date())\n  }\n  if (showTime.value) return emitDayjs.millisecond(0)\n  return emitDayjs.startOf('day')\n}\nconst emit = (value: Dayjs | Dayjs[], ...args: any[]) => {\n  if (!value) {\n    contextEmit('pick', value, ...args)\n  } else if (isArray(value)) {\n    const dates = value.map(formatEmit)\n    contextEmit('pick', dates, ...args)\n  } else {\n    contextEmit('pick', formatEmit(value), ...args)\n  }\n  userInputDate.value = null\n  userInputTime.value = null\n  isChangeToNow.value = false\n  isShortcut = false\n}\nconst handleDatePick = async (value: DateTableEmits, keepOpen?: boolean) => {\n  if (selectionMode.value === 'date') {\n    value = value as Dayjs\n    let newDate = props.parsedValue\n      ? (props.parsedValue as Dayjs)\n          .year(value.year())\n          .month(value.month())\n          .date(value.date())\n      : value\n    // change default time while out of selectableRange\n    if (!checkDateWithinRange(newDate)) {\n      newDate = (selectableRange.value[0][0] as Dayjs)\n        .year(value.year())\n        .month(value.month())\n        .date(value.date())\n    }\n    innerDate.value = newDate\n    emit(newDate, showTime.value || keepOpen)\n    // fix: https://github.com/element-plus/element-plus/issues/14728\n    if (props.type === 'datetime') {\n      await nextTick()\n      handleFocusPicker()\n    }\n  } else if (selectionMode.value === 'week') {\n    emit((value as WeekPickerEmits).date)\n  } else if (selectionMode.value === 'dates') {\n    emit(value as DatesPickerEmits, true) // set true to keep panel open\n  }\n}\n\nconst moveByMonth = (forward: boolean) => {\n  const action = forward ? 'add' : 'subtract'\n  innerDate.value = innerDate.value[action](1, 'month')\n  handlePanelChange('month')\n}\n\nconst moveByYear = (forward: boolean) => {\n  const currentDate = innerDate.value\n  const action = forward ? 'add' : 'subtract'\n\n  innerDate.value =\n    currentView.value === 'year'\n      ? currentDate[action](10, 'year')\n      : currentDate[action](1, 'year')\n\n  handlePanelChange('year')\n}\n\nconst currentView = ref('date')\n\nconst yearLabel = computed(() => {\n  const yearTranslation = t('el.datepicker.year')\n  if (currentView.value === 'year') {\n    const startYear = Math.floor(year.value / 10) * 10\n    if (yearTranslation) {\n      return `${startYear} ${yearTranslation} - ${\n        startYear + 9\n      } ${yearTranslation}`\n    }\n    return `${startYear} - ${startYear + 9}`\n  }\n  return `${year.value} ${yearTranslation}`\n})\n\ntype Shortcut = {\n  value: (() => Dayjs) | Dayjs\n  onClick?: (ctx: Omit<SetupContext, 'expose'>) => void\n}\n\nconst handleShortcutClick = (shortcut: Shortcut) => {\n  const shortcutValue = isFunction(shortcut.value)\n    ? shortcut.value()\n    : shortcut.value\n  if (shortcutValue) {\n    isShortcut = true\n    emit(dayjs(shortcutValue).locale(lang.value))\n    return\n  }\n  if (shortcut.onClick) {\n    shortcut.onClick({\n      attrs,\n      slots,\n      emit: contextEmit as SetupContext['emit'],\n    })\n  }\n}\n\nconst selectionMode = computed<DatePickType>(() => {\n  const { type } = props\n  if (['week', 'month', 'months', 'year', 'years', 'dates'].includes(type))\n    return type\n  return 'date' as DatePickType\n})\n\nconst isMultipleType = computed(() => {\n  return (\n    selectionMode.value === 'dates' ||\n    selectionMode.value === 'months' ||\n    selectionMode.value === 'years'\n  )\n})\n\nconst keyboardMode = computed<string>(() => {\n  return selectionMode.value === 'date'\n    ? currentView.value\n    : selectionMode.value\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst handleMonthPick = async (\n  month: number | MonthsPickerEmits,\n  keepOpen?: boolean\n) => {\n  if (selectionMode.value === 'month') {\n    innerDate.value = getValidDateOfMonth(\n      innerDate.value,\n      innerDate.value.year(),\n      month as number,\n      lang.value,\n      disabledDate\n    )\n    emit(innerDate.value, false)\n  } else if (selectionMode.value === 'months') {\n    emit(month as MonthsPickerEmits, keepOpen ?? true)\n  } else {\n    innerDate.value = getValidDateOfMonth(\n      innerDate.value,\n      innerDate.value.year(),\n      month as number,\n      lang.value,\n      disabledDate\n    )\n    currentView.value = 'date'\n    if (['month', 'year', 'date', 'week'].includes(selectionMode.value)) {\n      emit(innerDate.value, true)\n      await nextTick()\n      handleFocusPicker()\n    }\n  }\n  handlePanelChange('month')\n}\n\nconst handleYearPick = async (\n  year: number | YearsPickerEmits,\n  keepOpen?: boolean\n) => {\n  if (selectionMode.value === 'year') {\n    const data = innerDate.value.startOf('year').year(year as number)\n    innerDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    emit(innerDate.value, false)\n  } else if (selectionMode.value === 'years') {\n    emit(year as YearsPickerEmits, keepOpen ?? true)\n  } else {\n    const data = innerDate.value.year(year as number)\n    innerDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    currentView.value = 'month'\n    if (['month', 'year', 'date', 'week'].includes(selectionMode.value)) {\n      emit(innerDate.value, true)\n      await nextTick()\n      handleFocusPicker()\n    }\n  }\n  handlePanelChange('year')\n}\n\nconst showPicker = async (view: 'month' | 'year') => {\n  currentView.value = view\n  await nextTick()\n  handleFocusPicker()\n}\n\nconst showTime = computed(\n  () => props.type === 'datetime' || props.type === 'datetimerange'\n)\n\nconst footerVisible = computed(() => {\n  const showDateFooter = showTime.value || selectionMode.value === 'dates'\n  const showYearFooter = selectionMode.value === 'years'\n  const showMonthFooter = selectionMode.value === 'months'\n  const isDateView = currentView.value === 'date'\n  const isYearView = currentView.value === 'year'\n  const isMonthView = currentView.value === 'month'\n  return (\n    (showDateFooter && isDateView) ||\n    (showYearFooter && isYearView) ||\n    (showMonthFooter && isMonthView)\n  )\n})\n\nconst disabledConfirm = computed(() => {\n  if (!disabledDate) return false\n  if (!props.parsedValue) return true\n  if (isArray(props.parsedValue)) {\n    return disabledDate(props.parsedValue[0].toDate())\n  }\n  return disabledDate(props.parsedValue.toDate())\n})\nconst onConfirm = () => {\n  if (isMultipleType.value) {\n    emit(props.parsedValue as Dayjs[])\n  } else {\n    // deal with the scenario where: user opens the date time picker, then confirm without doing anything\n    let result = props.parsedValue as Dayjs\n    if (!result) {\n      const defaultTimeD = dayjs(defaultTime).locale(lang.value)\n      const defaultValueD = getDefaultValue()\n      result = defaultTimeD\n        .year(defaultValueD.year())\n        .month(defaultValueD.month())\n        .date(defaultValueD.date())\n    }\n    innerDate.value = result\n    emit(result)\n  }\n}\n\nconst disabledNow = computed(() => {\n  if (!disabledDate) return false\n  return disabledDate(dayjs().locale(lang.value).toDate())\n})\nconst changeToNow = () => {\n  // NOTE: not a permanent solution\n  //       consider disable \"now\" button in the future\n  const now = dayjs().locale(lang.value)\n  const nowDate = now.toDate()\n  isChangeToNow.value = true\n  if (\n    (!disabledDate || !disabledDate(nowDate)) &&\n    checkDateWithinRange(nowDate)\n  ) {\n    innerDate.value = dayjs().locale(lang.value)\n    emit(innerDate.value)\n  }\n}\n\nconst timeFormat = computed(() => {\n  return props.timeFormat || extractTimeFormat(props.format)\n})\n\nconst dateFormat = computed(() => {\n  return props.dateFormat || extractDateFormat(props.format)\n})\n\nconst visibleTime = computed(() => {\n  if (userInputTime.value) return userInputTime.value\n  if (!props.parsedValue && !defaultValue.value) return\n  return ((props.parsedValue || innerDate.value) as Dayjs).format(\n    timeFormat.value\n  )\n})\n\nconst visibleDate = computed(() => {\n  if (userInputDate.value) return userInputDate.value\n  if (!props.parsedValue && !defaultValue.value) return\n  return ((props.parsedValue || innerDate.value) as Dayjs).format(\n    dateFormat.value\n  )\n})\n\nconst timePickerVisible = ref(false)\nconst onTimePickerInputFocus = () => {\n  timePickerVisible.value = true\n}\nconst handleTimePickClose = () => {\n  timePickerVisible.value = false\n}\n\nconst getUnits = (date: Dayjs) => {\n  return {\n    hour: date.hour(),\n    minute: date.minute(),\n    second: date.second(),\n    year: date.year(),\n    month: date.month(),\n    date: date.date(),\n  }\n}\n\nconst handleTimePick = (value: Dayjs, visible: boolean, first: boolean) => {\n  const { hour, minute, second } = getUnits(value)\n  const newDate = props.parsedValue\n    ? (props.parsedValue as Dayjs).hour(hour).minute(minute).second(second)\n    : value\n  innerDate.value = newDate\n  emit(innerDate.value, true)\n  if (!first) {\n    timePickerVisible.value = visible\n  }\n}\n\nconst handleVisibleTimeChange = (value: string) => {\n  const newDate = dayjs(value, timeFormat.value).locale(lang.value)\n  if (newDate.isValid() && checkDateWithinRange(newDate)) {\n    const { year, month, date } = getUnits(innerDate.value)\n    innerDate.value = newDate.year(year).month(month).date(date)\n    userInputTime.value = null\n    timePickerVisible.value = false\n    emit(innerDate.value, true)\n  }\n}\n\nconst handleVisibleDateChange = (value: string) => {\n  const newDate = correctlyParseUserInput(\n    value,\n    dateFormat.value,\n    lang.value,\n    isDefaultFormat\n  ) as Dayjs\n  if (newDate.isValid()) {\n    if (disabledDate && disabledDate(newDate.toDate())) {\n      return\n    }\n    const { hour, minute, second } = getUnits(innerDate.value)\n    innerDate.value = newDate.hour(hour).minute(minute).second(second)\n    userInputDate.value = null\n    emit(innerDate.value, true)\n  }\n}\n\nconst isValidValue = (date: unknown) => {\n  return (\n    dayjs.isDayjs(date) &&\n    date.isValid() &&\n    (disabledDate ? !disabledDate(date.toDate()) : true)\n  )\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? (value as Dayjs[]).map((_) => _.format(props.format))\n    : (value as Dayjs).format(props.format)\n}\n\nconst parseUserInput = (value: Dayjs) => {\n  return correctlyParseUserInput(\n    value,\n    props.format,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst getDefaultValue = () => {\n  const parseDate = dayjs(defaultValue.value).locale(lang.value)\n  if (!defaultValue.value) {\n    const defaultTimeDValue = defaultTimeD.value\n    return dayjs()\n      .hour(defaultTimeDValue.hour())\n      .minute(defaultTimeDValue.minute())\n      .second(defaultTimeDValue.second())\n      .locale(lang.value)\n  }\n  return parseDate\n}\n\nconst handleFocusPicker = () => {\n  if (['week', 'month', 'year', 'date'].includes(selectionMode.value)) {\n    currentViewRef.value?.focus()\n  }\n}\n\nconst _handleFocusPicker = () => {\n  handleFocusPicker()\n  // TODO: After focus the date input, the first time you use the ArrowDown keys, you cannot focus on the date cell\n  if (selectionMode.value === 'week') {\n    handleKeyControl(EVENT_CODE.down)\n  }\n}\n\nconst handleKeydownTable = (event: KeyboardEvent) => {\n  const { code } = event\n  const validCode = [\n    EVENT_CODE.up,\n    EVENT_CODE.down,\n    EVENT_CODE.left,\n    EVENT_CODE.right,\n    EVENT_CODE.home,\n    EVENT_CODE.end,\n    EVENT_CODE.pageUp,\n    EVENT_CODE.pageDown,\n  ]\n  if (validCode.includes(code)) {\n    handleKeyControl(code)\n    event.stopPropagation()\n    event.preventDefault()\n  }\n  if (\n    [EVENT_CODE.enter, EVENT_CODE.space, EVENT_CODE.numpadEnter].includes(\n      code\n    ) &&\n    userInputDate.value === null &&\n    userInputTime.value === null\n  ) {\n    event.preventDefault()\n    emit(innerDate.value, false)\n  }\n}\n\nconst handleKeyControl = (code: string) => {\n  type KeyControlMappingCallableOffset = (date: Date, step?: number) => number\n  type KeyControl = {\n    [key: string]:\n      | number\n      | KeyControlMappingCallableOffset\n      | ((date: Date, step: number) => any)\n    offset: (date: Date, step: number) => any\n  }\n  interface KeyControlMapping {\n    [key: string]: KeyControl\n  }\n\n  const { up, down, left, right, home, end, pageUp, pageDown } = EVENT_CODE\n  const mapping: KeyControlMapping = {\n    year: {\n      [up]: -4,\n      [down]: 4,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setFullYear(date.getFullYear() + step),\n    },\n    month: {\n      [up]: -4,\n      [down]: 4,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setMonth(date.getMonth() + step),\n    },\n    week: {\n      [up]: -1,\n      [down]: 1,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setDate(date.getDate() + step * 7),\n    },\n    date: {\n      [up]: -7,\n      [down]: 7,\n      [left]: -1,\n      [right]: 1,\n      [home]: (date: Date) => -date.getDay(),\n      [end]: (date: Date) => -date.getDay() + 6,\n      [pageUp]: (date: Date) =>\n        -new Date(date.getFullYear(), date.getMonth(), 0).getDate(),\n      [pageDown]: (date: Date) =>\n        new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate(),\n      offset: (date: Date, step: number) => date.setDate(date.getDate() + step),\n    },\n  }\n\n  const newDate = innerDate.value.toDate()\n  while (Math.abs(innerDate.value.diff(newDate, 'year', true)) < 1) {\n    const map = mapping[keyboardMode.value]\n    if (!map) return\n    map.offset(\n      newDate,\n      isFunction(map[code])\n        ? (map[code] as unknown as KeyControlMappingCallableOffset)(newDate)\n        : (map[code] as number) ?? 0\n    )\n    if (disabledDate && disabledDate(newDate)) {\n      break\n    }\n    const result = dayjs(newDate).locale(lang.value)\n    innerDate.value = result\n    contextEmit('pick', result, true)\n    break\n  }\n}\n\nconst handlePanelChange = (mode: 'month' | 'year') => {\n  contextEmit('panel-change', innerDate.value.toDate(), mode, currentView.value)\n}\n\nwatch(\n  () => selectionMode.value,\n  (val) => {\n    if (['month', 'year'].includes(val)) {\n      currentView.value = val\n      return\n    } else if (val === 'years') {\n      currentView.value = 'year'\n      return\n    } else if (val === 'months') {\n      currentView.value = 'month'\n      return\n    }\n    currentView.value = 'date'\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => currentView.value,\n  () => {\n    popper?.updatePopper()\n  }\n)\n\nwatch(\n  () => defaultValue.value,\n  (val) => {\n    if (val) {\n      innerDate.value = getDefaultValue()\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.parsedValue,\n  (val) => {\n    if (val) {\n      if (isMultipleType.value) return\n      if (isArray(val)) return\n      innerDate.value = val\n    } else {\n      innerDate.value = getDefaultValue()\n    }\n  },\n  { immediate: true }\n)\n\ncontextEmit('set-picker-option', ['isValidValue', isValidValue])\ncontextEmit('set-picker-option', ['formatToString', formatToString])\ncontextEmit('set-picker-option', ['parseUserInput', parseUserInput])\ncontextEmit('set-picker-option', ['handleFocusPicker', _handleFocusPicker])\n</script>\n"], "names": ["month", "year", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_renderSlot", "_openBlock", "_Fragment", "_renderList", "_toDisplayString", "_createCommentVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyPA,IAAA,MAAM,eAAkB,GAAA,CAAC,CAAe,EAAA,EAAA,EAAS,GAAgB,KAAA,IAAA,CAAA;AAGjE,IAAM,MAAA,IAAA,GAAO,aAAa,cAAc,CAAA,CAAA;AACxC,IAAM,MAAA,IAAA,GAAO,aAAa,aAAa,CAAA,CAAA;AACvC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AACvB,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC9B,IAAM,MAAA,UAAA,GAAa,OAAO,gBAAgB,CAAA,CAAA;AAC1C,IAAM,MAAA,eAAA,GAAkB,OAAO,mBAAmB,CAAA,CAAA;AAClD,IAAM,MAAA,MAAA,GAAS,OAAO,qBAAqB,CAAA,CAAA;AAC3C,IAAA,MAAM,EAAE,SAAW,EAAA,YAAA,EAAc,aAAe,EAAA,WAAA,KAAgB,UAAW,CAAA,KAAA,CAAA;AAC3E,IAAA,MAAM,YAAe,GAAA,KAAA,CAAM,UAAW,CAAA,KAAA,EAAO,cAAc,CAAA,CAAA;AAE3D,IAAA,MAAM,iBAAiB,GAA2B,EAAA,CAAA;AAElD,IAAA,MAAM,YAAY,GAAI,CAAA,KAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AAEhD,IAAM,MAAA,aAAA,GAAgB,IAAI,KAAK,CAAA,CAAA;AAE/B,IAAA,IAAI,UAAa,GAAA,KAAA,CAAA;AAEjB,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,OAAO,KAAM,CAAA,WAAW,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AAAA,KAC5C,CAAA,CAAA;AAED,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAO,OAAA,SAAA,CAAU,MAAM,KAAM,EAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAO,SAAS,MAAM;AAC1B,MAAO,OAAA,SAAA,CAAU,MAAM,IAAK,EAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkB,GAAI,CAAA,EAAE,CAAA,CAAA;AAC9B,IAAM,MAAA,aAAA,GAAgB,IAAmB,IAAI,CAAA,CAAA;AAC7C,IAAM,MAAA,aAAA,GAAgB,IAAmB,IAAI,CAAA,CAAA;AAE7C,IAAM,MAAA,oBAAA,GAAuB,CAAC,IAAqB,KAAA;AACjD,MAAO,OAAA,eAAA,CAAgB,KAAM,CAAA,MAAA,GAAS,CAClC,GAAA,eAAA,CAAgB,IAAM,EAAA,eAAA,CAAgB,KAAO,EAAA,KAAA,CAAM,MAAU,IAAA,UAAU,CACvE,GAAA,IAAA,CAAA;AAAA,KACN,CAAA;AACA,IAAM,MAAA,UAAA,GAAa,CAAC,SAAqB,KAAA;AACvC,MACE,IAAA,WAAA,IACA,CAAC,WAAY,CAAA,KAAA,IACb,CAAC,aAAc,CAAA,KAAA,IACf,CAAC,UACD,EAAA;AACA,QAAA,OAAO,YAAa,CAAA,KAAA,CACjB,IAAK,CAAA,SAAA,CAAU,MAAM,CAAA,CACrB,KAAM,CAAA,SAAA,CAAU,OAAO,CAAA,CACvB,IAAK,CAAA,SAAA,CAAU,MAAM,CAAA,CAAA;AAAA,OAC1B;AACA,MAAA,IAAI,QAAS,CAAA,KAAA;AACb,QAAO,OAAA,qBAAuB,CAAA,CAAA,CAAA,CAAA;AAAA,MAChC,OAAA,SAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAM,CAAA;AACJ,IAAA,MAAI,IAAQ,GAAA,CAAA,KAAA,EAAA,GAAA,IAAA,KAAA;AACV,MAAY,IAAA,CAAA,KAAA,EAAA;AAAsB,QACpC,WAAmB,CAAA,MAAA,EAAA,KAAQ,EAAA,GAAA,IAAA,CAAA,CAAA;AACzB,OAAM,MAAA,IAAA,OAAc,CAAA,KAAA,CAAA,EAAc;AAClC,QAAY,MAAA,KAAA,GAAA,KAAA,CAAQ,GAAO,CAAA,UAAO,CAAA,CAAA;AAAA,QAC7B,WAAA,CAAA,MAAA,EAAA,KAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AACL,OAAA,MAAA;AAA8C,QAChD,WAAA,CAAA,MAAA,EAAA,UAAA,CAAA,KAAA,CAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AACA,OAAA;AACA,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,MAAa,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,MACf,UAAA,GAAA,KAAA,CAAA;AACA,KAAM,CAAA;AACJ,IAAI,MAAA,cAAc,UAAU,KAAQ,EAAA,QAAA,KAAA;AAClC,MAAQ,IAAA,aAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACR,QAAA,cAAc;AAOd,QAAI,IAAA,OAAsB,GAAA,KAAA,CAAA,WAAA,GAAU,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA;AAClC,QAAA,IAAA,CAAA,4BAAiC,CAAA;AAGb,UACtB,OAAA,GAAA,eAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACA,SAAA;AACA,QAAK,SAAA,CAAA,KAAkB,GAAA,OAAA,CAAA;AAEvB,QAAI,IAAA,CAAA,iBAA2B,CAAA,KAAA,IAAA,QAAA,CAAA,CAAA;AAC7B,QAAA,IAAA,KAAe,CAAA,IAAA,KAAA,UAAA,EAAA;AACf,UAAkB,MAAA,QAAA,EAAA,CAAA;AAAA,UACpB,iBAAA,EAAA,CAAA;AAAA,SACF;AACE,OAAA,MAAM,iBAA8B,CAAA,KAAA,KAAA,MAAA,EAAA;AAAA,QACtC,IAAA,CAAA,KAAyB,CAAA,IAAA,CAAA,CAAA;AACvB,OAAA,MAAK,iBAA+B,CAAA,KAAA,KAAA,OAAA,EAAA;AAAA,QACtC,IAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,WAAS,WAAkB,KAAA;AACjC,MAAA,MAAA,gBAA4B,GAAA,KAAA,GAAA;AAC5B,MAAA,SAAA,CAAA,KAAA,GAAkB,SAAO,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA;AAAA,MAC3B,iBAAA,CAAA,OAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,qBAA8B,KAAA;AAC9B,MAAM,MAAA,uBAA2B,CAAA,KAAA,CAAA;AAEjC,MAAA,MAAA,MACE,GAAA,OAAA,GAAA,KAAY,GAAU,UAAA,CAAA;AAIxB,MAAA,SAAA,CAAA,KAAA,GAAkB,WAAM,CAAA,KAAA,KAAA,MAAA,GAAA,WAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,WAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AAAA,MAC1B,iBAAA,CAAA,MAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AAEN,IAAM,MAAA,WAAA,aAA2B,CAAA,CAAA;AAC/B,IAAM,MAAA,SAAA,GAAA,QAAkB,OAAsB;AAC9C,MAAI,MAAA,oBAAsB,oBAAQ,CAAA,CAAA;AAChC,MAAA,IAAA,iBAAuB,KAAA,MAAW,EAAA;AAClC,QAAA,MAAqB,SAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,GAAA,EAAA,CAAA;AACnB,QAAO,IAAA,iBAAY;AAEA,UACrB,OAAA,CAAA,EAAA,SAAA,CAAA,CAAA,EAAA,eAAA,CAAA,GAAA,EAAA,SAAA,GAAA,CAAA,CAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AACA,SAAA;AAAsC,QACxC,OAAA,CAAA,EAAA,SAAA,CAAA,GAAA,EAAA,SAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AAAuC,MACxC,OAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AAOD,KAAM,CAAA,CAAA;AACJ,IAAM,MAAA,+BAAoC,KAAA;AAG1C,MAAA,MAAmB,aAAA,GAAA,UAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA;AACjB,MAAa,IAAA,aAAA,EAAA;AACb,QAAA,aAAwB,IAAA,CAAA;AACxB,QAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,QACF,OAAA;AACA,OAAA;AACE,MAAA,IAAA,QAAiB,CAAA,OAAA,EAAA;AAAA,QACf,QAAA,CAAA,OAAA,CAAA;AAAA,UACA,KAAA;AAAA,UACA,KAAM;AAAA,UACP,IAAA,EAAA,WAAA;AAAA,SACH,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,aAAW,GAAA,QAAA,CAAA,MAAA;AACjB,MAAI,cAAkB,GAAA,KAAA,CAAA;AACpB,MAAO,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACT,QAAO,OAAA,IAAA,CAAA;AAAA,MACR,OAAA,MAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,iBACgB,QAAU,CAAA,MAAA;AAEA,MAE3B,OAAA,aAAA,CAAA,KAAA,KAAA,OAAA,IAAA,aAAA,CAAA,KAAA,KAAA,QAAA,IAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,YAAqB,GAAA,QAAA,CAAA,MACjB;AACc,MACnB,OAAA,aAAA,CAAA,KAAA,KAAA,MAAA,GAAA,WAAA,CAAA,KAAA,GAAA,aAAA,CAAA,KAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,YAAA,GAAA,QACJA,CAAAA,MAAAA,CAAAA,CAAAA,SAEG,CAAA,MAAA,CAAA,CAAA;AACH,IAAI,MAAA,yBAAiC,MAAA,EAAA,QAAA,KAAA;AACnC,MAAA,IAAA,aAAkB,CAAA,KAAA,KAAA,OAAA,EAAA;AAAA,QAAA,SACN,CAAA,KAAA,GAAA,mBAAA,CAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,IAAA,EAAA,EAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAAA,QACV,IAAA,CAAA,eAAqB,EAAA,KAAA,CAAA,CAAA;AAAA,OACrBA,MAAAA,IAAAA,aAAAA,CAAAA,KAAAA,KAAAA,QAAAA,EAAAA;AAAA,QAAA,IACK,CAAA,MAAA,EAAA,QAAA,IAAA,IAAA,GAAA,QAAA,GAAA,IAAA,CAAA,CAAA;AAAA,OACL,MAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,mBAAA,CAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,IAAA,EAAA,EAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AACA,QAAK,WAAA,CAAA,cAAsB,CAAA;AAAA,QAC7B,IAAA,CAAA,OAAyB,EAAA,MAAA,EAAA,MAAA,EAAA,MAAoB,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AAC3C,UAAKA,IAAAA,CAAAA,qBAA4C,CAAA,CAAA;AAAA,UAC5C,MAAA,QAAA,EAAA,CAAA;AACL,UAAA,iBAAkB,EAAA,CAAA;AAAA,SAAA;AACN,OACV;AAAqB,MACrBA,iBAAAA,CAAAA,OAAAA,CAAAA,CAAAA;AAAA,KAAA,CAAA;AACK,IACL,MAAA,cAAA,GAAA,OAAA,KAAA,EAAA,QAAA,KAAA;AAAA,MACF,IAAA,aAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,QAAA,MAAA,IAAA,GAAoB,SAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACpB,QAAI,eAAkB,GAAA,kBAAc,CAAE,IAAA,EAAA,IAAS,CAAc,KAAA,EAAA,YAAQ,CAAA,CAAA;AACnE,QAAK,IAAA,CAAA,SAAA,CAAA,YAAqB,CAAA,CAAA;AAC1B,OAAA,MAAA,IAAe,aAAA,CAAA,KAAA,KAAA,OAAA,EAAA;AACf,QAAkB,IAAA,CAAA,KAAA,EAAA,QAAA,IAAA,IAAA,GAAA,QAAA,GAAA,IAAA,CAAA,CAAA;AAAA,OACpB,MAAA;AAAA,QACF,MAAA,IAAA,GAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,QAAA,SAAA,CAAA,KAAA,GAAyB,kBAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAAA,QAC3B,WAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAEA,QAAM,IAAA,CAAA,OAAA,EAAA,MACJC,EAAAA,MAAAA,EAAAA,MAEG,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AACH,UAAI,IAAA,CAAA,SAAc,YAAkB,CAAA,CAAA;AAClC,UAAA,cAAuB,EAAA,CAAA;AACvB,UAAA,iBAAkB,EAAA,CAAA;AAClB,SAAK;AAAsB,OAC7B;AACE,MAAKA,0BAAsC;AAAI,KAAA,CACjD;AACE,IAAA,MAAA,UAAa,GAAA,OAAA,IAAgB,KAAA;AAC7B,MAAA,WAAA,CAAU,KAAQ,GAAA,IAAA,CAAA;AAClB,MAAA,MAAA,QAAY,EAAQ,CAAA;AACpB,MAAI,iBAAkB,EAAA,CAAA;AACpB,KAAK,CAAA;AACL,IAAA,MAAA,QAAe,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,UAAA,IAAA,KAAA,CAAA,IAAA,KAAA,eAAA,CAAA,CAAA;AACf,IAAkB,MAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACpB,MAAA,cAAA,GAAA,QAAA,CAAA,KAAA,IAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAAA,MACF,MAAA,cAAA,GAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AACA,MAAA,MAAA,eAAwB,GAAA,aAAA,CAAA,KAAA,KAAA,QAAA,CAAA;AAAA,MAC1B,MAAA,UAAA,GAAA,WAAA,CAAA,KAAA,KAAA,MAAA,CAAA;AAEA,MAAM,MAAA,UAAA,cAA+C,CAAA,KAAA,KAAA,MAAA,CAAA;AACnD,MAAA,MAAA,WAAoB,GAAA,WAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AACpB,MAAA,OAAe,cAAA,IAAA,UAAA,IAAA,cAAA,IAAA,UAAA,IAAA,eAAA,IAAA,WAAA,CAAA;AACf,KAAkB,CAAA,CAAA;AAAA,IACpB,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAA,IAAM,CAAW,YAAA;AAAA,QACT,OAAA,KAAe,CAAA;AAA6B,MACpD,IAAA,CAAA,KAAA,CAAA,WAAA;AAEA,QAAM,OAAA,IAAA,CAAA;AACJ,MAAA,IAAA,OAAuB,CAAA,KAAA,CAAA,WAAA,CAAA,EAAS;AAChC,QAAM,OAAA,YAAA,CAAA,iBAAyC,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AAC/C,OAAM;AACN,MAAM,OAAA,YAAa,kBAAsB,CAAA,MAAA,EAAA,CAAA,CAAA;AACzC,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,SAAA,GAAA;AACN,MAAA,IAAA,cACqB,CAAA,KAAA,EAAA;AAEC,QAEvB,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AAED,OAAM,MAAA;AACJ,QAAI,kBAAsB,CAAA,WAAA,CAAA;AAC1B,QAAI,IAAO,CAAA,MAAA,EAAA;AACX,UAAI,MAAA,aAAc,GAAA,KAAc,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAC9B,UAAA,mBAA0B,GAAA,eAAa,EAAE;AAAQ,UACnD,MAAA,GAAA,aAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACA,SAAA;AAA8C,QAC/C,SAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AACD,QAAA;AACE,OAAA;AACE,KAAA,CAAA;AAAiC,IAAA,MAC5B,WAAA,GAAA,QAAA,CAAA,MAAA;AAEL,MAAA,IAAA,CAAA,YAAmB;AACnB,QAAA,OAAa,KAAA,CAAA;AACX,MAAA,OAAA,oBAA2B,CAAA,MAAA,CAAA,IAAA,CAAA,KAAa,CAAA,CAAA,UAAY;AACpD,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,WACG,GAAA,MAAA;AAEyB,MAC9B,MAAA,GAAA,GAAA,KAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAA,MAAA,OAAkB,GAAA,GAAA,CAAA,MAAA,EAAA,CAAA;AAClB,MAAA,aAAW,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,MACb,IAAA,CAAA,CAAA,YAAA,IAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,oBAAA,CAAA,OAAA,CAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,KAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAEA,QAAM,IAAA,CAAA,SAAA,CAAA;AACJ,OAAI;AACJ,KAAO,CAAA;AAAgD,IACzD,MAAC,UAAA,GAAA,QAAA,CAAA,MAAA;AACD,MAAA,uBAA0B,IAAA,iBAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAGxB,KAAA,CAAA,CAAA;AACA,IAAM,MAAA,UAAA,WAAqB,CAAA,MAAA;AAC3B,MAAA,OAAA,KAAA,CAAA,UAAsB,IAAA,iBAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACtB,KACG,CAAA,CAAA;AAGD,IAAA,MAAA,WAAkB,GAAA,QAAM,CAAE,MAAA;AAC1B,MAAA,IAAA,aAAe,CAAK,KAAA;AAAA,QACtB,OAAA,aAAA,CAAA,KAAA,CAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,WAAA,IAAA,CAAA,YAAA,CAAA,KAAA;AAEA,QAAM,OAAA;AACJ,MAAA,OAAO,CAAM,KAAA,CAAA,WAAA,IAAgC,SAAA,CAAA,KAAA,EAAA,MAAY,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAC1D,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,WAAsB,CAAM,MAAA;AAChC,MAAA,IAAA,aAAa,CAAA,KAAA;AAA4C,QAC1D,OAAA,aAAA,CAAA,KAAA,CAAA;AAED,MAAM,IAAA,CAAA,KAAA,CAAA,gBAAuB,YAAM,CAAA,KAAA;AACjC,QAAI,OAAA;AACJ,MAAA,OAAK,CAAA,KAAqB,CAAA,WAAA,mBAAqB,EAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAC/C,KAAS,CAAA,CAAA;AAAgD,IAAA,MAC5C,iBAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACb,MAAA,sBAAA,GAAA,MAAA;AAAA,MACD,iBAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAED,KAAM,CAAA;AACJ,IAAI,MAAA,mBAAqB,GAAA,MAAO;AAChC,MAAA,iBAA0B,CAAA,KAAA,GAAA;AAC1B,KAAS,CAAA;AAAgD,IAAA,MAC5C,QAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACb,OAAA;AAAA,QACD,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA;AAED,QAAM,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA;AACN,QAAA;AACE,QAAA,IAAA,EAAA,IAAA,CAAA,IAAA,EAA0B;AAAA,QAC5B,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA;AACA,QAAA;AACE,OAAA,CAAA;AAA0B,KAC5B,CAAA;AAEA,IAAM,MAAA,cAA4B,GAAA,CAAA,KAAA,EAAA,OAAA,EAAA,KAAA,KAAA;AAChC,MAAO,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACL,MAAA,OAAW,GAAK,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,KAAA,CAAA;AAAA,MAChB,SAAA,CAAQ,KAAK,GAAO,OAAA,CAAA;AAAA,MACpB,IAAA,CAAA,UAAa,KAAO,EAAA,IAAA,CAAA,CAAA;AAAA,MACpB,IAAA,CAAA;AAAgB,QAChB,iBAAkB,CAAA,KAAA,GAAA,OAAA,CAAA;AAAA,OAClB;AAAgB,KAClB,CAAA;AAAA,IACF,MAAA,uBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAA,MAAuB,OAAA,GAAA,KAAA,CAAC,KAAc,EAAA,UAAqC,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACzE,MAAA,IAAA,OAAc,CAAA,OAAA,EAAA,IAAe,oBAAkB,CAAA,OAAA,CAAA,EAAA;AAC/C,QAAA,MAAgB,EAAA,IAAA,EAAA,KAAA,EACX,KAAA,EAAA,MAAA,EAAA,IAAM,EAAsB,GAAA,QAAA,CAAA,SAAW,CAAO,KAAA,CAAA,CAAA;AAEnD,QAAA,SAAkB,CAAA,KAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAClB,QAAK,aAAU,SAAW,IAAA,CAAA;AAC1B,QAAA,iBAAY,CAAA,KAAA,GAAA,KAAA,CAAA;AACV,QAAA,IAAA,CAAA,SAAA,CAAA,KAA0B,EAAA,IAAA,CAAA,CAAA;AAAA,OAC5B;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,uBAAA,GAA0B,CAAC,KAAkB,KAAA;AACjD,MAAM,MAAA,OAAA,GAAU,uBAAa,CAAW,KAAK,EAAE,gBAAiB,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAChE,MAAA,IAAI,OAAQ,CAAA,OAAA,EAAa,EAAA;AACvB,QAAM,IAAA,YAAQ,IAAA,oBAAY,CAAA,MAAa,EAAA,CAAA,EAAA;AACvC,UAAU,OAAA;AACV,SAAA;AACA,QAAA,MAAA,EAAA,IAAA,EAAA,MAA0B,EAAA,MAAA,EAAA,GAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAC1B,QAAK,SAAA,CAAA,KAAU,UAAW,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA;AAAA,QAC5B,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,QACF,IAAA,CAAA,SAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAEA,OAAM;AACJ,KAAA,CAAA;AAAgB,IACd,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MAAA,OACW,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,OAAA,EAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACN,IACL,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,OAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,KAAI,CAAA;AACF,IAAA,MAAA,cAAoB,GAAA,CAAA,KAAA,KAAA;AAClB,MAAA,OAAA,uBAAA,CAAA,KAAA,EAAA,KAAA,CAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAAA,KACF,CAAA;AACA,IAAA,MAAA,eAAc,GAAA;AACd,MAAU,MAAA,SAAA,GAAA,kBAAqB,CAAI,KAAS,CAAA,CAAA,MAAA,CAAA,IAAQ,CAAA,KAAO,CAAM,CAAA;AACjE,MAAA,IAAA,CAAA,YAAsB,CAAA,KAAA,EAAA;AACtB,QAAK,MAAA,iBAAqB,GAAA,YAAA,CAAA,KAAA,CAAA;AAAA,QAC5B,OAAA,KAAA,EAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACF;AAEA,MAAM,OAAA,SAAA,CAAA;AACJ,KAAA,CAAA;AAGiD,IAEnD,MAAA,iBAAA,GAAA,MAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAA,IAAA,CAAA,eAAoB,EAAA,MACG,EAAA,MAAW,CAAA,CAAA,QAAS,CAAA,mBAAa,CACnD,EAAgB;AAAmB,QAC1C,CAAA,EAAA,GAAA,cAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAEA,OAAM;AACJ,KAAO,CAAA;AAAA,IACL,MAAA,kBAAA,GAAA,MAAA;AAAA,MAAA,iBACM,EAAA,CAAA;AAAA,MAAA,IACD,aAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AAAA,QACL,gBAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,kBAAkB,GAAM,CAAA,KAAA,KAAA;AAC5B,MAAA,MAAM,gBAAkB,CAAA;AACxB,MAAI,kBAAc;AAChB,QAAA;AACA,QAAA,eACG;AAGiB,QACtB,UAAA,CAAA,IAAA;AACA,QAAO,UAAA,CAAA,KAAA;AAAA,QACT,UAAA,CAAA,IAAA;AAEA,QAAA;AACE,QAAI,WAAS,MAAS;AACpB,QAAA,UAAA,CAAA;AAA4B,OAC9B,CAAA;AAAA,MACF,IAAA,SAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AAEA,QAAA,uBAA2B;AACzB,QAAkB,KAAA,CAAA,eAAA,EAAA,CAAA;AAElB,QAAI,KAAA,CAAA;AACF,OAAA;AAAgC,MAClC,IAAA,CAAA,UAAA,CAAA,KAAA,EAAA,UAAA,CAAA,KAAA,EAAA,UAAA,CAAA,WAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AAAA,QACF,KAAA,CAAA,cAAA,EAAA,CAAA;AAEA,QAAM,IAAA,CAAA,SAAA,CAAA,KAAA,EAAA,KAA+C,CAAA,CAAA;AACnD,OAAM;AACN,KAAA,CAAA;AAAkB,IAAA,MACL,gBAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MAAA,IACA,EAAA,CAAA;AAAA,MAAA,MACA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,MAAA,EAAA,QAAA,EAAA,GAAA,UAAA,CAAA;AAAA,MAAA,MACA,OAAA,GAAA;AAAA,QACX,IAAW,EAAA;AAAA,UACA,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,UACA,CAAA,IAAA,GAAA,CAAA;AAAA,UACA,CAAA,IAAA,GAAA,CAAA,CAAA;AAAA,UACb,CAAA,KAAA,GAAA,CAAA;AACA,UAAI,MAAA,EAAA,CAAA,IAAmB,EAAA,IAAA,KAAO,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,EAAA,GAAA,IAAA,CAAA;AAC5B,SAAA;AACA,QAAA,KAAA,EAAsB;AACtB,UAAA,CAAA,EAAA,GAAqB,CAAA,CAAA;AAAA,UACvB,CAAA,IAAA,GAAA,CAAA;AACA,UACE,CAAC,IAAW,GAAA,CAAA,CAAA;AAAiD,UAC3D,CAAA,KAAA,GAAA,CAAA;AAAA,gBAEY,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IACd,CAAA,QAAA,CAAA,IAAA,CAAA,iBACA,CAAA;AACA,SAAA;AACA,QAAK,IAAA,EAAA;AAAsB,UAC7B,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,UACF,CAAA,IAAA,GAAA,CAAA;AAEA,UAAM,CAAA,IAAA,GAAA,CAAA,CAAA;AAaJ,UAAM,CAAA,QAAY,CAAA;AAClB,UAAA,MAAmC,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,GAAA,CAAA,CAAA;AAAA,SAC3B;AAAA,QACJ,MAAM;AAAA,UACN,CAAC,KAAO,CAAA,CAAA;AAAA,UACR,CAAC,IAAI,GAAG,CAAA;AAAA,UACR,CAAC,OAAQ,CAAA,CAAA;AAAA,UACT,CAAA,KAAA,GAAS,CAAY;AACuB,UAC9C,CAAA,IAAA,GAAA,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAAA,UACO,CAAA,GAAA,GAAA,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA,GAAA,CAAA;AAAA,UACL,CAAC,MAAK,GAAA,CAAA,IAAA,KAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,IAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,EAAA;AAAA,UACN,CAAC,QAAO,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,IAAA,CAAA,QAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,EAAA;AAAA,UACR,MAAQ,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,CAAA;AAAA,SACR;AAAS,OACT,CAAA;AACsC,MACxC,MAAA,OAAA,GAAA,SAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA;AAAA,MAAA,OACM,IAAA,CAAA,GAAA,CAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA,GAAA,CAAA,EAAA;AAAA,QACJ,MAAM,GAAA,GAAA,OAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACN,QAAQ;AAAA,UACR,OAAQ;AAAA,QACR,GAAC,OAAQ,CAAA,OAAA,EAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AAAA,QACT,IAAA,YAAS,IACP,oBAAkB,CAAA,EAAA;AAAoB,UAC1C,MAAA;AAAA,SACM;AAAA,QACJ,MAAM,MAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACN,SAAQ,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,QACR,WAAQ,CAAA,MAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAAA,QACR;AAAS,OAAA;AAC4B,KACrC,CAAA;AAAwC,IAAA,MACxC,iBAAW,GACT,CAAC,IAAI,KAAK;AAAgD,MAAA,WACnD,CAAA,cACP,WAAS,CAAA,KAAiB,CAAA,MAAA,EAAA,EAAA,MAAQ,WAAS,CAAA,KAAQ,EAAE;AAAQ,KAC/D,CAAA;AAAwE,IAC1E,KAAA,CAAA,MAAA,aAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACF,IAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AAEA,QAAM,WAAA,CAAA,KAAoB,GAAA,GAAA,CAAA;AAC1B,QAAO,OAAA;AACL,OAAM,MAAA,IAAA,GAAM,KAAQ,OAAA,EAAA;AACpB,QAAA,WAAU,CAAA,KAAA,GAAA,MAAA,CAAA;AACV,QAAI,OAAA;AAAA,OACF,MAAA,IAAA,GAAA,KAAA,QAAA,EAAA;AAAA,QAAA,WACW,CAAA,KAAI,GAAI,OACd,CAAA;AACwB,QAC/B,OAAA;AACA,OAAI;AACF,MAAA,WAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,KACF,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACA,IAAA,KAAA,CAAA,iBAAqB,CAAA,KAAA,EAAA,MAAS;AAC9B,MAAA,MAAA,IAAA,IAAkB,GAAA,KAAA,CAAA,GAAA,MAAA,CAAA,YAAA,EAAA,CAAA;AAClB,KAAY,CAAA,CAAA;AACZ,IAAA,KAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACF,IAAA,GAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,eAAA,EAAA,CAAA;AAEA,OAAM;AACJ,KAAA,EAAA,EAAA,SAAY;AAAiE,IAC/E,KAAA,CAAA,MAAA,KAAA,CAAA,WAAA,EAAA,CAAA,GAAA,KAAA;AAEA,MAAA,IAAA,GAAA,EAAA;AAAA,YACQ,cAAc,CAAA,KAAA;AAAA,UACX,OAAA;AACP,QAAA,IAAI,OAAU,CAAA,GAAA,CAAA;AACZ,UAAA,OAAA;AACA,QAAA,SAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAAA,OACF,MAAA;AACE,QAAA,SAAA,CAAA,KAAoB,GAAA,eAAA,EAAA,CAAA;AACpB,OAAA;AAAA,KACF,EAAA,EAAA,SAAA,SAAmB,CAAU;AAC3B,IAAA,WAAA,CAAA,mBAAoB,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AACpB,IAAA,WAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IACF,WAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AACA,IAAA,WAAA,CAAA,mBAAoB,EAAA,CAAA,mBAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;AAAA,IACtB,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACA,gBAAkB,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QACpB,KAAA,EAAAC,cAAA,CAAA;AAEA,UAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AAAA,eACoB,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AAAA,UACZ;AACJ,YAAA,aAAqB,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAAA,KAAA,CAAA,YAAA,CAAA;AAAA,YACvB,UAAA,EAAAA,KAAA,CAAA,QAAA,CAAA;AAAA,WACF;AAEA,SAAA,CAAA;AAAA;AACqB,QACVC,kBAAA,CAAA,KAAA,EAAA;AACP,UAAA,KAAS,EAAAF,cAAA,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AACP,SAAA,EAAA;AAAkC,UACpCE,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAAA,YACF,KAAA,EAAAH,cAAA,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA;AACkB,UACpBA,KAAA,CAAA,YAAA,CAAA,IAAAG,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;AAEA,YAAA,GAAA,EAAA,CAAA;AAAA,YACQ,KAAM,EAAAC,cAAA,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,WACH,EAAA;AACP,aAASG,SAAA,CAAA,IAAA,CAAA,EAAAL,kBAAA,CAAAM,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAAL,KAAA,CAAA,SAAA,CAAA,EAAA,CAAA,QAAA,EAAA,GAAA,KAAA;AACP,cAAI,gBAAsB,EAAA,EAAAF,kBAAA,CAAA,QAAA,EAAA;AAC1B,gBAAI,GAAA;AACJ,gBAAA,IAAU,EAAQ,QAAA;AAAA,gBACb,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA;AACL,gBAAA,gBAAkC,KAAA,mBAAA,CAAA,QAAA,CAAA;AAAA,eACpC,EAAAM,eAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAA,aACF,CAAA,EAAA,GAAA,CAAA;AAAA,mBACaC,kBAAK,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,UACpBN,kBAAA,CAAA,KAAA,EAAA;AAEA,YAAA,KAAiC,EAAAF,cAAA,CAAAC,KAAiB,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA;AAClD,WAAA,EAAA;AACA,YAAAA,KAAiC,CAAA,QAAA,CAAA,IAAAG,SAAmB,EAAA,EAAAL,kBAAA,CAAA,KAAe,EAAA;AACnE,cAAA,GAAiC,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}