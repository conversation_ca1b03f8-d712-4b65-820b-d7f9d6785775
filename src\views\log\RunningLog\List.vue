<template>
	<div class="list-container">
		<!-- 搜索区域 -->
		<div class="search-area">
			<!-- <el-form :inline="true" :model="searchForm">

				<el-form-item label="违规类型">
					<el-select v-model="searchForm.type" placeholder="请选择违规类型" clearable style="min-width: 150px;">
						<el-option label="isbn" :value="0" />
						<el-option label="书名" :value="1" />
						<el-option label="作者" :value="2" />
						<el-option label="出版社" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item label="违规内容">
					<el-input v-model="searchForm.name" placeholder="请输入违规内容" clearable />
				</el-form-item>
				<el-form-item label="审核状态">
					<el-select v-model="searchForm.review" placeholder="请选择审核状态" clearable style="min-width: 150px;">
						<el-option label="待提交" :value="0" />
						<el-option label="待审核" :value="1" />
						<el-option label="已审核" :value="2" />
						<el-option label="已撤回" :value="3" />
						<el-option label="审核失败" :value="4" />
					</el-select>
				</el-form-item>
				
				
				<el-form-item label="状态">
					<el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="min-width: 150px;">
						<el-option label="正常" :value="0" />
						<el-option label="停用" :value="1" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button @click="resetSearch">重置</el-button>
				</el-form-item>
			</el-form> -->
		</div>

		

		<div class="search-area">
			<!-- 操作按钮 -->
			<ActionBar @refresh="refreshData">
				<!-- <template #left>
					<el-button type="success" :disabled="multiple" @click="btnSuccess()">通过</el-button>
					<el-button type="danger"  :disabled="multiple" @click="btnError()" >驳回</el-button>
					<el-button type="danger"  :disabled="multiple" @click="btnRemove()" >删除</el-button>
					<el-button type="warning" :disabled="single" @click="btnEdit()">修改</el-button>
				</template> -->
			</ActionBar>
			<!-- 数据表格 -->
			<el-table
				ref="tableRef"
				:data="tableData"
				border
				stripe
				style="width: 100%;"
				v-loading="loading"
				@selection-change="handleSelectionChange"
			>

				<el-table-column type="selection" width="55" align="center" />
				<el-table-column label="主键" align="center" prop="id" v-if="false" />
				
				<el-table-column label="文件名称" align="center" prop="fileName" />
				<el-table-column label="文件类型" align="center" prop="fileType" />
				<el-table-column label="排序" align="center" prop="fileOrder" />

				<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
					<template #default="scope">
						<el-button type="primary" @click="viewLog(scope.row.fileName)">查看日志</el-button>
					</template> 
				</el-table-column>
					
			</el-table>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination
					v-model:current-page="pagination.current"
					v-model:page-size="pagination.size"
					:page-sizes="[10, 20, 50, 100]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="pagination.total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</div>


		<el-dialog
			v-model="logVisible"
			width="1300px"
			:close-on-click-modal="false"
		>
			<el-input
				v-model="logContent"
				style="width: 100%;"
				:rows="30"
				type="textarea"
				placeholder="暂无日志信息"
				disabled
				ref="logTextareaRef"
			/>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeLog">关闭</el-button>
				</span>
			</template>
		</el-dialog>
	
	</div>
</template>

<script setup>
import { ref, reactive, onMounted,nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { runningApi } from '@/api/modules/runningLog.js'
import RefreshButton from '@/components/RefreshButton.vue'
import ActionBar from '@/components/ActionBar.vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableRef = ref(null)
const remark = ref('')

const logVisible = ref(false);

// 搜索表单
const searchForm = reactive({
	type:'',
	name:'',
	review:'',
	status:''
})

// 分页配置
const pagination = reactive({
	current: 1,
	size: 10,
	total: 0
})


const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}



// 初始化
onMounted(() => {
	fetchData()
})

// 获取表格数据
const fetchData = async () => {
	loading.value = true
	try {
		
		// 构建查询参数
		const params = {
			page: pagination.current,
			size: pagination.size
		}
		
		// 使用API模块获取数据
		const res = await runningApi.pageQuery(params)
		// 由于响应拦截器已经提取了response.data，所以直接使用res
		if (res.code === 200) {
			
			tableData.value = res.data.list || []
			pagination.total = res.data.total || 0
		} else {
			ElMessage.error(res.message || '获取数据失败')
		}
	} catch (error) {
		console.error('获取数据失败:', error)
		// 显示更具体的错误信息
		if (error.code === 'ECONNABORTED') {
			ElMessage.error('请求超时，请检查网络连接或联系管理员')
		} else if (error.response) {
			ElMessage.error(`请求失败: ${error.response.status} ${error.response.statusText}`)
		} else if (error.request) {
			ElMessage.error('服务器未响应，请稍后再试')
		} else {
			ElMessage.error(`请求错误: ${error.message}`)
		}
		
		// 设置空数据
		tableData.value = []
		pagination.total = 0
	} finally {
		loading.value = false
	}
}




const logContent = ref();
const logTextareaRef = ref(null); // 获取 textarea 的引用

 let timer = null;
//查看日志
const viewLog = async (fileName) => {
	clearInterval(timer)
	timer = null
	logContent.value = '';

	logVisible.value = true;
	await fetchLogContent(fileName);
	timer = setInterval( async () => {
		await fetchLogContent(fileName);
	}, 5000)
}

// 封装获取日志内容的逻辑
const fetchLogContent = async (fileName) => {
	
  const res = await runningApi.viewLog(fileName);
  logContent.value = res.data;
  
  // 滚动到底部
  nextTick(() => {
    const textarea = logTextareaRef.value?.$el?.querySelector('textarea');
    if (textarea) {
      textarea.scrollTop = textarea.scrollHeight; // 关键代码
    }
  });
};

const closeLog = () =>{
	clearInterval(timer)
	timer = null
	logVisible.value = false;
	logContent.value = '';
	
}



// 刷新数据
const refreshData = () => {
	fetchData()
}

// 处理搜索
const handleSearch = () => {
	pagination.current = 1
	fetchData()
}

// 重置搜索
const resetSearch = () => {
	pagination.current = 1
	fetchData()
}

// 分页大小变化
const handleSizeChange = (size) => {
	pagination.size = size
	pagination.current = 1
	fetchData()
}

// 页码变化
const handleCurrentChange = (current) => {
	pagination.current = current
	fetchData()
}


</script>

<style scoped>
/* .list-container {
	padding: 5px;
} */

.search-area {
	margin-bottom: 20px;
	padding: 15px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	text-align: left;
}

.search-area .el-form-item {
	margin-right: 18px;
	margin-bottom: 10px;
}

.action-bar {
	margin-bottom: 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 10px;
	text-align: left;
}

.action-left {
	display: flex;
	gap: 10px;
}

.action-right {
	display: flex;
	gap: 10px;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-start;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.card-secret-content {
	text-align: left;
	
	p {
		margin-bottom: 15px;
		font-size: 16px;
	}
}

.card-params-content {
	text-align: left;
	
	p {
		margin-bottom: 15px;
		font-size: 16px;
	}
}



:global(.el-textarea.is-disabled .el-textarea__inner){
  background-color: #000 !important;
  color: #fff !important;  /* 文字颜色设为白色 */
  font-family: monospace; /* 等宽字体，适合日志显示 */
}


.unit-label {
	margin-left: 5px;
}
</style> 