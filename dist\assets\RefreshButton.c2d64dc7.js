import{Z as e,a3 as s,o as a,F as t,w as r,m as n,n as i,aq as o,z as l}from"./vendor.9a6f3141.js";const u=Object.assign({name:"RefreshButton"},{__name:"RefreshButton",props:{size:{type:String,default:"default"},position:{type:String,default:"right"}},emits:["refresh"],setup(u,{emit:f}){const c=f,d=e(!1),p=()=>{d.value=!0,c("refresh"),setTimeout((()=>{d.value=!1}),500)};return(e,f)=>{const c=l,m=s;return a(),t(m,{class:"btn-circle btn-refresh",size:u.size,onClick:p,loading:d.value},{default:r((()=>[n(c,null,{default:r((()=>[n(i(o))])),_:1})])),_:1},8,["size","loading"])}}});u.__scopeId="data-v-4afcc2ad";var f=u;export{f as R};
