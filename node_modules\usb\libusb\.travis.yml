language: c

git:
    depth: 1

matrix:
    include:
        - os: linux
          dist: focal
          compiler: clang
        - os: linux
          dist: focal
          compiler: gcc
        - os: linux
          dist: bionic
          compiler: clang
        - os: linux
          dist: bionic
          compiler: gcc
        - os: linux
          dist: xenial
          compiler: clang
        - os: linux
          dist: xenial
          compiler: gcc
        - os: osx
          osx_image: xcode12.2
          compiler: clang
        - os: osx
          osx_image: xcode11.3
          compiler: clang
        - os: osx
          osx_image: xcode9.4
          compiler: clang

addons:
    apt:
        packages:
            - autoconf
            - automake
            - libtool
            - libudev-dev
            - m4
    homebrew:
        packages:
            - autoconf
            - automake
            - libtool
            - m4

before_script:
    - ./bootstrap.sh

script:
    - if [ "$TRAVIS_OS_NAME" = "linux" ]; then .private/ci-build.sh --build-dir build-netlink -- --disable-udev; fi
    - if [ "$TRAVIS_OS_NAME" = "linux" ]; then .private/ci-build.sh --build-dir build-udev -- --enable-udev; fi
    - if [ "$TRAVIS_OS_NAME" = "osx" ]; then .private/ci-build.sh --build-dir build; fi
    - if [ "$TRAVIS_OS_NAME" = "osx" ]; then cd Xcode && xcodebuild -project libusb.xcodeproj; fi
