{"version": 3, "file": "content.mjs", "sources": ["../../../../../../packages/components/popper/src/content.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport { buildProps, definePropType } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\nimport { popperArrowProps } from './arrow'\n\nimport type { PopperEffect } from './popper'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type { Options, Placement } from '@popperjs/core'\nimport type { Measurable } from './constants'\nimport type Content from './content.vue'\n\ntype ClassObjectType = Record<string, boolean>\ntype ClassType = string | ClassObjectType | ClassType[]\n\nconst POSITIONING_STRATEGIES = ['fixed', 'absolute'] as const\n\nexport interface CreatePopperInstanceParams {\n  referenceEl: Measurable\n  popperContentEl: HTMLElement\n  arrowEl: HTMLElement | undefined\n}\n\nexport const popperCoreConfigProps = buildProps({\n  boundariesPadding: {\n    type: Number,\n    default: 0,\n  },\n  fallbackPlacements: {\n    type: definePropType<Placement[]>(Array),\n    default: undefined,\n  },\n  gpuAcceleration: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description offset of the Tooltip\n   */\n  offset: {\n    type: Number,\n    default: 12,\n  },\n  /**\n   * @description position of Tooltip\n   */\n  placement: {\n    type: String,\n    values: placements,\n    default: 'bottom',\n  },\n  /**\n   * @description [popper.js](https://popper.js.org/docs/v2/) parameters\n   */\n  popperOptions: {\n    type: definePropType<Partial<Options>>(Object),\n    default: () => ({}),\n  },\n  strategy: {\n    type: String,\n    values: POSITIONING_STRATEGIES,\n    default: 'absolute',\n  },\n} as const)\nexport type PopperCoreConfigProps = ExtractPropTypes<\n  typeof popperCoreConfigProps\n>\n\nexport const popperContentProps = buildProps({\n  ...popperCoreConfigProps,\n  ...popperArrowProps,\n  id: String,\n  style: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n  },\n  className: {\n    type: definePropType<ClassType>([String, Array, Object]),\n  },\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'dark',\n  },\n  visible: Boolean,\n  enterable: {\n    type: Boolean,\n    default: true,\n  },\n  pure: Boolean,\n  focusOnShow: {\n    type: Boolean,\n    default: false,\n  },\n  trapping: {\n    type: Boolean,\n    default: false,\n  },\n  popperClass: {\n    type: definePropType<ClassType>([String, Array, Object]),\n  },\n  popperStyle: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n  },\n  referenceEl: {\n    type: definePropType<HTMLElement>(Object),\n  },\n  triggerTargetEl: {\n    type: definePropType<HTMLElement>(Object),\n  },\n  stopPopperMouseEvent: {\n    type: Boolean,\n    default: true,\n  },\n  virtualTriggering: Boolean,\n  zIndex: Number,\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport type PopperContentProps = ExtractPropTypes<typeof popperContentProps>\n\nexport const popperContentEmits = {\n  mouseenter: (evt: MouseEvent) => evt instanceof MouseEvent,\n  mouseleave: (evt: MouseEvent) => evt instanceof MouseEvent,\n  focus: () => true,\n  blur: () => true,\n  close: () => true,\n}\nexport type PopperContentEmits = typeof popperContentEmits\n\nexport type PopperContentInstance = InstanceType<typeof Content> & unknown\n\n/** @deprecated use `popperCoreConfigProps` instead, and it will be deprecated in the next major version */\nexport const usePopperCoreConfigProps = popperCoreConfigProps\n\n/** @deprecated use `popperContentProps` instead, and it will be deprecated in the next major version */\nexport const usePopperContentProps = popperContentProps\n\n/** @deprecated use `popperContentEmits` instead, and it will be deprecated in the next major version */\nexport const usePopperContentEmits = popperContentEmits\n\n/** @deprecated use `PopperCoreConfigProps` instead, and it will be deprecated in the next major version */\nexport type UsePopperCoreConfigProps = PopperCoreConfigProps\n\n/** @deprecated use `PopperContentProps` instead, and it will be deprecated in the next major version */\nexport type UsePopperContentProps = PopperContentProps\n\n/** @deprecated use `PopperContentInstance` instead, and it will be deprecated in the next major version */\nexport type ElPopperArrowContent = PopperContentInstance\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,sBAAsB,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACzC,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,sBAAsB;AAClC,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG,UAAU,CAAC;AAC7C,EAAE,GAAG,qBAAqB;AAC1B,EAAE,GAAG,gBAAgB;AACrB,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,oBAAoB,EAAE;AACxB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG;AAClC,EAAE,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAChD,EAAE,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAChD,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,IAAI,EAAE,MAAM,IAAI;AAClB,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE;AACU,MAAC,wBAAwB,GAAG,sBAAsB;AAClD,MAAC,qBAAqB,GAAG,mBAAmB;AAC5C,MAAC,qBAAqB,GAAG;;;;"}