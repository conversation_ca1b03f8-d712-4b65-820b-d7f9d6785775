<template>
  <div class="container">
    <!-- 搜索栏 -->
    <div class="search-area">
      <div class="search-row">
        <div class="search-item">
          <span class="search-label">一级货区编号</span>
          <el-input v-model="searchForm.code" placeholder="请输入一级货区编号" clearable></el-input>
        </div>
        <div class="search-item">
          <span class="search-label">一级货区名称</span>
          <el-input v-model="searchForm.name" placeholder="请输入名称" clearable></el-input>
        </div>
        <div class="search-item btn-item">
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button type="primary" @click="handleAdd" :icon="Plus">创建一级货区</el-button>
        <el-button @click="handleRules" :icon="Setting">仓库规则设置</el-button>
      </div>
    </div>

    <!-- 树形表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="id"
      border
      lazy
      :load="loadNode"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
      empty-text="暂无数据"
      stripe
      highlight-current-row
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', textAlign: 'center' }"
      :row-class-name="tableRowClassName"
      height="500"
      max-height="500"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="货区名称">
        <template #default="scope">
          <span>{{ scope.row.name }}{{ scope.row.unit ? ' (' + scope.row.unit + ')' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="货区编号"  />
      <el-table-column prop="categoryNumber" label="书品类别"  />
      <el-table-column prop="inventory" label="库存数量"  />
      <el-table-column prop="userId" label="用户"  />
      <el-table-column prop="status" label="货区状态" >
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' || scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === '0' || scope.row.status === 0 ? '正常' : '异常(未选择运费模板)' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="运费模板选择" width="150">
        <template #default="scope">
          <div class="template-select">
            <span v-if="scope.row.templateName">{{ scope.row.templateName }}</span>
            <span v-else class="template-link">
              <el-link type="primary" @click="selectTemplate(scope.row)">未选择</el-link>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <!-- 只在一级节点显示这些操作按钮 -->
          <template v-if="!scope.row.level || scope.row.level === 1">
            <el-button type="primary" link :icon="Plus" @click="handleAddFreight(scope.row)"></el-button>
            <el-button type="primary" link :icon="Edit" @click="handleEdit(scope.row)"></el-button>
            <el-button 
              type="danger" 
              link 
              :icon="Delete" 
              @click="handleDelete(scope.row)"
              v-if="scope.row.id && !scope.row.noData"
            ></el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '创建货区' : '编辑货区'"
      width="500px"
    >
      <el-form :model="form" label-width="120px" :rules="rules" ref="formRef">
        <el-form-item label="货区名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入货区名称"></el-input>
        </el-form-item>
        <el-form-item label="货区编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入货区编号"></el-input>
        </el-form-item>
        <el-form-item label="书品类别" prop="categoryNumber">
          <el-input v-model="form.categoryNumber" placeholder="请输入书品类别"></el-input>
        </el-form-item>
        <el-form-item label="用户" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID"></el-input>
        </el-form-item>
        <el-form-item label="货区状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">正常</el-radio>
            <el-radio :label="1">异常</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 选择运费模板弹窗 -->
    <el-dialog v-model="templateDialogVisible" title="选择运费模板" width="500px">
      <el-form :model="templateForm" label-width="120px">
        <el-form-item label="运费模板">
          <el-select v-model="templateForm.templateId" placeholder="请选择运费模板">
            <el-option v-for="item in templateOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTemplateForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, Setting } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { depotApi } from '@/api'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  // 不设置默认值
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const multipleSelection = ref([])

// 弹窗表单
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = reactive({
  id: null,
  name: '',
  code: '',
  categoryNumber: '',
  userId: '',
  status: 0
})

// 运费模板相关
const templateDialogVisible = ref(false)
const templateForm = reactive({
  depotId: null,
  templateId: null
})
const templateOptions = ref([
  { value: '1', label: '南宁' },
  { value: '2', label: '南门坡街' }
])
const currentDepot = ref(null)

// 表单验证规则
const rules = reactive({
  name: [{ required: true, message: '请输入货区名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入货区编号', trigger: 'blur' }]
})

// 选择运费模板
const selectTemplate = (row) => {
  currentDepot.value = row
  templateForm.depotId = row.id
  templateForm.templateId = null
  templateDialogVisible.value = true
}

// 提交运费模板选择
const submitTemplateForm = async () => {
  try {
    // 这里应该调用API保存运费模板选择
    // const res = await depotApi.setDepotTemplate(templateForm)
    
    // 模拟API调用成功
    const selectedTemplate = templateOptions.value.find(item => item.value === templateForm.templateId)
    if (currentDepot.value && selectedTemplate) {
      currentDepot.value.templateName = selectedTemplate.label
      ElMessage.success('运费模板设置成功')
      templateDialogVisible.value = false
    } else {
      ElMessage.error('请选择运费模板')
    }
  } catch (error) {
    console.error('设置运费模板失败', error)
    ElMessage.error('设置运费模板失败')
  }
}

// 初始化加载数据
onMounted(() => {
  fetchDepotData()
})

// 获取货区数据
const fetchDepotData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    }
    const res = await depotApi.getDepotList(params)
    if (res.code === 200) {
      const depotList = res.data.list || []
      total.value = res.data.total || 0
      
      // 为每个一级货区添加hasChildren标记和level属性
      depotList.forEach(depot => {
        depot.hasChildren = true  // 默认都有子节点
        depot.level = 1  // 设置为一级节点
      })
      
      tableData.value = depotList
    } else {
      ElMessage.error(res.message || '获取货区数据失败')
    }
  } catch (error) {
    console.error('获取货区数据失败', error)
    ElMessage.error('获取货区数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchDepotData()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = undefined;
  })
  currentPage.value = 1
  fetchDepotData()
}

// 添加货区
const handleAdd = () => {
  dialogType.value = 'add'
  Object.keys(form).forEach(key => {
    form[key] = key === 'status' ? 0 : ''
  })
  dialogVisible.value = true
}

// 编辑货区
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.keys(form).forEach(key => {
    form[key] = row[key]
  })
  dialogVisible.value = true
}

// 删除货区
const handleDelete = (row) => {
  if (!row.id) {
    ElMessage.error('无效的ID，无法删除')
    return
  }
  
  ElMessageBox.confirm('确认删除该货区?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      console.log(row)
      // 确保ID不为空
      if (!row.id || row.id === 'null' || row.noData) {
        ElMessage.error('无效的ID，无法删除')
        return
      }
      
      const res = await depotApi.deleteDepot(row.id)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        fetchDepotData()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  }).catch(() => {})
}

// 添加运费模板
const handleAddFreight = (row) => {
  ElMessage.info('添加运费模板功能待实现')
}

// 仓库规则设置
const handleRules = () => {
  // 创建一个a标签用于下载
  const link = document.createElement('a')
  link.href = '/templates/depotRule.doc'
  link.download = '仓库规则设置.doc'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let res
        if (dialogType.value === 'add') {
          res = await depotApi.createDepot(form)
        } else {
          res = await depotApi.updateDepot(form)
        }
        
        if (res.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '创建成功' : '更新成功')
          dialogVisible.value = false
          fetchDepotData()
        } else {
          ElMessage.error(res.message || (dialogType.value === 'add' ? '创建失败' : '更新失败'))
        }
      } catch (error) {
        console.error(dialogType.value === 'add' ? '创建失败' : '更新失败', error)
        ElMessage.error(dialogType.value === 'add' ? '创建失败' : '更新失败')
      }
    }
  })
}

// 处理多选
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 分页
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchDepotData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchDepotData()
}

// 加载节点数据
const loadNode = async (row, treeNode, resolve) => {
  if (row.level === 1) {
    // 加载二级货架数据
    try {
      const res = await depotApi.getShelvesByDepotId(row.id)
      if (res.code === 200) {
        const shelves = res.data || []
        if (shelves.length === 0) {
          // 如果没有二级数据，返回一个特殊的空数据提示
          resolve([{ id: `empty-${row.id}`, name: '暂无数据', noData: true, hasChildren: false }])
        } else {
          shelves.forEach(shelf => {
            shelf.hasChildren = true
            shelf.level = 2
          })
          resolve(shelves)
        }
      } else {
        resolve([{ id: `empty-${row.id}`, name: '暂无数据', noData: true, hasChildren: false }])
        ElMessage.error(res.message || '获取二级货架数据失败')
      }
    } catch (error) {
      console.error('获取二级货架数据失败', error)
      ElMessage.error('获取二级货架数据失败')
      resolve([{ id: `empty-${row.id}`, name: '暂无数据', noData: true, hasChildren: false }])
    }
  } else if (row.level === 2) {
    // 加载三级货位数据
    try {
      const res = await depotApi.getFreightByShelveId(row.id)
      if (res.code === 200) {
        const freights = res.data || []
        if (freights.length === 0) {
          // 如果没有三级数据，返回一个特殊的空数据提示
          resolve([{ id: `empty-${row.id}`, name: '暂无数据', noData: true, hasChildren: false }])
        } else {
          freights.forEach(freight => {
            freight.level = 3
            freight.hasChildren = false
          })
          resolve(freights)
        }
      } else {
        resolve([{ id: `empty-${row.id}`, name: '暂无数据', noData: true, hasChildren: false }])
        ElMessage.error(res.message || '获取三级货位数据失败')
      }
    } catch (error) {
      console.error('获取三级货位数据失败', error)
      ElMessage.error('获取三级货位数据失败')
      resolve([{ id: `empty-${row.id}`, name: '暂无数据', noData: true, hasChildren: false }])
    }
  } else {
    resolve([])
  }
}

// 表格行样式
const tableRowClassName = ({ row }) => {
  if (row.noData) {
    return 'empty-row'
  }
  return ''
}
</script>

<style scoped>
.container {
  padding: 20px;
}

/* 搜索区域样式优化 */
.search-area {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.search-row {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 5px;
}

.search-label {
  width: 90px;
  text-align: right;
  padding-right: 10px;
  color: #606266;
  font-size: 14px;
}

.search-item .el-input {
  width: 220px;
}

.search-item .el-select {
  width: 220px;
}

.btn-item {
  margin-left: auto;
}

/* 操作按钮区域优化 */
.action-bar {
  margin-bottom: 15px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
}

.action-left {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
}

.template-select {
  display: flex;
  align-items: center;
}

.template-link {
  cursor: pointer;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

:deep(.el-table__header-wrapper) {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
  scrollbar-width: thin;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  border-radius: 3px;
  background: #c0c4cc;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  border-radius: 3px;
  background: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  text-align: center;
  height: 50px;
  padding: 8px 0;
  font-weight: 500;
  border-bottom: 2px solid #EBEEF5;
}

:deep(.el-table td) {
  padding: 12px 8px;
  height: 55px;
  vertical-align: middle;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__row:hover td) {
  background-color: #f0f7ff !important;
}

:deep(.el-table__row.empty-row td) {
  color: #909399;
  font-style: italic;
  background-color: #f9f9f9;
  text-align: center;
  padding: 20px 0;
}

:deep(.el-table__expand-icon) {
  color: #409EFF;
  font-size: 16px;
}

:deep(.el-table__indent) {
  padding-left: 15px;
}

:deep(.el-table .cell) {
  padding: 0 10px;
}

/* 表格中的标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 28px;
  line-height: 26px;
  font-weight: 500;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  border-color: #fde2e2;
}

/* 链接按钮样式优化 */
:deep(.el-button.is-link) {
  padding: 4px 8px;
  margin: 0 4px;
}

:deep(.el-button.is-link:hover) {
  background-color: #ecf5ff;
  border-radius: 4px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 暂无数据的样式 */
:deep(.el-table__empty-text) {
  padding: 30px 0;
  font-size: 14px;
  color: #909399;
}
</style> 