<template>
    <el-menu router :default-active="$route.path" :collapse="false" unique-opened background-color="#304156" text-color="#bfcbd9" active-text-color="#409EFF">
        <template v-for="item in userMenuTree" :key="item.id">
            <!-- 有子菜单的情况 -->
            <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path || item.id.toString()">
                <template #title>
                    <el-icon v-if="item.icon">
                        <component :is="getIcon(item.icon)" />
                    </el-icon>
                    <span>{{ item.name }}</span>
                </template>
                <el-menu-item v-for="child in item.children" :key="child.id" :index="child.path">
                    {{ child.name }}
                </el-menu-item>
            </el-sub-menu>
            
            <!-- 单个菜单项 -->
            <el-menu-item v-else :index="item.path">
                <el-icon v-if="item.icon">
                    <component :is="getIcon(item.icon)" />
                </el-icon>
                <span>{{ item.name }}</span>
            </el-menu-item>
        </template>
    </el-menu>
</template>

<script setup lang="ts">
    import { ref, onMounted } from 'vue'
    import { Setting, Shop, Notebook, Monitor } from '@element-plus/icons-vue'
    import { getUserMenuTree } from '@/api/permission'

    const userMenuTree = ref([])

    // 图标映射
    const iconMap = {
        'Setting': Setting,
        'Shop': Shop,
        'Notebook': Notebook,
        'Monitor': Monitor
    }

    const getIcon = (iconName: string) => {
        return iconMap[iconName] || Setting
    }

    const loadUserMenu = async () => {
        try {
            const res = await getUserMenuTree()
            if (res.code === 200) {
                userMenuTree.value = res.data || []
            }
        } catch (error) {
            console.error('获取用户菜单失败:', error)
        }
    }

    onMounted(() => {
        loadUserMenu()
    })
</script>