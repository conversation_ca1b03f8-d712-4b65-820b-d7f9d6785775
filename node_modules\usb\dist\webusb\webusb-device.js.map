{"version": 3, "file": "webusb-device.js", "sourceRoot": "", "sources": ["../../tsc/webusb/webusb-device.ts"], "names": [], "mappings": ";;;AAAA,8BAA8B;AAC9B,+BAAiC;AAEjC,2BAA8B;AAE9B,MAAM,yBAAyB,GAAG,IAAI,CAAC;AACvC,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,MAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,MAAM,aAAa,GAAG,IAAI,CAAC;AAE3B;;GAEG;AACH,MAAa,YAAY;IACd,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,MAAkB,EAAE,sBAAsB,GAAG,IAAI;QAChF,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC;QAClE,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAC;IACpB,CAAC;IA+BD,YAA4B,MAAkB,EAAU,sBAA+B;QAA3D,WAAM,GAAN,MAAM,CAAY;QAAU,2BAAsB,GAAtB,sBAAsB,CAAS;QAdhF,mBAAc,GAAuB,EAAE,CAAC;QAe3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,CAAC;QAEzC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;QACxD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;QAC9D,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;QAC9D,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;QAEnD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5E,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC;QAC9C,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC;QAC9C,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC;QAE/C,IAAI,CAAC,oBAAoB,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvF,IAAI,CAAC,UAAU,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjE,IAAI,CAAC,wBAAwB,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjG,CAAC;IAED,IAAW,aAAa;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAChC,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;QAC9E,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,oBAAoB,CAAC,CAAC;IAChH,CAAC;IAED,IAAW,MAAM;QACb,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAI;QACb,IAAI,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,OAAO;YACX,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,eAAe,KAAK,EAAE,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,KAAK;QACd,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACf,OAAO;YACX,CAAC;YAED,IAAI,CAAC;gBACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;wBAChD,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;wBACpD,0DAA0D;wBAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;4BAC1E,eAAe,EAAE,KAAK,CAAC,eAAe;4BACtC,SAAS,EAAE,KAAK,CAAC,SAAS;4BAC1B,UAAU,EAAE,KAAK,CAAC,UAAU;4BAC5B,OAAO,EAAE,KAAK;yBACjB,CAAC;oBACN,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,MAAM,EAAE,CAAC;gBACd,SAAS;YACb,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,gBAAgB,KAAK,EAAE,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,kBAA0B;QACvD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,kBAAkB,EAAE,CAAC;YAC1E,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,CAAC;QAClH,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,eAAuB;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,KAAK,eAAe,CAAC,CAAC;QACnH,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,CAAC;YAE/C,0DAA0D;YAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;gBAC1E,eAAe;gBACf,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,OAAO,EAAE,IAAI;aAChB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,eAAuB;QACjD,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,KAAK,eAAe,CAAC,CAAC;YACnH,IAAI,KAAK,EAAE,CAAC;gBACR,0DAA0D;gBAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;oBAC1E,eAAe;oBACf,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,OAAO,EAAE,KAAK;iBACjB,CAAC;YACN,CAAC;QACL,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,eAAuB,EAAE,gBAAwB;QACnF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,KAAK,eAAe,CAAC,CAAC;QACnH,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,KAAmC,EAAE,MAAc;QAC9E,IAAI,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAC7E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAEtG,OAAO;gBACH,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,UAAU,CAAC,MAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBAChF,MAAM,EAAE,IAAI;aACf,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAK,KAA6B,CAAC,KAAK,KAAK,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACrE,OAAO;oBACH,MAAM,EAAE,OAAO;iBAClB,CAAC;YACN,CAAC;YAED,IAAK,KAA6B,CAAC,KAAK,KAAK,GAAG,CAAC,wBAAwB,EAAE,CAAC;gBACxE,OAAO;oBACH,MAAM,EAAE,QAAQ;iBACnB,CAAC;YACN,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,KAAmC,EAAE,IAAkB;QACnF,IAAI,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,GAAG,CAAC,mBAAmB,CAAC,CAAC;YAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,YAAY,GAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAEpH,OAAO;gBACH,YAAY;gBACZ,MAAM,EAAE,IAAI;aACf,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAK,KAA6B,CAAC,KAAK,KAAK,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACrE,OAAO;oBACH,YAAY,EAAE,CAAC;oBACf,MAAM,EAAE,OAAO;iBAClB,CAAC;YACN,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,SAAuB,EAAE,cAAsB;QAClE,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,cAAc,GAAG,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACxG,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,yBAAyB,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC;QACxI,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,cAAsB,EAAE,MAAc;QAC1D,IAAI,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,GAAG,CAAC,kBAAkB,CAAe,CAAC;YACzF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAEpD,OAAO;gBACH,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBACtE,MAAM,EAAE,IAAI;aACf,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAK,KAA6B,CAAC,KAAK,KAAK,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACrE,OAAO;oBACH,MAAM,EAAE,OAAO;iBAClB,CAAC;YACN,CAAC;YAED,IAAK,KAA6B,CAAC,KAAK,KAAK,GAAG,CAAC,wBAAwB,EAAE,CAAC;gBACxE,OAAO;oBACH,MAAM,EAAE,QAAQ;iBACnB,CAAC;YACN,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,cAAsB,EAAE,IAAiB;QAC9D,IAAI,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,GAAG,CAAC,mBAAmB,CAAgB,CAAC;YAC3F,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAE1D,OAAO;gBACH,YAAY;gBACZ,MAAM,EAAE,IAAI;aACf,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAK,KAA6B,CAAC,KAAK,KAAK,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACrE,OAAO;oBACH,YAAY,EAAE,CAAC;oBACf,MAAM,EAAE,OAAO;iBAClB,CAAC;YACN,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,EAAE,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,KAAK;QACd,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,gBAAgB,KAAK,EAAE,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,eAAuB,EAAE,cAAwB;QAChF,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,eAAuB,EAAE,KAAmB,EAAE,cAAwB;QACtG,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAEM,KAAK,CAAC,MAAM;QACf,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAEnB,oEAAoE;gBACpE,iDAAiD;gBACjD,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,IAAA,aAAQ,GAAE,KAAK,QAAQ,EAAE,CAAC;oBACvD,MAAM,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YACnG,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACzF,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAC/F,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;gBAAS,CAAC;YACP,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACxB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,OAAe;QACjC,MAAM,GAAG,GAAG,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO;YACH,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC;YAC5C,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC;YAC5C,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC;SAC7C,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAa;QAC3C,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YAC1D,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACpD,MAAM,UAAU,GAAmB,EAAE,CAAC;YAEtC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpC,MAAM,UAAU,GAA4B,EAAE,CAAC;gBAE/C,KAAK,MAAM,SAAS,IAAI,KAAK,EAAE,CAAC;oBAC5B,MAAM,SAAS,GAAkB,EAAE,CAAC;oBAEpC,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;wBACzC,SAAS,CAAC,IAAI,CAAC;4BACX,cAAc,EAAE,QAAQ,CAAC,gBAAgB,GAAG,oBAAoB;4BAChE,SAAS,EAAE,QAAQ,CAAC,gBAAgB,GAAG,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;4BAC5E,IAAI,EAAE,CAAC,QAAQ,CAAC,YAAY,GAAG,yBAAyB,CAAC,KAAK,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC,MAAM;gCAChG,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,GAAG,yBAAyB,CAAC,KAAK,GAAG,CAAC,8BAA8B,CAAC,CAAC,CAAC,WAAW;oCACtG,CAAC,CAAC,aAAa;4BACvB,UAAU,EAAE,QAAQ,CAAC,cAAc;yBACtC,CAAC,CAAC;oBACP,CAAC;oBAED,UAAU,CAAC,IAAI,CAAC;wBACZ,gBAAgB,EAAE,SAAS,CAAC,iBAAiB;wBAC7C,cAAc,EAAE,SAAS,CAAC,eAAe;wBACzC,iBAAiB,EAAE,SAAS,CAAC,kBAAkB;wBAC/C,iBAAiB,EAAE,SAAS,CAAC,kBAAkB;wBAC/C,aAAa,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC;wBACnE,SAAS;qBACZ,CAAC,CAAC;gBACP,CAAC;gBAED,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;gBAClD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,CAAC;gBAErH,IAAI,SAAS,EAAE,CAAC;oBACZ,UAAU,CAAC,IAAI,CAAC;wBACZ,eAAe;wBACf,SAAS;wBACT,UAAU;wBACV,OAAO,EAAE,KAAK;qBACjB,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAED,OAAO,CAAC,IAAI,CAAC;gBACT,kBAAkB,EAAE,MAAM,CAAC,mBAAmB;gBAC9C,iBAAiB,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,cAAc,CAAC;gBACxE,UAAU;aACb,CAAC,CAAC;QACP,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,WAAW,CAAC,OAAe;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACzC,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,2BAA2B,CAAC,KAAmC,EAAE,SAAiB;QACtF,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,uBAAuB;YACxE,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,0BAA0B;gBAC9D,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,yBAAyB;oBAC5D,CAAC,CAAC,GAAG,CAAC,sBAAsB,CAAC;QAEzC,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,4BAA4B;YACnF,CAAC,CAAC,KAAK,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,yBAAyB;gBAC3D,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAEzC,OAAO,SAAS,GAAG,WAAW,GAAG,SAAS,CAAC;IAC/C,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,eAAuB;QACnD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,KAAK,eAAe,CAAC,CAAC;QACnH,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC,YAAY,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;CACJ;AAjfD,oCAifC"}