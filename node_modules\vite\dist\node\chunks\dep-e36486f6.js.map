{"version": 3, "file": "dep-e36486f6.js", "sources": ["../../../../../node_modules/postcss-value-parser/lib/parse.js", "../../../../../node_modules/postcss-value-parser/lib/walk.js", "../../../../../node_modules/postcss-value-parser/lib/stringify.js", "../../../../../node_modules/postcss-value-parser/lib/unit.js", "../../../../../node_modules/postcss-value-parser/lib/index.js"], "sourcesContent": ["var openParentheses = \"(\".charCodeAt(0);\nvar closeParentheses = \")\".charCodeAt(0);\nvar singleQuote = \"'\".charCodeAt(0);\nvar doubleQuote = '\"'.charCodeAt(0);\nvar backslash = \"\\\\\".charCodeAt(0);\nvar slash = \"/\".charCodeAt(0);\nvar comma = \",\".charCodeAt(0);\nvar colon = \":\".charCodeAt(0);\nvar star = \"*\".charCodeAt(0);\nvar uLower = \"u\".charCodeAt(0);\nvar uUpper = \"U\".charCodeAt(0);\nvar plus = \"+\".charCodeAt(0);\nvar isUnicodeRange = /^[a-f0-9?-]+$/i;\n\nmodule.exports = function(input) {\n  var tokens = [];\n  var value = input;\n\n  var next,\n    quote,\n    prev,\n    token,\n    escape,\n    escapePos,\n    whitespacePos,\n    parenthesesOpenPos;\n  var pos = 0;\n  var code = value.charCodeAt(pos);\n  var max = value.length;\n  var stack = [{ nodes: tokens }];\n  var balanced = 0;\n  var parent;\n\n  var name = \"\";\n  var before = \"\";\n  var after = \"\";\n\n  while (pos < max) {\n    // Whitespaces\n    if (code <= 32) {\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      token = value.slice(pos, next);\n\n      prev = tokens[tokens.length - 1];\n      if (code === closeParentheses && balanced) {\n        after = token;\n      } else if (prev && prev.type === \"div\") {\n        prev.after = token;\n      } else if (\n        code === comma ||\n        code === colon ||\n        (code === slash &&\n          value.charCodeAt(next + 1) !== star &&\n          (!parent ||\n            (parent && parent.type === \"function\" && parent.value !== \"calc\")))\n      ) {\n        before = token;\n      } else {\n        tokens.push({\n          type: \"space\",\n          sourceIndex: pos,\n          value: token\n        });\n      }\n\n      pos = next;\n\n      // Quotes\n    } else if (code === singleQuote || code === doubleQuote) {\n      next = pos;\n      quote = code === singleQuote ? \"'\" : '\"';\n      token = {\n        type: \"string\",\n        sourceIndex: pos,\n        quote: quote\n      };\n      do {\n        escape = false;\n        next = value.indexOf(quote, next + 1);\n        if (~next) {\n          escapePos = next;\n          while (value.charCodeAt(escapePos - 1) === backslash) {\n            escapePos -= 1;\n            escape = !escape;\n          }\n        } else {\n          value += quote;\n          next = value.length - 1;\n          token.unclosed = true;\n        }\n      } while (escape);\n      token.value = value.slice(pos + 1, next);\n\n      tokens.push(token);\n      pos = next + 1;\n      code = value.charCodeAt(pos);\n\n      // Comments\n    } else if (code === slash && value.charCodeAt(pos + 1) === star) {\n      token = {\n        type: \"comment\",\n        sourceIndex: pos\n      };\n\n      next = value.indexOf(\"*/\", pos);\n      if (next === -1) {\n        token.unclosed = true;\n        next = value.length;\n      }\n\n      token.value = value.slice(pos + 2, next);\n      tokens.push(token);\n\n      pos = next + 2;\n      code = value.charCodeAt(pos);\n\n      // Operation within calc\n    } else if (\n      (code === slash || code === star) &&\n      parent &&\n      parent.type === \"function\" &&\n      parent.value === \"calc\"\n    ) {\n      token = value[pos];\n      tokens.push({\n        type: \"word\",\n        sourceIndex: pos - before.length,\n        value: token\n      });\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      // Dividers\n    } else if (code === slash || code === comma || code === colon) {\n      token = value[pos];\n\n      tokens.push({\n        type: \"div\",\n        sourceIndex: pos - before.length,\n        value: token,\n        before: before,\n        after: \"\"\n      });\n      before = \"\";\n\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      // Open parentheses\n    } else if (openParentheses === code) {\n      // Whitespaces after open parentheses\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      parenthesesOpenPos = pos;\n      token = {\n        type: \"function\",\n        sourceIndex: pos - name.length,\n        value: name,\n        before: value.slice(parenthesesOpenPos + 1, next)\n      };\n      pos = next;\n\n      if (name === \"url\" && code !== singleQuote && code !== doubleQuote) {\n        next -= 1;\n        do {\n          escape = false;\n          next = value.indexOf(\")\", next + 1);\n          if (~next) {\n            escapePos = next;\n            while (value.charCodeAt(escapePos - 1) === backslash) {\n              escapePos -= 1;\n              escape = !escape;\n            }\n          } else {\n            value += \")\";\n            next = value.length - 1;\n            token.unclosed = true;\n          }\n        } while (escape);\n        // Whitespaces before closed\n        whitespacePos = next;\n        do {\n          whitespacePos -= 1;\n          code = value.charCodeAt(whitespacePos);\n        } while (code <= 32);\n        if (parenthesesOpenPos < whitespacePos) {\n          if (pos !== whitespacePos + 1) {\n            token.nodes = [\n              {\n                type: \"word\",\n                sourceIndex: pos,\n                value: value.slice(pos, whitespacePos + 1)\n              }\n            ];\n          } else {\n            token.nodes = [];\n          }\n          if (token.unclosed && whitespacePos + 1 !== next) {\n            token.after = \"\";\n            token.nodes.push({\n              type: \"space\",\n              sourceIndex: whitespacePos + 1,\n              value: value.slice(whitespacePos + 1, next)\n            });\n          } else {\n            token.after = value.slice(whitespacePos + 1, next);\n          }\n        } else {\n          token.after = \"\";\n          token.nodes = [];\n        }\n        pos = next + 1;\n        code = value.charCodeAt(pos);\n        tokens.push(token);\n      } else {\n        balanced += 1;\n        token.after = \"\";\n        tokens.push(token);\n        stack.push(token);\n        tokens = token.nodes = [];\n        parent = token;\n      }\n      name = \"\";\n\n      // Close parentheses\n    } else if (closeParentheses === code && balanced) {\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      parent.after = after;\n      after = \"\";\n      balanced -= 1;\n      stack.pop();\n      parent = stack[balanced];\n      tokens = parent.nodes;\n\n      // Words\n    } else {\n      next = pos;\n      do {\n        if (code === backslash) {\n          next += 1;\n        }\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (\n        next < max &&\n        !(\n          code <= 32 ||\n          code === singleQuote ||\n          code === doubleQuote ||\n          code === comma ||\n          code === colon ||\n          code === slash ||\n          code === openParentheses ||\n          (code === star &&\n            parent &&\n            parent.type === \"function\" &&\n            parent.value === \"calc\") ||\n          (code === slash &&\n            parent.type === \"function\" &&\n            parent.value === \"calc\") ||\n          (code === closeParentheses && balanced)\n        )\n      );\n      token = value.slice(pos, next);\n\n      if (openParentheses === code) {\n        name = token;\n      } else if (\n        (uLower === token.charCodeAt(0) || uUpper === token.charCodeAt(0)) &&\n        plus === token.charCodeAt(1) &&\n        isUnicodeRange.test(token.slice(2))\n      ) {\n        tokens.push({\n          type: \"unicode-range\",\n          sourceIndex: pos,\n          value: token\n        });\n      } else {\n        tokens.push({\n          type: \"word\",\n          sourceIndex: pos,\n          value: token\n        });\n      }\n\n      pos = next;\n    }\n  }\n\n  for (pos = stack.length - 1; pos; pos -= 1) {\n    stack[pos].unclosed = true;\n  }\n\n  return stack[0].nodes;\n};\n", "module.exports = function walk(nodes, cb, bubble) {\n  var i, max, node, result;\n\n  for (i = 0, max = nodes.length; i < max; i += 1) {\n    node = nodes[i];\n    if (!bubble) {\n      result = cb(node, i, nodes);\n    }\n\n    if (\n      result !== false &&\n      node.type === \"function\" &&\n      Array.isArray(node.nodes)\n    ) {\n      walk(node.nodes, cb, bubble);\n    }\n\n    if (bubble) {\n      cb(node, i, nodes);\n    }\n  }\n};\n", "function stringifyNode(node, custom) {\n  var type = node.type;\n  var value = node.value;\n  var buf;\n  var customResult;\n\n  if (custom && (customResult = custom(node)) !== undefined) {\n    return customResult;\n  } else if (type === \"word\" || type === \"space\") {\n    return value;\n  } else if (type === \"string\") {\n    buf = node.quote || \"\";\n    return buf + value + (node.unclosed ? \"\" : buf);\n  } else if (type === \"comment\") {\n    return \"/*\" + value + (node.unclosed ? \"\" : \"*/\");\n  } else if (type === \"div\") {\n    return (node.before || \"\") + value + (node.after || \"\");\n  } else if (Array.isArray(node.nodes)) {\n    buf = stringify(node.nodes, custom);\n    if (type !== \"function\") {\n      return buf;\n    }\n    return (\n      value +\n      \"(\" +\n      (node.before || \"\") +\n      buf +\n      (node.after || \"\") +\n      (node.unclosed ? \"\" : \")\")\n    );\n  }\n  return value;\n}\n\nfunction stringify(nodes, custom) {\n  var result, i;\n\n  if (Array.isArray(nodes)) {\n    result = \"\";\n    for (i = nodes.length - 1; ~i; i -= 1) {\n      result = stringifyNode(nodes[i], custom) + result;\n    }\n    return result;\n  }\n  return stringifyNode(nodes, custom);\n}\n\nmodule.exports = stringify;\n", "var minus = \"-\".charCodeAt(0);\nvar plus = \"+\".charCodeAt(0);\nvar dot = \".\".charCodeAt(0);\nvar exp = \"e\".charCodeAt(0);\nvar EXP = \"E\".charCodeAt(0);\n\n// Check if three code points would start a number\n// https://www.w3.org/TR/css-syntax-3/#starts-with-a-number\nfunction likeNumber(value) {\n  var code = value.charCodeAt(0);\n  var nextCode;\n\n  if (code === plus || code === minus) {\n    nextCode = value.charCodeAt(1);\n\n    if (nextCode >= 48 && nextCode <= 57) {\n      return true;\n    }\n\n    var nextNextCode = value.charCodeAt(2);\n\n    if (nextCode === dot && nextNextCode >= 48 && nextNextCode <= 57) {\n      return true;\n    }\n\n    return false;\n  }\n\n  if (code === dot) {\n    nextCode = value.charCodeAt(1);\n\n    if (nextCode >= 48 && nextCode <= 57) {\n      return true;\n    }\n\n    return false;\n  }\n\n  if (code >= 48 && code <= 57) {\n    return true;\n  }\n\n  return false;\n}\n\n// Consume a number\n// https://www.w3.org/TR/css-syntax-3/#consume-number\nmodule.exports = function(value) {\n  var pos = 0;\n  var length = value.length;\n  var code;\n  var nextCode;\n  var nextNextCode;\n\n  if (length === 0 || !likeNumber(value)) {\n    return false;\n  }\n\n  code = value.charCodeAt(pos);\n\n  if (code === plus || code === minus) {\n    pos++;\n  }\n\n  while (pos < length) {\n    code = value.charCodeAt(pos);\n\n    if (code < 48 || code > 57) {\n      break;\n    }\n\n    pos += 1;\n  }\n\n  code = value.charCodeAt(pos);\n  nextCode = value.charCodeAt(pos + 1);\n\n  if (code === dot && nextCode >= 48 && nextCode <= 57) {\n    pos += 2;\n\n    while (pos < length) {\n      code = value.charCodeAt(pos);\n\n      if (code < 48 || code > 57) {\n        break;\n      }\n\n      pos += 1;\n    }\n  }\n\n  code = value.charCodeAt(pos);\n  nextCode = value.charCodeAt(pos + 1);\n  nextNextCode = value.charCodeAt(pos + 2);\n\n  if (\n    (code === exp || code === EXP) &&\n    ((nextCode >= 48 && nextCode <= 57) ||\n      ((nextCode === plus || nextCode === minus) &&\n        nextNextCode >= 48 &&\n        nextNextCode <= 57))\n  ) {\n    pos += nextCode === plus || nextCode === minus ? 3 : 2;\n\n    while (pos < length) {\n      code = value.charCodeAt(pos);\n\n      if (code < 48 || code > 57) {\n        break;\n      }\n\n      pos += 1;\n    }\n  }\n\n  return {\n    number: value.slice(0, pos),\n    unit: value.slice(pos)\n  };\n};\n", "var parse = require(\"./parse\");\nvar walk = require(\"./walk\");\nvar stringify = require(\"./stringify\");\n\nfunction ValueParser(value) {\n  if (this instanceof ValueParser) {\n    this.nodes = parse(value);\n    return this;\n  }\n  return new ValueParser(value);\n}\n\nValueParser.prototype.toString = function() {\n  return Array.isArray(this.nodes) ? stringify(this.nodes) : \"\";\n};\n\nValueParser.prototype.walk = function(cb, bubble) {\n  walk(this.nodes, cb, bubble);\n  return this;\n};\n\nValueParser.unit = require(\"./unit\");\n\nValueParser.walk = walk;\n\nValueParser.stringify = stringify;\n\nmodule.exports = ValueParser;\n"], "names": ["plus", "parse", "walk", "stringify", "require$$0", "require$$1", "require$$2", "require$$3"], "mappings": ";;AAAA,IAAI,eAAe,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,gBAAgB,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,WAAW,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,WAAW,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAIA,MAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,cAAc,GAAG,gBAAgB,CAAC;AACtC;IACAC,OAAc,GAAG,SAAS,KAAK,EAAE;AACjC,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC;AACpB;AACA,EAAE,IAAI,IAAI;AACV,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,kBAAkB,CAAC;AACvB,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AACzB,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AAClC,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC;AACnB,EAAE,IAAI,MAAM,CAAC;AACb;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB;AACA,EAAE,OAAO,GAAG,GAAG,GAAG,EAAE;AACpB;AACA,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE;AACpB,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,MAAM,GAAG;AACT,QAAQ,IAAI,IAAI,CAAC,CAAC;AAClB,QAAQ,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,OAAO,QAAQ,IAAI,IAAI,EAAE,EAAE;AAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACrC;AACA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvC,MAAM,IAAI,IAAI,KAAK,gBAAgB,IAAI,QAAQ,EAAE;AACjD,QAAQ,KAAK,GAAG,KAAK,CAAC;AACtB,OAAO,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;AAC9C,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,OAAO,MAAM;AACb,QAAQ,IAAI,KAAK,KAAK;AACtB,QAAQ,IAAI,KAAK,KAAK;AACtB,SAAS,IAAI,KAAK,KAAK;AACvB,UAAU,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,IAAI;AAC7C,WAAW,CAAC,MAAM;AAClB,aAAa,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC;AAC/E,QAAQ;AACR,QAAQ,MAAM,GAAG,KAAK,CAAC;AACvB,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,IAAI,CAAC;AACpB,UAAU,IAAI,EAAE,OAAO;AACvB,UAAU,WAAW,EAAE,GAAG;AAC1B,UAAU,KAAK,EAAE,KAAK;AACtB,SAAS,CAAC,CAAC;AACX,OAAO;AACP;AACA,MAAM,GAAG,GAAG,IAAI,CAAC;AACjB;AACA;AACA,KAAK,MAAM,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,EAAE;AAC7D,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,MAAM,KAAK,GAAG,IAAI,KAAK,WAAW,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/C,MAAM,KAAK,GAAG;AACd,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,WAAW,EAAE,GAAG;AACxB,QAAQ,KAAK,EAAE,KAAK;AACpB,OAAO,CAAC;AACR,MAAM,GAAG;AACT,QAAQ,MAAM,GAAG,KAAK,CAAC;AACvB,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;AAC9C,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,UAAU,SAAS,GAAG,IAAI,CAAC;AAC3B,UAAU,OAAO,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;AAChE,YAAY,SAAS,IAAI,CAAC,CAAC;AAC3B,YAAY,MAAM,GAAG,CAAC,MAAM,CAAC;AAC7B,WAAW;AACX,SAAS,MAAM;AACf,UAAU,KAAK,IAAI,KAAK,CAAC;AACzB,UAAU,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAClC,UAAU,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAChC,SAAS;AACT,OAAO,QAAQ,MAAM,EAAE;AACvB,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/C;AACA,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AACrB,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA;AACA,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;AACrE,MAAM,KAAK,GAAG;AACd,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,WAAW,EAAE,GAAG;AACxB,OAAO,CAAC;AACR;AACA,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACtC,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;AACvB,QAAQ,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9B,QAAQ,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/C,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AACrB,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA;AACA,KAAK,MAAM;AACX,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI;AACtC,MAAM,MAAM;AACZ,MAAM,MAAM,CAAC,IAAI,KAAK,UAAU;AAChC,MAAM,MAAM,CAAC,KAAK,KAAK,MAAM;AAC7B,MAAM;AACN,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,MAAM,MAAM,CAAC,IAAI,CAAC;AAClB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,WAAW,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM;AACxC,QAAQ,KAAK,EAAE,KAAK;AACpB,OAAO,CAAC,CAAC;AACT,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA;AACA,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;AACnE,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB;AACA,MAAM,MAAM,CAAC,IAAI,CAAC;AAClB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,WAAW,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM;AACxC,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,KAAK,EAAE,EAAE;AACjB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,GAAG,EAAE,CAAC;AAClB;AACA,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA;AACA,KAAK,MAAM,IAAI,eAAe,KAAK,IAAI,EAAE;AACzC;AACA,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,MAAM,GAAG;AACT,QAAQ,IAAI,IAAI,CAAC,CAAC;AAClB,QAAQ,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,OAAO,QAAQ,IAAI,IAAI,EAAE,EAAE;AAC3B,MAAM,kBAAkB,GAAG,GAAG,CAAC;AAC/B,MAAM,KAAK,GAAG;AACd,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,WAAW,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM;AACtC,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,IAAI,CAAC;AACzD,OAAO,CAAC;AACR,MAAM,GAAG,GAAG,IAAI,CAAC;AACjB;AACA,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,EAAE;AAC1E,QAAQ,IAAI,IAAI,CAAC,CAAC;AAClB,QAAQ,GAAG;AACX,UAAU,MAAM,GAAG,KAAK,CAAC;AACzB,UAAU,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;AAC9C,UAAU,IAAI,CAAC,IAAI,EAAE;AACrB,YAAY,SAAS,GAAG,IAAI,CAAC;AAC7B,YAAY,OAAO,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;AAClE,cAAc,SAAS,IAAI,CAAC,CAAC;AAC7B,cAAc,MAAM,GAAG,CAAC,MAAM,CAAC;AAC/B,aAAa;AACb,WAAW,MAAM;AACjB,YAAY,KAAK,IAAI,GAAG,CAAC;AACzB,YAAY,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACpC,YAAY,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAClC,WAAW;AACX,SAAS,QAAQ,MAAM,EAAE;AACzB;AACA,QAAQ,aAAa,GAAG,IAAI,CAAC;AAC7B,QAAQ,GAAG;AACX,UAAU,aAAa,IAAI,CAAC,CAAC;AAC7B,UAAU,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AACjD,SAAS,QAAQ,IAAI,IAAI,EAAE,EAAE;AAC7B,QAAQ,IAAI,kBAAkB,GAAG,aAAa,EAAE;AAChD,UAAU,IAAI,GAAG,KAAK,aAAa,GAAG,CAAC,EAAE;AACzC,YAAY,KAAK,CAAC,KAAK,GAAG;AAC1B,cAAc;AACd,gBAAgB,IAAI,EAAE,MAAM;AAC5B,gBAAgB,WAAW,EAAE,GAAG;AAChC,gBAAgB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,GAAG,CAAC,CAAC;AAC1D,eAAe;AACf,aAAa,CAAC;AACd,WAAW,MAAM;AACjB,YAAY,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7B,WAAW;AACX,UAAU,IAAI,KAAK,CAAC,QAAQ,IAAI,aAAa,GAAG,CAAC,KAAK,IAAI,EAAE;AAC5D,YAAY,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7B,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;AAC7B,cAAc,IAAI,EAAE,OAAO;AAC3B,cAAc,WAAW,EAAE,aAAa,GAAG,CAAC;AAC5C,cAAc,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE,IAAI,CAAC;AACzD,aAAa,CAAC,CAAC;AACf,WAAW,MAAM;AACjB,YAAY,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,WAAW;AACX,SAAS,MAAM;AACf,UAAU,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC3B,UAAU,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC3B,SAAS;AACT,QAAQ,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACrC,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,OAAO,MAAM;AACb,QAAQ,QAAQ,IAAI,CAAC,CAAC;AACtB,QAAQ,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AACzB,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAQ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAQ,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAClC,QAAQ,MAAM,GAAG,KAAK,CAAC;AACvB,OAAO;AACP,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB;AACA;AACA,KAAK,MAAM,IAAI,gBAAgB,KAAK,IAAI,IAAI,QAAQ,EAAE;AACtD,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB,MAAM,QAAQ,IAAI,CAAC,CAAC;AACpB,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;AAClB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;AAC5B;AACA;AACA,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,MAAM,GAAG;AACT,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;AAChC,UAAU,IAAI,IAAI,CAAC,CAAC;AACpB,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,CAAC;AAClB,QAAQ,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,OAAO;AACP,QAAQ,IAAI,GAAG,GAAG;AAClB,QAAQ;AACR,UAAU,IAAI,IAAI,EAAE;AACpB,UAAU,IAAI,KAAK,WAAW;AAC9B,UAAU,IAAI,KAAK,WAAW;AAC9B,UAAU,IAAI,KAAK,KAAK;AACxB,UAAU,IAAI,KAAK,KAAK;AACxB,UAAU,IAAI,KAAK,KAAK;AACxB,UAAU,IAAI,KAAK,eAAe;AAClC,WAAW,IAAI,KAAK,IAAI;AACxB,YAAY,MAAM;AAClB,YAAY,MAAM,CAAC,IAAI,KAAK,UAAU;AACtC,YAAY,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC;AACpC,WAAW,IAAI,KAAK,KAAK;AACzB,YAAY,MAAM,CAAC,IAAI,KAAK,UAAU;AACtC,YAAY,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC;AACpC,WAAW,IAAI,KAAK,gBAAgB,IAAI,QAAQ,CAAC;AACjD,SAAS;AACT,QAAQ;AACR,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACrC;AACA,MAAM,IAAI,eAAe,KAAK,IAAI,EAAE;AACpC,QAAQ,IAAI,GAAG,KAAK,CAAC;AACrB,OAAO,MAAM;AACb,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACzE,QAAQD,MAAI,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACpC,QAAQ,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3C,QAAQ;AACR,QAAQ,MAAM,CAAC,IAAI,CAAC;AACpB,UAAU,IAAI,EAAE,eAAe;AAC/B,UAAU,WAAW,EAAE,GAAG;AAC1B,UAAU,KAAK,EAAE,KAAK;AACtB,SAAS,CAAC,CAAC;AACX,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,IAAI,CAAC;AACpB,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,WAAW,EAAE,GAAG;AAC1B,UAAU,KAAK,EAAE,KAAK;AACtB,SAAS,CAAC,CAAC;AACX,OAAO;AACP;AACA,MAAM,GAAG,GAAG,IAAI,CAAC;AACjB,KAAK;AACL,GAAG;AACH;AACA,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;AAC9C,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACxB,CAAC;;IC/SDE,MAAc,GAAG,SAAS,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE;AAClD,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC;AAC3B;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AACnD,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,KAAK;AACL;AACA,IAAI;AACJ,MAAM,MAAM,KAAK,KAAK;AACtB,MAAM,IAAI,CAAC,IAAI,KAAK,UAAU;AAC9B,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/B,MAAM;AACN,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;AACnC,KAAK;AACL;AACA,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACzB,KAAK;AACL,GAAG;AACH,CAAC;;ACrBD,SAAS,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE;AACrC,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACzB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,YAAY,CAAC;AACnB;AACA,EAAE,IAAI,MAAM,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,SAAS,EAAE;AAC7D,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE;AAClD,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAChC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC3B,IAAI,OAAO,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AACpD,GAAG,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AACjC,IAAI,OAAO,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACtD,GAAG,MAAM,IAAI,IAAI,KAAK,KAAK,EAAE;AAC7B,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AAC5D,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxC,IAAI,GAAG,GAAGC,WAAS,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,IAAI,IAAI,KAAK,UAAU,EAAE;AAC7B,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,GAAG;AACT,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;AACzB,MAAM,GAAG;AACT,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AACxB,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,GAAG,CAAC;AAChC,MAAM;AACN,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA,SAASA,WAAS,CAAC,KAAK,EAAE,MAAM,EAAE;AAClC,EAAE,IAAI,MAAM,EAAE,CAAC,CAAC;AAChB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAC3C,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtC,CAAC;AACD;IACA,WAAc,GAAGA,WAAS;;AC/C1B,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA;AACA;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACjC,EAAE,IAAI,QAAQ,CAAC;AACf;AACA,EAAE,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACvC,IAAI,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA,IAAI,IAAI,QAAQ,KAAK,GAAG,IAAI,YAAY,IAAI,EAAE,IAAI,YAAY,IAAI,EAAE,EAAE;AACtE,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE;AACpB,IAAI,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA;AACA;IACA,IAAc,GAAG,SAAS,KAAK,EAAE;AACjC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,YAAY,CAAC;AACnB;AACA,EAAE,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC1C,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,EAAE,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACvC,IAAI,GAAG,EAAE,CAAC;AACV,GAAG;AACH;AACA,EAAE,OAAO,GAAG,GAAG,MAAM,EAAE;AACvB,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACjC;AACA,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE;AAChC,MAAM,MAAM;AACZ,KAAK;AACL;AACA,IAAI,GAAG,IAAI,CAAC,CAAC;AACb,GAAG;AACH;AACA,EAAE,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC/B,EAAE,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACvC;AACA,EAAE,IAAI,IAAI,KAAK,GAAG,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE;AACxD,IAAI,GAAG,IAAI,CAAC,CAAC;AACb;AACA,IAAI,OAAO,GAAG,GAAG,MAAM,EAAE;AACzB,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,MAAM,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE;AAClC,QAAQ,MAAM;AACd,OAAO;AACP;AACA,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC/B,EAAE,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACvC,EAAE,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3C;AACA,EAAE;AACF,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG;AACjC,KAAK,CAAC,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE;AACtC,OAAO,CAAC,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK;AAC/C,QAAQ,YAAY,IAAI,EAAE;AAC1B,QAAQ,YAAY,IAAI,EAAE,CAAC,CAAC;AAC5B,IAAI;AACJ,IAAI,GAAG,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3D;AACA,IAAI,OAAO,GAAG,GAAG,MAAM,EAAE;AACzB,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,MAAM,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE;AAClC,QAAQ,MAAM;AACd,OAAO;AACP;AACA,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;AAC/B,IAAI,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AAC1B,GAAG,CAAC;AACJ,CAAC;;ACvHD,IAAI,KAAK,GAAGC,OAAkB,CAAC;AAC/B,IAAI,IAAI,GAAGC,MAAiB,CAAC;AAC7B,IAAI,SAAS,GAAGC,WAAsB,CAAC;AACvC;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,IAAI,IAAI,YAAY,WAAW,EAAE;AACnC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AACD;AACA,WAAW,CAAC,SAAS,CAAC,QAAQ,GAAG,WAAW;AAC5C,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAChE,CAAC,CAAC;AACF;AACA,WAAW,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,EAAE,EAAE,MAAM,EAAE;AAClD,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;AAC/B,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF;AACA,WAAW,CAAC,IAAI,GAAGC,IAAiB,CAAC;AACrC;AACA,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB;AACA,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;AAClC;IACA,GAAc,GAAG;;"}