let LODOP = null

// 模板注册中心（扩展新模板只需在此注册）
const templateModules = {
	// 销售小票模板
	receipt: (LODOP, params) => {
		LODOP.PRINT_INIT("销售小票")
		LODOP.SET_PRINT_PAGESIZE(1, '76mm', '110mm', '')

		LODOP.ADD_PRINT_TEXT("10mm", "5mm", "60mm", "5mm", "=== 销售小票 ===")
		LODOP.SET_PRINT_STYLEA(0, "FontSize", 12)

		params.items.forEach((item, index) => {
			LODOP.ADD_PRINT_TEXT(
				`${25 + index * 8}mm`,
				"5mm",
				"60mm",
				"5mm",
				`${item.name} x${item.qty} ￥${item.price}`
			)
		})
	},

	// 韵达快递模板
	yunda: (LODOP, params) => {
		LODOP.PRINT_INIT("韵达快递单")
		LODOP.SET_PRINT_PAGESIZE(3, "100mm", "150mm", "")
		
		// ========== 外边框设计 ==========
		LODOP.ADD_PRINT_RECT("3mm", "3mm", "94mm", "144mm", 0, 1) // 外围边框
		LODOP.ADD_PRINT_LINE("20mm", "3mm", "20mm", "97mm", 0, 1) // 上部横线
		LODOP.ADD_PRINT_LINE("75mm", "3mm", "75mm", "97mm", 0, 1) // 中部横线
		LODOP.ADD_PRINT_LINE("120mm", "3mm", "120mm", "97mm", 0, 1) // 底部横线
		
		// ========== 头部区域 ==========
		// 代收货款
		LODOP.ADD_PRINT_TEXT("5mm", "65mm", "30mm", "8mm", "代收货款")
		LODOP.ADD_PRINT_TEXT("10mm", "65mm", "30mm", "8mm", `¥${params.payment}`)
		LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)
		
		// 运费总计
		LODOP.ADD_PRINT_TEXT("15mm", "65mm", "30mm", "5mm", "运费总计")
		LODOP.ADD_PRINT_TEXT("20mm", "65mm", "30mm", "5mm", `¥${params.freight}`)
		LODOP.SET_PRINT_STYLEA(0, "FontSize", 8)
		
		// 条码区域
		LODOP.ADD_PRINT_BARCODE("25mm", "10mm", "60mm", "15mm", "CODE39", params.trackingNumber)
		LODOP.ADD_PRINT_TEXT("40mm", "10mm", "60mm", "5mm", params.trackingNumber) // 运单号
		
		// 杭州编号
		LODOP.ADD_PRINT_TEXT("45mm", "10mm", "40mm", "5mm", params.cityCode)
		LODOP.SET_PRINT_STYLEA(0, "FontSize", 9)
		
		// ========== 集包信息 ==========
		LODOP.ADD_PRINT_TEXT("50mm", "10mm", "30mm", "5mm", `集 ${params.packageCenter}`)
		
		// 左侧信息
		LODOP.ADD_PRINT_TEXT("80mm", "5mm", "40mm", "25mm", 
		  `联 ${params.receiver.name}\n` +
		  ` ${params.receiver.phone}\n` +
		  `${params.receiver.address}`
		)
		LODOP.SET_PRINT_STYLEA(0, "LineSpacing", "3mm") // 行间距
	
		// 右侧寄件信息
		LODOP.ADD_PRINT_TEXT("80mm", "50mm", "40mm", "25mm", 
		  `寄 ${params.sender.name}\n` +
		  ` ${params.sender.phone}\n` +
		  `${params.sender.address}`
		)
			
			// ========== 底部信息 ==========
			LODOP.ADD_PRINT_TEXT("125mm", "10mm", "80mm", "5mm", "已验视")
			LODOP.ADD_PRINT_TEXT("135mm", "10mm", "80mm", "5mm", "本包裹由大商道商贸技术支持")

		// // 发件人信息
		// LODOP.ADD_PRINT_TEXT("12mm", "10mm", "40mm", "8mm", `发件人：${params.sender.name}`)
		// LODOP.ADD_PRINT_TEXT("12mm", "60mm", "40mm", "8mm", `电话：${params.sender.phone}`)

		// // 发件地址（自动换行）
		// LODOP.ADD_PRINT_TEXT("20mm", "10mm", "80mm", "20mm", `发件地址：${params.sender.address}`)
		// LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)


		// // 收件人姓名电话
		// LODOP.ADD_PRINT_TEXT("45mm", "10mm", "40mm", "8mm", `收件人：${params.receiver.name}`)
		// LODOP.ADD_PRINT_TEXT("45mm", "60mm", "40mm", "8mm", `电话： ${params.receiver.phone}`)

		// // 收件地址（自动换行）
		// LODOP.ADD_PRINT_TEXT("53mm", "10mm", "80mm", "25mm", `发件地址：${params.receiver.address}`)
		// LODOP.SET_PRINT_STYLEA(0, "FontSize", 10)

		// // 快递条形码
		// LODOP.ADD_PRINT_BARCODE("85mm", "10mm", "60mm", "15mm", "CODE39",params.trackingNumber)
		// LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 1)    // 显示条码下方文字

		// // 订单信息
		// LODOP.ADD_PRINT_TEXT("105mm", "10mm", "50mm", "5mm", `运单号：${params.trackingNumber}`)
		// LODOP.ADD_PRINT_TEXT("110mm", "10mm", "30mm", "5mm", `重量：${params.weight} kg`)
		// LODOP.ADD_PRINT_TEXT("115mm", "10mm", "80mm", "10mm", `备注： ${(params.remark || "无")}`)

		

		// ========== 打印控制 ==========
		LODOP.SET_PRINT_STYLEA(0, "Bold", 1) // 关键信息加粗
		LODOP.SET_PRINT_STYLEA(0, "Alignment", 2) // 居中对齐
	},

	// 可继续添加其他模板...
}

export const initLodop = () => {
	return new Promise((resolve, reject) => {
		if (LODOP) return resolve(LODOP)

		const script = document.createElement('script')
		script.src = 'http://localhost:8000/CLodopfuncs.js'
		script.onload = () => {
			LODOP = getCLodop()
			if (!LODOP) {
				reject(new Error('C-Lodop 未正确安装'))
			}
			resolve(LODOP)
		}
		script.onerror = () => reject(new Error('加载 C-Lodop 失败'))
		document.head.appendChild(script)
	})
}

export const createPrintTask = async (templateName, content) => {
	try {
		const LODOP = await initLodop()

		// 检查模板是否存在
		if (!templateModules[templateName]) {
			console.log(templateName)
			throw new Error(`未找到模板: ${templateName}`)
		}

		// 执行模板渲染
		templateModules[templateName](LODOP, content)

		return LODOP
	} catch (error) {
		throw error
	}
}