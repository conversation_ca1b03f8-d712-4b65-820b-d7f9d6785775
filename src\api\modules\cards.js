import instance from '../../utils/axios.js'

const cardsApi = {
  // 获取卡密列表(分页)
  pageQueryCard: (params) => {
    // 转换分页参数名称
    const convertedParams = {
      ...params,
      pageNum: params.page,
      pageSize: params.size
    }
    // 删除旧的参数
    delete convertedParams.page
    delete convertedParams.size
    
    console.log('卡密查询参数:', convertedParams)
    return instance.get('/cards/pageQueryCard', { 
      params: convertedParams,
      paramsSerializer: params => {
        return Object.entries(params)
          .filter(([_, v]) => v !== undefined)
          .map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
          .join('&')
      }
    })
  },
  
  // 删除卡密（逻辑删除）
  deleteCard: (id) => instance.post(`/cards/deleteCard`, { id }),
  
  // 停用卡密
  disableCard: (id) => instance.post(`/cards/disableCard`, { id }),
  
  // 启用卡密
  enableCard: (id) => instance.post(`/cards/enableCard`, { id }),
  
  // 获取卡密
  createCardSecret: (data) => instance.post('/cards/createCardSecret', data),
  
  // 批量生成卡密
  batchCreateCards: (data) => instance.post('/cards/batchCreate', data),
  
  // 修改卡密状态
  updateCardStatus: (data) => instance.put('/cards/updateStatus', data),

  // 获取活跃卡密列表(分页)
  getActiveCardsPage: (params) => {
    // 转换分页参数名称
    const convertedParams = {
      pageNum: params.current,
      pageSize: params.size
    }

    // 删除旧的参数
    delete convertedParams.current
    delete convertedParams.size

    console.log('活跃卡密查询参数:', convertedParams)
    return instance.get('/verifyPrice/getActiveCardsPage', {
      params: convertedParams,
      paramsSerializer: params => {
        return Object.entries(params)
            .filter(([_, v]) => v !== undefined)
            .map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
            .join('&')
      }
    })
  }

}

export { cardsApi } 