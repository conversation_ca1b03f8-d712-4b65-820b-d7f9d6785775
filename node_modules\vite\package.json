{"name": "vite", "version": "2.5.3", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "main": "dist/node/index.js", "types": "dist/node/index.d.ts", "files": ["bin", "dist", "client.d.ts", "src", "types", "!/src/**/__tests__/"], "engines": {"node": ">=12.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/#readme", "scripts": {"predev": "<PERSON><PERSON><PERSON> dist", "dev": "rollup -c -w", "prebuild": "rimraf dist && yarn lint", "build": "run-s build-bundle build-types", "build-bundle": "rollup -c", "build-types": "run-s build-temp-types patch-types roll-types", "build-temp-types": "tsc --emitDeclarationOnly --outDir temp/node -p src/node", "ci-build": "rimraf dist && yarn run-s build-bundle build-types", "patch-types": "node scripts/patchTypes", "roll-types": "api-extractor run && rimraf temp", "lint": "eslint --ext .ts src/**", "format": "prettier --write --parser typescript \"src/**/*.ts\"", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path .", "release": "node ../../scripts/release.js"}, "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.12.17", "postcss": "^8.3.6", "resolve": "^1.20.0", "rollup": "^2.38.5"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@ampproject/remapping": "^1.0.1", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-dynamic-import-vars": "^1.4.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "13.0.4", "@rollup/plugin-typescript": "^8.2.5", "@rollup/pluginutils": "^4.1.1", "@types/convert-source-map": "^1.5.2", "@types/debug": "^4.1.7", "@types/es-module-lexer": "^0.3.0", "@types/estree": "^0.0.50", "@types/etag": "^1.8.1", "@types/less": "^3.0.3", "@types/mime": "^2.0.3", "@types/node": "^15.12.2", "@types/resolve": "^1.20.1", "@types/sass": "^1.16.1", "@types/stylus": "^0.48.36", "@types/ws": "^7.4.7", "@vue/compiler-dom": "^3.1.5", "acorn": "^8.4.1", "acorn-class-fields": "^1.0.0", "acorn-numeric-separator": "^0.3.6", "acorn-static-class-features": "^1.0.0", "brotli-size": "^4.0.0", "builtin-modules": "^3.2.0", "cac": "^6.7.3", "chalk": "^4.1.2", "chokidar": "^3.5.2", "compression": "^1.7.4", "connect": "^3.7.0", "connect-history-api-fallback": "^1.6.0", "convert-source-map": "^1.8.0", "cors": "^2.8.5", "debug": "^4.3.2", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "es-module-lexer": "^0.7.1", "estree-walker": "^2.0.2", "etag": "^1.8.1", "execa": "^5.1.1", "fast-glob": "^3.2.7", "http-proxy": "^1.18.1", "launch-editor-middleware": "^2.2.1", "magic-string": "^0.25.7", "mime": "^2.5.2", "minimatch": "^3.0.4", "okie": "^1.0.1", "open": "^8.2.1", "periscopic": "^2.0.3", "postcss-import": "^14.0.2", "postcss-load-config": "^3.0.0", "postcss-modules": "^4.2.2", "resolve.exports": "^1.0.2", "rollup-plugin-license": "^2.5.0", "selfsigned": "^1.10.11", "sirv": "^1.0.17", "source-map": "^0.6.1", "source-map-support": "^0.5.19", "strip-ansi": "^6.0.0", "strip-bom": "^4.0.0", "strip-json-comments": "^3.1.1", "terser": "^5.7.1", "tsconfig": "^7.0.0", "tslib": "^2.3.0", "types": "link:./types", "ws": "^7.5.3"}}