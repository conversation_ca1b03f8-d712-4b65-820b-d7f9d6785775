<template>
  <div class="list-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="订单编号">
            <el-input v-model="queryParams.orderSn" placeholder="请输入订单编号" clearable />
          </el-form-item>
          <el-form-item label="用户ID">
            <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable />
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable style="width: 100px;">
              <el-option label="待支付" :value="0" />
              <el-option label="已支付" :value="1" />
              <el-option label="已取消" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="充值类型">
            <el-select v-model="queryParams.rechargeType" placeholder="请选择充值类型" clearable style="width: 120px;">
              <el-option label="会员充值" value="1" />
              <el-option label="增值服务" value="2" />
              <el-option label="会员升级" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="微信商户号">
            <el-input v-model="queryParams.mchid" placeholder="请输入商户号" clearable />
          </el-form-item>
          <el-form-item label="微信应用ID">
            <el-input v-model="queryParams.appid" placeholder="请输入应用ID" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon> 查询
            </el-button>
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <!-- <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon> 新增订单
        </el-button> -->
      </div>

      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="orderList" border stripe style="width: 100%" highlight-current-row>
        <el-table-column type="index" label="序号" width="60" align="center">
          <template #default="scope">
            <span>{{ (queryParams.current - 1) * queryParams.size + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单编号" min-width="180" show-overflow-tooltip />
        <el-table-column prop="userId" label="用户ID" width="80" align="center" />
        <el-table-column prop="amount" label="订单金额" width="100" align="center" />
        <el-table-column prop="orderType" label="订单类型" width="100" align="center">
          <template #default="scope">
            <el-tag type="primary" v-if="scope.row.orderType === '1'">小程序</el-tag>
            <el-tag v-else>{{ scope.row.orderType || '其他' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mchid" label="微信商户号" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="appid" label="微信应用ID" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="rechargeType" label="充值类型" width="120" align="center">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.rechargeType === '1'">会员充值</el-tag>
            <el-tag type="warning" v-else-if="scope.row.rechargeType === '2'">增值服务</el-tag>
            <el-tag type="info" v-else-if="scope.row.rechargeType === '3'">会员升级</el-tag>
            <el-tag v-else>{{ scope.row.rechargeType || '其他' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'warning' : scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 0 ? '待支付' : scope.row.status === 1 ? '已支付' : '已取消' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="scope">
            <el-button-group>
              <el-button type="primary" size="small" @click="handleDetail(scope.row)">
                <el-icon><View /></el-icon> 详情
              </el-button>
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">
                <el-icon><Edit /></el-icon> 编辑
              </el-button>
              <!-- <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                <el-icon><Delete /></el-icon> 删除
              </el-button> -->
            </el-button-group>
          </template>
        </el-table-column>
        <template #empty>
          <div class="empty-data">
            <el-empty description="暂无数据" />
          </div>
        </template>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.current"
          v-model:page-size="queryParams.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="订单编号" prop="orderSn">
          <el-input v-model="orderForm.orderSn" placeholder="请输入订单编号" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="orderForm.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="商品名称" prop="settlerCostConfigId">
          <el-input v-model="orderForm.settlerCostConfigId" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="订单金额" prop="amount">
          <el-input v-model="orderForm.amount" placeholder="请输入订单金额" />
        </el-form-item>
        <el-form-item label="订单类型" prop="orderType">
          <el-select v-model="orderForm.orderType" placeholder="请选择订单类型">
            <el-option label="小程序" value="1" />
            <el-option label="其他" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="微信商户号" prop="mchid">
          <el-input v-model="orderForm.mchid" placeholder="请输入微信商户号" />
        </el-form-item>
        <el-form-item label="微信应用ID" prop="appid">
          <el-input v-model="orderForm.appid" placeholder="请输入微信应用ID" />
        </el-form-item>
        <el-form-item label="充值类型" prop="rechargeType">
          <el-select v-model="orderForm.rechargeType" placeholder="请选择充值类型">
            <el-option label="会员充值" value="1" />
            <el-option label="增值服务" value="2" />
            <el-option label="会员升级" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="orderForm.status" placeholder="请选择订单状态">
            <el-option label="待支付" :value="0" />
            <el-option label="已支付" :value="1" />
            <el-option label="已取消" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      title="订单详情"
      v-model="detailDialogVisible"
      width="500px"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="ID">{{ orderDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="订单编号">{{ orderDetail.orderSn }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ orderDetail.userId }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">{{ orderDetail.amount }}</el-descriptions-item>
        <el-descriptions-item label="订单类型">
          <el-tag type="primary" v-if="orderDetail.orderType === '1'">小程序</el-tag>
          <el-tag v-else>{{ orderDetail.orderType || '其他' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="微信商户号">{{ orderDetail.mchid }}</el-descriptions-item>
        <el-descriptions-item label="微信应用ID">{{ orderDetail.appid }}</el-descriptions-item>
        <el-descriptions-item label="充值类型">
          <el-tag type="success" v-if="orderDetail.rechargeType === '1'">会员充值</el-tag>
          <el-tag type="warning" v-else-if="orderDetail.rechargeType === '2'">增值服务</el-tag>
          <el-tag type="info" v-else-if="orderDetail.rechargeType === '3'">会员升级</el-tag>
          <el-tag v-else>{{ orderDetail.rechargeType || '其他' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="orderDetail.status === 0 ? 'warning' : orderDetail.status === 1 ? 'success' : 'danger'">
            {{ orderDetail.status === 0 ? '待支付' : orderDetail.status === 1 ? '已支付' : '已取消' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ orderDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ orderDetail.updateTime }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, View } from '@element-plus/icons-vue'
import { getOrderList, getOrderDetail, addOrder, updateOrder, deleteOrder } from '@/api/modules/order'

export default {
  components: {
    Search,
    Refresh,
    Plus,
    Edit,
    Delete,
    View
  },
  setup() {
    // 查询参数
    const queryParams = reactive({
      orderSn: '',
      userId: '',
      status: '',
      rechargeType: '',
      mchid: '',
      appid: '',
      current: 1,
      size: 10
    })

    // 表格数据
    const orderList = ref([])
    const total = ref(0)
    const loading = ref(false)

    // 弹窗控制
    const dialogVisible = ref(false)
    const dialogTitle = ref('')
    const detailDialogVisible = ref(false)

    // 表单对象
    const orderFormRef = ref(null)
    const orderForm = reactive({
      id: '',
      orderSn: '',
      userId: '',
      settlerCostConfigId: '',
      amount: '',
      orderType: '1', // 默认为小程序
      mchid: '',
      appid: '',
      rechargeType: '',
      status: 0
    })

    // 详情对象
    const orderDetail = ref({})

    // 表单校验规则
    const rules = reactive({
      orderSn: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
      userId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
      settlerCostConfigId: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
      amount: [{ required: true, message: '请输入订单金额', trigger: 'blur' }],
      orderType: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
      mchid: [{ required: false, message: '请输入微信商户号', trigger: 'blur' }],
      appid: [{ required: false, message: '请输入微信应用ID', trigger: 'blur' }],
      rechargeType: [{ required: false, message: '请选择充值类型', trigger: 'change' }],
      status: [{ required: true, message: '请选择订单状态', trigger: 'change' }]
    })

    // 初始化
    onMounted(() => {
      getOrderListData()
    })

    // 获取订单列表
    const getOrderListData = async () => {
      loading.value = true
      try {
        const res = await getOrderList(queryParams)
        console.log("res", res)
        if (res.code === 200) {
          // 处理数据，确保字段映射正确
          orderList.value = res.data.list.map(item => ({
            id: item.id,
            orderSn: item.orderSn || item.id, // 如果没有orderSn，使用id作为显示
            userId: item.userId || 1,
            amount: item.amount || 0,
            orderType: item.orderType || '',
            mchid: item.mchid || '',
            appid: item.appid || '',
            rechargeType: item.rechargeType || '',
            status: item.status || 1,
            createTime: formatTime(item.createdTime),
            updateTime: formatTime(item.updatedTime)
          })) || []
          total.value = res.data.total || (res.data.list ? res.data.list.length : 0)
        } else {
          orderList.value = []
          total.value = 0
          ElMessage.warning(res.message || '暂无符合条件的数据')
        }
      } catch (error) {
        console.error('获取订单列表失败', error)
        orderList.value = []
        total.value = 0
        ElMessage.error('获取订单列表失败，请检查网络连接')
      } finally {
        loading.value = false
      }
    }

    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString();
    }

    // 查询按钮
    const handleQuery = () => {
      queryParams.current = 1
      getOrderListData()
    }

    // 重置按钮
    const resetQuery = () => {
      queryParams.orderSn = ''
      queryParams.userId = ''
      queryParams.status = ''
      queryParams.rechargeType = ''
      queryParams.mchid = ''
      queryParams.appid = ''
      queryParams.current = 1
      getOrderListData()
    }

    // 分页大小改变
    const handleSizeChange = (val) => {
      queryParams.size = val
      getOrderListData()
    }

    // 当前页改变
    const handleCurrentChange = (val) => {
      queryParams.current = val
      getOrderListData()
    }

    // 新增按钮
    const handleAdd = () => {
      resetForm()
      dialogTitle.value = '新增订单'
      dialogVisible.value = true
    }

    // 编辑按钮
    const handleEdit = (row) => {
      resetForm()
      dialogTitle.value = '编辑订单'
      Object.assign(orderForm, row)
      dialogVisible.value = true
    }

    // 详情按钮
    const handleDetail = async (row) => {
      try {
        const res = await getOrderDetail(row.id)
        if (res.code === 200) {
          // 处理详情数据
          orderDetail.value = {
            id: res.data.id,
            orderSn: res.data.orderSn || res.data.id,
            userId: res.data.userId || '',
            settlerCostConfigId: res.data.settlerCostConfigId || '',
            amount: res.data.amount || 0,
            orderType: res.data.orderType || '',
            mchid: res.data.mchid || '',
            appid: res.data.appid || '',
            rechargeType: res.data.rechargeType || '',
            status: res.data.status || 0,
            createTime: formatTime(res.data.createdTime),
            updateTime: formatTime(res.data.updatedTime)
          }
          detailDialogVisible.value = true
        } else {
          ElMessage.error(res.message || '获取订单详情失败')
        }
      } catch (error) {
        console.error('获取订单详情失败', error)
        ElMessage.error('获取订单详情失败')
      }
    }

    // 删除按钮
    const handleDelete = (row) => {
      ElMessageBox.confirm('确认删除该订单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteOrder(row.id)
          if (res.code === 200) {
            ElMessage.success(res.message || '删除成功')
            getOrderListData()
          } else {
            ElMessage.error(res.message || '删除失败')
          }
        } catch (error) {
          console.error('删除失败', error)
          ElMessage.error('删除失败')
        }
      }).catch(() => {
        // 取消删除
      })
    }

    // 重置表单
    const resetForm = () => {
      if (orderFormRef.value) {
        orderFormRef.value.resetFields()
      }
      orderForm.id = ''
      orderForm.orderSn = ''
      orderForm.userId = ''
      orderForm.settlerCostConfigId = ''
      orderForm.amount = ''
      orderForm.orderType = '1' // 默认为小程序
      orderForm.mchid = ''
      orderForm.appid = ''
      orderForm.rechargeType = ''
      orderForm.status = 0
    }

    // 提交表单
    const submitForm = async () => {
      if (!orderFormRef.value) return
      
      await orderFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            let res
            if (orderForm.id) {
              // 编辑
              res = await updateOrder(orderForm)
            } else {
              // 新增
              res = await addOrder(orderForm)
            }
            
            if (res.code === 200) {
              ElMessage.success(res.message || (orderForm.id ? '修改成功' : '新增成功'))
              dialogVisible.value = false
              getOrderListData()
            } else {
              ElMessage.error(res.message || (orderForm.id ? '修改失败' : '新增失败'))
            }
          } catch (error) {
            console.error(orderForm.id ? '修改失败' : '新增失败', error)
            ElMessage.error(orderForm.id ? '修改失败' : '新增失败')
          }
        }
      })
    }

    return {
      queryParams,
      orderList,
      total,
      loading,
      dialogVisible,
      dialogTitle,
      detailDialogVisible,
      orderFormRef,
      orderForm,
      orderDetail,
      rules,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleAdd,
      handleEdit,
      handleDetail,
      handleDelete,
      submitForm
    }
  }
}
</script>

<style scoped>
.list-container {
  background: #fff;
}

.search-area {
  margin-bottom: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  text-align: left;
}

.action-bar {
  margin-bottom: 10px;
  text-align: left;
}

.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 10px;
}

.el-button-group {
  display: flex;
  gap: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

.empty-data {
  padding: 20px 0;
  text-align: center;
}
</style> 