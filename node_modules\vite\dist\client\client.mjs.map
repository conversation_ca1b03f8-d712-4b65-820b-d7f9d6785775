{"version": 3, "file": "client.mjs", "sources": ["../../src/client/overlay.ts", "../../src/client/client.ts"], "sourcesContent": ["import { ErrorPayload } from 'types/hmrPayload'\n\nconst template = /*html*/ `\n<style>\n:host {\n  position: fixed;\n  z-index: 99999;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow-y: scroll;\n  margin: 0;\n  background: rgba(0, 0, 0, 0.66);\n  --monospace: 'SFMono-Regular', <PERSON><PERSON><PERSON>,\n              'Liberation Mono', Menlo, Courier, monospace;\n  --red: #ff5555;\n  --yellow: #e2aa53;\n  --purple: #cfa4ff;\n  --cyan: #2dd9da;\n  --dim: #c9c9c9;\n}\n\n.window {\n  font-family: var(--monospace);\n  line-height: 1.5;\n  width: 800px;\n  color: #d8d8d8;\n  margin: 30px auto;\n  padding: 25px 40px;\n  position: relative;\n  background: #181818;\n  border-radius: 6px 6px 8px 8px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n  overflow: hidden;\n  border-top: 8px solid var(--red);\n}\n\npre {\n  font-family: var(--monospace);\n  font-size: 16px;\n  margin-top: 0;\n  margin-bottom: 1em;\n  overflow-x: scroll;\n  scrollbar-width: none;\n}\n\npre::-webkit-scrollbar {\n  display: none;\n}\n\n.message {\n  line-height: 1.3;\n  font-weight: 600;\n  white-space: pre-wrap;\n}\n\n.message-body {\n  color: var(--red);\n}\n\n.plugin {\n  color: var(--purple);\n}\n\n.file {\n  color: var(--cyan);\n  margin-bottom: 0;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.frame {\n  color: var(--yellow);\n}\n\n.stack {\n  font-size: 13px;\n  color: var(--dim);\n}\n\n.tip {\n  font-size: 13px;\n  color: #999;\n  border-top: 1px dotted #999;\n  padding-top: 13px;\n}\n\ncode {\n  font-size: 13px;\n  font-family: var(--monospace);\n  color: var(--yellow);\n}\n\n.file-link {\n  text-decoration: underline;\n  cursor: pointer;\n}\n</style>\n<div class=\"window\">\n  <pre class=\"message\"><span class=\"plugin\"></span><span class=\"message-body\"></span></pre>\n  <pre class=\"file\"></pre>\n  <pre class=\"frame\"></pre>\n  <pre class=\"stack\"></pre>\n  <div class=\"tip\">\n    Click outside or fix the code to dismiss.<br>\n    You can also disable this overlay with\n    <code>hmr: { overlay: false }</code> in <code>vite.config.js.</code>\n  </div>\n</div>\n`\n\nconst fileRE = /(?:[a-zA-Z]:\\\\|\\/).*?:\\d+:\\d+/g\nconst codeframeRE = /^(?:>?\\s+\\d+\\s+\\|.*|\\s+\\|\\s*\\^.*)\\r?\\n/gm\n\nexport class ErrorOverlay extends HTMLElement {\n  root: ShadowRoot\n\n  constructor(err: ErrorPayload['err']) {\n    super()\n    this.root = this.attachShadow({ mode: 'open' })\n    this.root.innerHTML = template\n\n    codeframeRE.lastIndex = 0\n    const hasFrame = err.frame && codeframeRE.test(err.frame)\n    const message = hasFrame\n      ? err.message.replace(codeframeRE, '')\n      : err.message\n    if (err.plugin) {\n      this.text('.plugin', `[plugin:${err.plugin}] `)\n    }\n    this.text('.message-body', message.trim())\n\n    const [file] = (err.loc?.file || err.id || 'unknown file').split(`?`)\n    if (err.loc) {\n      this.text('.file', `${file}:${err.loc.line}:${err.loc.column}`, true)\n    } else if (err.id) {\n      this.text('.file', file)\n    }\n\n    if (hasFrame) {\n      this.text('.frame', err.frame!.trim())\n    }\n    this.text('.stack', err.stack, true)\n\n    this.root.querySelector('.window')!.addEventListener('click', (e) => {\n      e.stopPropagation()\n    })\n    this.addEventListener('click', () => {\n      this.close()\n    })\n  }\n\n  text(selector: string, text: string, linkFiles = false): void {\n    const el = this.root.querySelector(selector)!\n    if (!linkFiles) {\n      el.textContent = text\n    } else {\n      let curIndex = 0\n      let match: RegExpExecArray | null\n      while ((match = fileRE.exec(text))) {\n        const { 0: file, index } = match\n        if (index != null) {\n          const frag = text.slice(curIndex, index)\n          el.appendChild(document.createTextNode(frag))\n          const link = document.createElement('a')\n          link.textContent = file\n          link.className = 'file-link'\n          link.onclick = () => {\n            fetch('/__open-in-editor?file=' + encodeURIComponent(file))\n          }\n          el.appendChild(link)\n          curIndex += frag.length + file.length\n        }\n      }\n    }\n  }\n\n  close(): void {\n    this.parentNode?.removeChild(this)\n  }\n}\n\nexport const overlayId = 'vite-error-overlay'\n!customElements.get(overlayId) && customElements.define(overlayId, ErrorOverlay)\n", "import {\n  ErrorPayload,\n  FullReloadPayload,\n  HMRPayload,\n  PrunePayload,\n  Update,\n  UpdatePayload\n} from 'types/hmrPayload'\nimport { CustomEventName } from 'types/customEvent'\nimport { ErrorOverlay, overlayId } from './overlay'\n// eslint-disable-next-line node/no-missing-import\nimport '@vite/env'\n\n// injected by the hmr plugin when served\ndeclare const __BASE__: string\ndeclare const __HMR_PROTOCOL__: string\ndeclare const __HMR_HOSTNAME__: string\ndeclare const __HMR_PORT__: string\ndeclare const __HMR_TIMEOUT__: number\ndeclare const __HMR_ENABLE_OVERLAY__: boolean\n\nconsole.log('[vite] connecting...')\n\n// use server configuration, then fallback to inference\nconst socketProtocol =\n  __HMR_PROTOCOL__ || (location.protocol === 'https:' ? 'wss' : 'ws')\nconst socketHost = `${__HMR_HOSTNAME__ || location.hostname}:${__HMR_PORT__}`\nconst socket = new WebSocket(`${socketProtocol}://${socketHost}`, 'vite-hmr')\nconst base = __BASE__ || '/'\n\nfunction warnFailedFetch(err: Error, path: string | string[]) {\n  if (!err.message.match('fetch')) {\n    console.error(err)\n  }\n  console.error(\n    `[hmr] Failed to reload ${path}. ` +\n      `This could be due to syntax errors or importing non-existent ` +\n      `modules. (see errors above)`\n  )\n}\n\n// Listen for messages\nsocket.addEventListener('message', async ({ data }) => {\n  handleMessage(JSON.parse(data))\n})\n\nlet isFirstUpdate = true\n\nasync function handleMessage(payload: HMRPayload) {\n  switch (payload.type) {\n    case 'connected':\n      console.log(`[vite] connected.`)\n      // proxy(nginx, docker) hmr ws maybe caused timeout,\n      // so send ping package let ws keep alive.\n      setInterval(() => socket.send('ping'), __HMR_TIMEOUT__)\n      break\n    case 'update':\n      notifyListeners('vite:beforeUpdate', payload)\n      // if this is the first update and there's already an error overlay, it\n      // means the page opened with existing server compile error and the whole\n      // module script failed to load (since one of the nested imports is 500).\n      // in this case a normal update won't work and a full reload is needed.\n      if (isFirstUpdate && hasErrorOverlay()) {\n        window.location.reload()\n        return\n      } else {\n        clearErrorOverlay()\n        isFirstUpdate = false\n      }\n      payload.updates.forEach((update) => {\n        if (update.type === 'js-update') {\n          queueUpdate(fetchUpdate(update))\n        } else {\n          // css-update\n          // this is only sent when a css file referenced with <link> is updated\n          let { path, timestamp } = update\n          path = path.replace(/\\?.*/, '')\n          // can't use querySelector with `[href*=]` here since the link may be\n          // using relative paths so we need to use link.href to grab the full\n          // URL for the include check.\n          const el = (\n            [].slice.call(\n              document.querySelectorAll(`link`)\n            ) as HTMLLinkElement[]\n          ).find((e) => e.href.includes(path))\n          if (el) {\n            const newPath = `${base}${path.slice(1)}${\n              path.includes('?') ? '&' : '?'\n            }t=${timestamp}`\n            el.href = new URL(newPath, el.href).href\n          }\n          console.log(`[vite] css hot updated: ${path}`)\n        }\n      })\n      break\n    case 'custom': {\n      notifyListeners(payload.event as CustomEventName<any>, payload.data)\n      break\n    }\n    case 'full-reload':\n      notifyListeners('vite:beforeFullReload', payload)\n      if (payload.path && payload.path.endsWith('.html')) {\n        // if html file is edited, only reload the page if the browser is\n        // currently on that page.\n        const pagePath = location.pathname\n        const payloadPath = base + payload.path.slice(1)\n        if (\n          pagePath === payloadPath ||\n          (pagePath.endsWith('/') && pagePath + 'index.html' === payloadPath)\n        ) {\n          location.reload()\n        }\n        return\n      } else {\n        location.reload()\n      }\n      break\n    case 'prune':\n      notifyListeners('vite:beforePrune', payload)\n      // After an HMR update, some modules are no longer imported on the page\n      // but they may have left behind side effects that need to be cleaned up\n      // (.e.g style injections)\n      // TODO Trigger their dispose callbacks.\n      payload.paths.forEach((path) => {\n        const fn = pruneMap.get(path)\n        if (fn) {\n          fn(dataMap.get(path))\n        }\n      })\n      break\n    case 'error': {\n      notifyListeners('vite:error', payload)\n      const err = payload.err\n      if (enableOverlay) {\n        createErrorOverlay(err)\n      } else {\n        console.error(\n          `[vite] Internal Server Error\\n${err.message}\\n${err.stack}`\n        )\n      }\n      break\n    }\n    default: {\n      const check: never = payload\n      return check\n    }\n  }\n}\n\nfunction notifyListeners(\n  event: 'vite:beforeUpdate',\n  payload: UpdatePayload\n): void\nfunction notifyListeners(event: 'vite:beforePrune', payload: PrunePayload): void\nfunction notifyListeners(\n  event: 'vite:beforeFullReload',\n  payload: FullReloadPayload\n): void\nfunction notifyListeners(event: 'vite:error', payload: ErrorPayload): void\nfunction notifyListeners<T extends string>(\n  event: CustomEventName<T>,\n  data: any\n): void\nfunction notifyListeners(event: string, data: any): void {\n  const cbs = customListenersMap.get(event)\n  if (cbs) {\n    cbs.forEach((cb) => cb(data))\n  }\n}\n\nconst enableOverlay = __HMR_ENABLE_OVERLAY__\n\nfunction createErrorOverlay(err: ErrorPayload['err']) {\n  if (!enableOverlay) return\n  clearErrorOverlay()\n  document.body.appendChild(new ErrorOverlay(err))\n}\n\nfunction clearErrorOverlay() {\n  document\n    .querySelectorAll(overlayId)\n    .forEach((n) => (n as ErrorOverlay).close())\n}\n\nfunction hasErrorOverlay() {\n  return document.querySelectorAll(overlayId).length\n}\n\nlet pending = false\nlet queued: Promise<(() => void) | undefined>[] = []\n\n/**\n * buffer multiple hot updates triggered by the same src change\n * so that they are invoked in the same order they were sent.\n * (otherwise the order may be inconsistent because of the http request round trip)\n */\nasync function queueUpdate(p: Promise<(() => void) | undefined>) {\n  queued.push(p)\n  if (!pending) {\n    pending = true\n    await Promise.resolve()\n    pending = false\n    const loading = [...queued]\n    queued = []\n    ;(await Promise.all(loading)).forEach((fn) => fn && fn())\n  }\n}\n\nasync function waitForSuccessfulPing(ms = 1000) {\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    try {\n      await fetch(`${base}__vite_ping`)\n      break\n    } catch (e) {\n      await new Promise((resolve) => setTimeout(resolve, ms))\n    }\n  }\n}\n\n// ping server\nsocket.addEventListener('close', async ({ wasClean }) => {\n  if (wasClean) return\n  console.log(`[vite] server connection lost. polling for restart...`)\n  await waitForSuccessfulPing()\n  location.reload()\n})\n\n// https://wicg.github.io/construct-stylesheets\nconst supportsConstructedSheet = (() => {\n  try {\n    // new CSSStyleSheet()\n    // return true\n  } catch (e) {}\n  return false\n})()\n\nconst sheetsMap = new Map()\n\nexport function updateStyle(id: string, content: string): void {\n  let style = sheetsMap.get(id)\n  if (supportsConstructedSheet && !content.includes('@import')) {\n    if (style && !(style instanceof CSSStyleSheet)) {\n      removeStyle(id)\n      style = undefined\n    }\n\n    if (!style) {\n      style = new CSSStyleSheet()\n      style.replaceSync(content)\n      // @ts-ignore\n      document.adoptedStyleSheets = [...document.adoptedStyleSheets, style]\n    } else {\n      style.replaceSync(content)\n    }\n  } else {\n    if (style && !(style instanceof HTMLStyleElement)) {\n      removeStyle(id)\n      style = undefined\n    }\n\n    if (!style) {\n      style = document.createElement('style')\n      style.setAttribute('type', 'text/css')\n      style.innerHTML = content\n      document.head.appendChild(style)\n    } else {\n      style.innerHTML = content\n    }\n  }\n  sheetsMap.set(id, style)\n}\n\nexport function removeStyle(id: string): void {\n  const style = sheetsMap.get(id)\n  if (style) {\n    if (style instanceof CSSStyleSheet) {\n      // @ts-ignore\n      const index = document.adoptedStyleSheets.indexOf(style)\n      // @ts-ignore\n      document.adoptedStyleSheets = document.adoptedStyleSheets.filter(\n        (s: CSSStyleSheet) => s !== style\n      )\n    } else {\n      document.head.removeChild(style)\n    }\n    sheetsMap.delete(id)\n  }\n}\n\nasync function fetchUpdate({ path, acceptedPath, timestamp }: Update) {\n  const mod = hotModulesMap.get(path)\n  if (!mod) {\n    // In a code-splitting project,\n    // it is common that the hot-updating module is not loaded yet.\n    // https://github.com/vitejs/vite/issues/721\n    return\n  }\n\n  const moduleMap = new Map()\n  const isSelfUpdate = path === acceptedPath\n\n  // make sure we only import each dep once\n  const modulesToUpdate = new Set<string>()\n  if (isSelfUpdate) {\n    // self update - only update self\n    modulesToUpdate.add(path)\n  } else {\n    // dep update\n    for (const { deps } of mod.callbacks) {\n      deps.forEach((dep) => {\n        if (acceptedPath === dep) {\n          modulesToUpdate.add(dep)\n        }\n      })\n    }\n  }\n\n  // determine the qualified callbacks before we re-import the modules\n  const qualifiedCallbacks = mod.callbacks.filter(({ deps }) => {\n    return deps.some((dep) => modulesToUpdate.has(dep))\n  })\n\n  await Promise.all(\n    Array.from(modulesToUpdate).map(async (dep) => {\n      const disposer = disposeMap.get(dep)\n      if (disposer) await disposer(dataMap.get(dep))\n      const [path, query] = dep.split(`?`)\n      try {\n        const newMod = await import(\n          /* @vite-ignore */\n          base +\n            path.slice(1) +\n            `?import&t=${timestamp}${query ? `&${query}` : ''}`\n        )\n        moduleMap.set(dep, newMod)\n      } catch (e) {\n        warnFailedFetch(e, dep)\n      }\n    })\n  )\n\n  return () => {\n    for (const { deps, fn } of qualifiedCallbacks) {\n      fn(deps.map((dep) => moduleMap.get(dep)))\n    }\n    const loggedPath = isSelfUpdate ? path : `${acceptedPath} via ${path}`\n    console.log(`[vite] hot updated: ${loggedPath}`)\n  }\n}\n\ninterface HotModule {\n  id: string\n  callbacks: HotCallback[]\n}\n\ninterface HotCallback {\n  // the dependencies must be fetchable paths\n  deps: string[]\n  fn: (modules: object[]) => void\n}\n\nconst hotModulesMap = new Map<string, HotModule>()\nconst disposeMap = new Map<string, (data: any) => void | Promise<void>>()\nconst pruneMap = new Map<string, (data: any) => void | Promise<void>>()\nconst dataMap = new Map<string, any>()\nconst customListenersMap = new Map<string, ((data: any) => void)[]>()\nconst ctxToListenersMap = new Map<\n  string,\n  Map<string, ((data: any) => void)[]>\n>()\n\n// Just infer the return type for now\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport const createHotContext = (ownerPath: string) => {\n  if (!dataMap.has(ownerPath)) {\n    dataMap.set(ownerPath, {})\n  }\n\n  // when a file is hot updated, a new context is created\n  // clear its stale callbacks\n  const mod = hotModulesMap.get(ownerPath)\n  if (mod) {\n    mod.callbacks = []\n  }\n\n  // clear stale custom event listeners\n  const staleListeners = ctxToListenersMap.get(ownerPath)\n  if (staleListeners) {\n    for (const [event, staleFns] of staleListeners) {\n      const listeners = customListenersMap.get(event)\n      if (listeners) {\n        customListenersMap.set(\n          event,\n          listeners.filter((l) => !staleFns.includes(l))\n        )\n      }\n    }\n  }\n\n  const newListeners = new Map()\n  ctxToListenersMap.set(ownerPath, newListeners)\n\n  function acceptDeps(deps: string[], callback: HotCallback['fn'] = () => {}) {\n    const mod: HotModule = hotModulesMap.get(ownerPath) || {\n      id: ownerPath,\n      callbacks: []\n    }\n    mod.callbacks.push({\n      deps,\n      fn: callback\n    })\n    hotModulesMap.set(ownerPath, mod)\n  }\n\n  const hot = {\n    get data() {\n      return dataMap.get(ownerPath)\n    },\n\n    accept(deps: any, callback?: any) {\n      if (typeof deps === 'function' || !deps) {\n        // self-accept: hot.accept(() => {})\n        acceptDeps([ownerPath], ([mod]) => deps && deps(mod))\n      } else if (typeof deps === 'string') {\n        // explicit deps\n        acceptDeps([deps], ([mod]) => callback && callback(mod))\n      } else if (Array.isArray(deps)) {\n        acceptDeps(deps, callback)\n      } else {\n        throw new Error(`invalid hot.accept() usage.`)\n      }\n    },\n\n    acceptDeps() {\n      throw new Error(\n        `hot.acceptDeps() is deprecated. ` +\n          `Use hot.accept() with the same signature instead.`\n      )\n    },\n\n    dispose(cb: (data: any) => void) {\n      disposeMap.set(ownerPath, cb)\n    },\n\n    prune(cb: (data: any) => void) {\n      pruneMap.set(ownerPath, cb)\n    },\n\n    // TODO\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    decline() {},\n\n    invalidate() {\n      // TODO should tell the server to re-perform hmr propagation\n      // from this module as root\n      location.reload()\n    },\n\n    // custom events\n    on: (event: string, cb: (data: any) => void) => {\n      const addToMap = (map: Map<string, any[]>) => {\n        const existing = map.get(event) || []\n        existing.push(cb)\n        map.set(event, existing)\n      }\n      addToMap(customListenersMap)\n      addToMap(newListeners)\n    }\n  }\n\n  return hot\n}\n\n/**\n * urls here are dynamic import() urls that couldn't be statically analyzed\n */\nexport function injectQuery(url: string, queryToInject: string): string {\n  // skip urls that won't be handled by vite\n  if (!url.startsWith('.') && !url.startsWith('/')) {\n    return url\n  }\n\n  // can't use pathname from URL since it may be relative like ../\n  const pathname = url.replace(/#.*$/, '').replace(/\\?.*$/, '')\n  const { search, hash } = new URL(url, 'http://vitejs.dev')\n\n  return `${pathname}?${queryToInject}${search ? `&` + search.slice(1) : ''}${\n    hash || ''\n  }`\n}\n"], "names": [], "mappings": ";;AAEA,MAAM,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4GzB,CAAA;AAED,MAAM,MAAM,GAAG,gCAAgC,CAAA;AAC/C,MAAM,WAAW,GAAG,0CAA0C,CAAA;MAEjD,YAAa,SAAQ,WAAW;IAG3C,YAAY,GAAwB;;QAClC,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QAE9B,WAAW,CAAC,SAAS,GAAG,CAAC,CAAA;QACzB,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACzD,MAAM,OAAO,GAAG,QAAQ;cACpB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;cACpC,GAAG,CAAC,OAAO,CAAA;QACf,IAAI,GAAG,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC,CAAA;SAChD;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;QAE1C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,MAAA,GAAG,CAAC,GAAG,0CAAE,IAAI,KAAI,GAAG,CAAC,EAAE,IAAI,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;QACrE,IAAI,GAAG,CAAC,GAAG,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAA;SACtE;aAAM,IAAI,GAAG,CAAC,EAAE,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SACzB;QAED,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAM,CAAC,IAAI,EAAE,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAEpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9D,CAAC,CAAC,eAAe,EAAE,CAAA;SACpB,CAAC,CAAA;QACF,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;YAC7B,IAAI,CAAC,KAAK,EAAE,CAAA;SACb,CAAC,CAAA;KACH;IAED,IAAI,CAAC,QAAgB,EAAE,IAAY,EAAE,SAAS,GAAG,KAAK;QACpD,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAA;QAC7C,IAAI,CAAC,SAAS,EAAE;YACd,EAAE,CAAC,WAAW,GAAG,IAAI,CAAA;SACtB;aAAM;YACL,IAAI,QAAQ,GAAG,CAAC,CAAA;YAChB,IAAI,KAA6B,CAAA;YACjC,QAAQ,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAClC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAA;gBAChC,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;oBACxC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA;oBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;oBACxC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;oBACvB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAA;oBAC5B,IAAI,CAAC,OAAO,GAAG;wBACb,KAAK,CAAC,yBAAyB,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAA;qBAC5D,CAAA;oBACD,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;oBACpB,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;iBACtC;aACF;SACF;KACF;IAED,KAAK;;QACH,MAAA,IAAI,CAAC,UAAU,0CAAE,WAAW,CAAC,IAAI,CAAC,CAAA;KACnC;CACF;AAEM,MAAM,SAAS,GAAG,oBAAoB,CAAA;AAC7C,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC;;ACnKhF,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;AAEnC;AACA,MAAM,cAAc,GAClB,gBAAgB,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,CAAA;AACrE,MAAM,UAAU,GAAG,GAAG,gBAAgB,IAAI,QAAQ,CAAC,QAAQ,IAAI,YAAY,EAAE,CAAA;AAC7E,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,cAAc,MAAM,UAAU,EAAE,EAAE,UAAU,CAAC,CAAA;AAC7E,MAAM,IAAI,GAAG,QAAQ,IAAI,GAAG,CAAA;AAE5B,SAAS,eAAe,CAAC,GAAU,EAAE,IAAuB;IAC1D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;QAC/B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;KACnB;IACD,OAAO,CAAC,KAAK,CACX,0BAA0B,IAAI,IAAI;QAChC,+DAA+D;QAC/D,6BAA6B,CAChC,CAAA;AACH,CAAC;AAED;AACA,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;IAChD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;AACjC,CAAC,CAAC,CAAA;AAEF,IAAI,aAAa,GAAG,IAAI,CAAA;AAExB,eAAe,aAAa,CAAC,OAAmB;IAC9C,QAAQ,OAAO,CAAC,IAAI;QAClB,KAAK,WAAW;YACd,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;;;YAGhC,WAAW,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,eAAe,CAAC,CAAA;YACvD,MAAK;QACP,KAAK,QAAQ;YACX,eAAe,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAA;;;;;YAK7C,IAAI,aAAa,IAAI,eAAe,EAAE,EAAE;gBACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAA;gBACxB,OAAM;aACP;iBAAM;gBACL,iBAAiB,EAAE,CAAA;gBACnB,aAAa,GAAG,KAAK,CAAA;aACtB;YACD,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;gBAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;oBAC/B,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAA;iBACjC;qBAAM;;;oBAGL,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA;oBAChC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;;;;oBAI/B,MAAM,EAAE,GACN,EAAE,CAAC,KAAK,CAAC,IAAI,CACX,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAEpC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;oBACpC,IAAI,EAAE,EAAE;wBACN,MAAM,OAAO,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAC7B,KAAK,SAAS,EAAE,CAAA;wBAChB,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA;qBACzC;oBACD,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAA;iBAC/C;aACF,CAAC,CAAA;YACF,MAAK;QACP,KAAK,QAAQ,EAAE;YACb,eAAe,CAAC,OAAO,CAAC,KAA6B,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;YACpE,MAAK;SACN;QACD,KAAK,aAAa;YAChB,eAAe,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAA;YACjD,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;;;gBAGlD,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAA;gBAClC,MAAM,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAChD,IACE,QAAQ,KAAK,WAAW;qBACvB,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,YAAY,KAAK,WAAW,CAAC,EACnE;oBACA,QAAQ,CAAC,MAAM,EAAE,CAAA;iBAClB;gBACD,OAAM;aACP;iBAAM;gBACL,QAAQ,CAAC,MAAM,EAAE,CAAA;aAClB;YACD,MAAK;QACP,KAAK,OAAO;YACV,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;;;;;YAK5C,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI;gBACzB,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC7B,IAAI,EAAE,EAAE;oBACN,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;iBACtB;aACF,CAAC,CAAA;YACF,MAAK;QACP,KAAK,OAAO,EAAE;YACZ,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;YACtC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;YACvB,IAAI,aAAa,EAAE;gBACjB,kBAAkB,CAAC,GAAG,CAAC,CAAA;aACxB;iBAAM;gBACL,OAAO,CAAC,KAAK,CACX,iCAAiC,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,KAAK,EAAE,CAC7D,CAAA;aACF;YACD,MAAK;SACN;QACD,SAAS;YACP,MAAM,KAAK,GAAU,OAAO,CAAA;YAC5B,OAAO,KAAK,CAAA;SACb;KACF;AACH,CAAC;AAgBD,SAAS,eAAe,CAAC,KAAa,EAAE,IAAS;IAC/C,MAAM,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IACzC,IAAI,GAAG,EAAE;QACP,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;KAC9B;AACH,CAAC;AAED,MAAM,aAAa,GAAG,sBAAsB,CAAA;AAE5C,SAAS,kBAAkB,CAAC,GAAwB;IAClD,IAAI,CAAC,aAAa;QAAE,OAAM;IAC1B,iBAAiB,EAAE,CAAA;IACnB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,CAAA;AAClD,CAAC;AAED,SAAS,iBAAiB;IACxB,QAAQ;SACL,gBAAgB,CAAC,SAAS,CAAC;SAC3B,OAAO,CAAC,CAAC,CAAC,KAAM,CAAkB,CAAC,KAAK,EAAE,CAAC,CAAA;AAChD,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAA;AACpD,CAAC;AAED,IAAI,OAAO,GAAG,KAAK,CAAA;AACnB,IAAI,MAAM,GAAwC,EAAE,CAAA;AAEpD;;;;;AAKA,eAAe,WAAW,CAAC,CAAoC;IAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACd,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,IAAI,CAAA;QACd,MAAM,OAAO,CAAC,OAAO,EAAE,CAAA;QACvB,OAAO,GAAG,KAAK,CAAA;QACf,MAAM,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC,CAAA;QAC3B,MAAM,GAAG,EAAE,CACV;QAAA,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;KAC1D;AACH,CAAC;AAED,eAAe,qBAAqB,CAAC,EAAE,GAAG,IAAI;;IAE5C,OAAO,IAAI,EAAE;QACX,IAAI;YACF,MAAM,KAAK,CAAC,GAAG,IAAI,aAAa,CAAC,CAAA;YACjC,MAAK;SACN;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;SACxD;KACF;AACH,CAAC;AAED;AACA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;IAClD,IAAI,QAAQ;QAAE,OAAM;IACpB,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;IACpE,MAAM,qBAAqB,EAAE,CAAA;IAC7B,QAAQ,CAAC,MAAM,EAAE,CAAA;AACnB,CAAC,CAAC,CAAA;AAWF,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;SAEX,WAAW,CAAC,EAAU,EAAE,OAAe;IACrD,IAAI,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAetB;QACL,IAAI,KAAK,IAAI,EAAE,KAAK,YAAY,gBAAgB,CAAC,EAAE;YACjD,WAAW,CAAC,EAAE,CAAC,CAAA;YACf,KAAK,GAAG,SAAS,CAAA;SAClB;QAED,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;YACvC,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;YACtC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAA;YACzB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;SACjC;aAAM;YACL,KAAK,CAAC,SAAS,GAAG,OAAO,CAAA;SAC1B;KACF;IACD,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;AAC1B,CAAC;SAEe,WAAW,CAAC,EAAU;IACpC,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC/B,IAAI,KAAK,EAAE;QACT,IAAI,KAAK,YAAY,aAAa,EAAE;;YAEpB,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAC;;YAExD,QAAQ,CAAC,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,CAAC,MAAM,CAC9D,CAAC,CAAgB,KAAK,CAAC,KAAK,KAAK,CAClC,CAAA;SACF;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;SACjC;QACD,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;KACrB;AACH,CAAC;AAED,eAAe,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAU;IAClE,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACnC,IAAI,CAAC,GAAG,EAAE;;;;QAIR,OAAM;KACP;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;IAC3B,MAAM,YAAY,GAAG,IAAI,KAAK,YAAY,CAAA;;IAG1C,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAA;IACzC,IAAI,YAAY,EAAE;;QAEhB,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;KAC1B;SAAM;;QAEL,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,SAAS,EAAE;YACpC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG;gBACf,IAAI,YAAY,KAAK,GAAG,EAAE;oBACxB,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;iBACzB;aACF,CAAC,CAAA;SACH;KACF;;IAGD,MAAM,kBAAkB,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE;QACvD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;KACpD,CAAC,CAAA;IAEF,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG;QACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACpC,IAAI,QAAQ;YAAE,MAAM,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QAC9C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,IAAI;YACF,MAAM,MAAM,GAAG,MAAM;;YAEnB,IAAI;gBACF,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACb,aAAa,SAAS,GAAG,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG,EAAE,EAAE,CACtD,CAAA;YACD,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;SAC3B;QAAC,OAAO,CAAC,EAAE;YACV,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;SACxB;KACF,CAAC,CACH,CAAA;IAED,OAAO;QACL,KAAK,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,kBAAkB,EAAE;YAC7C,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;SAC1C;QACD,MAAM,UAAU,GAAG,YAAY,GAAG,IAAI,GAAG,GAAG,YAAY,QAAQ,IAAI,EAAE,CAAA;QACtE,OAAO,CAAC,GAAG,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAA;KACjD,CAAA;AACH,CAAC;AAaD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAqB,CAAA;AAClD,MAAM,UAAU,GAAG,IAAI,GAAG,EAA+C,CAAA;AACzE,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA+C,CAAA;AACvE,MAAM,OAAO,GAAG,IAAI,GAAG,EAAe,CAAA;AACtC,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAmC,CAAA;AACrE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAG9B,CAAA;AAEH;AACA;MACa,gBAAgB,GAAG,CAAC,SAAiB;IAChD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QAC3B,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;KAC3B;;;IAID,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IACxC,IAAI,GAAG,EAAE;QACP,GAAG,CAAC,SAAS,GAAG,EAAE,CAAA;KACnB;;IAGD,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IACvD,IAAI,cAAc,EAAE;QAClB,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,cAAc,EAAE;YAC9C,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,SAAS,EAAE;gBACb,kBAAkB,CAAC,GAAG,CACpB,KAAK,EACL,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAA;aACF;SACF;KACF;IAED,MAAM,YAAY,GAAG,IAAI,GAAG,EAAE,CAAA;IAC9B,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;IAE9C,SAAS,UAAU,CAAC,IAAc,EAAE,WAA8B,SAAQ;QACxE,MAAM,GAAG,GAAc,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI;YACrD,EAAE,EAAE,SAAS;YACb,SAAS,EAAE,EAAE;SACd,CAAA;QACD,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACjB,IAAI;YACJ,EAAE,EAAE,QAAQ;SACb,CAAC,CAAA;QACF,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;KAClC;IAED,MAAM,GAAG,GAAG;QACV,IAAI,IAAI;YACN,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;SAC9B;QAED,MAAM,CAAC,IAAS,EAAE,QAAc;YAC9B,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,EAAE;;gBAEvC,UAAU,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aACtD;iBAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;gBAEnC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;aACzD;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC9B,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;aAC3B;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;aAC/C;SACF;QAED,UAAU;YACR,MAAM,IAAI,KAAK,CACb,kCAAkC;gBAChC,mDAAmD,CACtD,CAAA;SACF;QAED,OAAO,CAAC,EAAuB;YAC7B,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;SAC9B;QAED,KAAK,CAAC,EAAuB;YAC3B,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;SAC5B;;;QAID,OAAO,MAAK;QAEZ,UAAU;;;YAGR,QAAQ,CAAC,MAAM,EAAE,CAAA;SAClB;;QAGD,EAAE,EAAE,CAAC,KAAa,EAAE,EAAuB;YACzC,MAAM,QAAQ,GAAG,CAAC,GAAuB;gBACvC,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;gBACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBACjB,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;aACzB,CAAA;YACD,QAAQ,CAAC,kBAAkB,CAAC,CAAA;YAC5B,QAAQ,CAAC,YAAY,CAAC,CAAA;SACvB;KACF,CAAA;IAED,OAAO,GAAG,CAAA;AACZ,EAAC;AAED;;;SAGgB,WAAW,CAAC,GAAW,EAAE,aAAqB;;IAE5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAChD,OAAO,GAAG,CAAA;KACX;;IAGD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IAC7D,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;IAE1D,OAAO,GAAG,QAAQ,IAAI,aAAa,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GACvE,IAAI,IAAI,EACV,EAAE,CAAA;AACJ;;;;"}