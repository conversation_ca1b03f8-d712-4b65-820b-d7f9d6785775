{"version": 3, "file": "use-content.js", "sources": ["../../../../../../../packages/components/popper/src/composables/use-content.ts"], "sourcesContent": ["import { computed, inject, onMounted, ref, unref, watch } from 'vue'\nimport { isUndefined } from 'lodash-unified'\nimport { usePopper } from '@element-plus/hooks'\nimport { POPPER_INJECTION_KEY } from '../constants'\nimport { buildPopperOptions, unwrapMeasurableEl } from '../utils'\n\nimport type { Modifier } from '@popperjs/core'\nimport type { PartialOptions } from '@element-plus/hooks'\nimport type { PopperContentProps } from '../content'\n\nconst DEFAULT_ARROW_OFFSET = 0\n\nexport const usePopperContent = (props: PopperContentProps) => {\n  const { popperInstanceRef, contentRef, triggerRef, role } = inject(\n    POPPER_INJECTION_KEY,\n    undefined\n  )!\n\n  const arrowRef = ref<HTMLElement>()\n  const arrowOffset = computed(() => props.arrowOffset)\n\n  const eventListenerModifier = computed(() => {\n    return {\n      name: 'eventListeners',\n      enabled: !!props.visible,\n    } as Modifier<'eventListeners', any>\n  })\n\n  const arrowModifier = computed(() => {\n    const arrowEl = unref(arrowRef)\n    const offset = unref(arrowOffset) ?? DEFAULT_ARROW_OFFSET\n    // Seems like the `phase` and `fn` is required by Modifier type\n    // But on its documentation they didn't specify that.\n    // Refer to https://popper.js.org/docs/v2/modifiers/arrow/\n    return {\n      name: 'arrow',\n      enabled: !isUndefined(arrowEl),\n      options: {\n        element: arrowEl,\n        padding: offset,\n      },\n    } as any\n  })\n\n  const options = computed<PartialOptions>(() => {\n    return {\n      onFirstUpdate: () => {\n        update()\n      },\n      ...buildPopperOptions(props, [\n        unref(arrowModifier),\n        unref(eventListenerModifier),\n      ]),\n    }\n  })\n\n  const computedReference = computed(\n    () => unwrapMeasurableEl(props.referenceEl) || unref(triggerRef)\n  )\n\n  const { attributes, state, styles, update, forceUpdate, instanceRef } =\n    usePopper(computedReference, contentRef, options)\n\n  watch(instanceRef, (instance) => (popperInstanceRef.value = instance), {\n    flush: 'sync',\n  })\n\n  onMounted(() => {\n    watch(\n      () => unref(computedReference)?.getBoundingClientRect(),\n      () => {\n        update()\n      }\n    )\n  })\n\n  return {\n    attributes,\n    arrowRef,\n    contentRef,\n    instanceRef,\n    state,\n    styles,\n    role,\n\n    forceUpdate,\n    update,\n  }\n}\n\nexport type UsePopperContentReturn = ReturnType<typeof usePopperContent>\n"], "names": ["inject", "POPPER_INJECTION_KEY", "ref", "computed", "unref", "isUndefined", "buildPopperOptions", "unwrapMeasurableEl", "usePopper", "watch", "onMounted"], "mappings": ";;;;;;;;;;AAKA,MAAM,oBAAoB,GAAG,CAAC,CAAC;AACnB,MAAC,gBAAgB,GAAG,CAAC,KAAK,KAAK;AAC3C,EAAE,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,GAAGA,UAAM,CAACC,8BAAoB,EAAE,KAAK,CAAC,CAAC,CAAC;AACnG,EAAE,MAAM,QAAQ,GAAGC,OAAG,EAAE,CAAC;AACzB,EAAE,MAAM,WAAW,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC;AACxD,EAAE,MAAM,qBAAqB,GAAGA,YAAQ,CAAC,MAAM;AAC/C,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,gBAAgB;AAC5B,MAAM,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO;AAC9B,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAGA,YAAQ,CAAC,MAAM;AACvC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,OAAO,GAAGC,SAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,MAAM,MAAM,GAAG,CAAC,EAAE,GAAGA,SAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,oBAAoB,CAAC;AACjF,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,OAAO,EAAE,CAACC,yBAAW,CAAC,OAAO,CAAC;AACpC,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO,EAAE,MAAM;AACvB,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAGF,YAAQ,CAAC,MAAM;AACjC,IAAI,OAAO;AACX,MAAM,aAAa,EAAE,MAAM;AAC3B,QAAQ,MAAM,EAAE,CAAC;AACjB,OAAO;AACP,MAAM,GAAGG,wBAAkB,CAAC,KAAK,EAAE;AACnC,QAAQF,SAAK,CAAC,aAAa,CAAC;AAC5B,QAAQA,SAAK,CAAC,qBAAqB,CAAC;AACpC,OAAO,CAAC;AACR,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAGD,YAAQ,CAAC,MAAMI,wBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,IAAIH,SAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACvG,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAGI,eAAS,CAAC,iBAAiB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC5H,EAAEC,SAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,KAAK,iBAAiB,CAAC,KAAK,GAAG,QAAQ,EAAE;AACvE,IAAI,KAAK,EAAE,MAAM;AACjB,GAAG,CAAC,CAAC;AACL,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAID,SAAK,CAAC,MAAM;AAChB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAGL,SAAK,CAAC,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAC3F,KAAK,EAAE,MAAM;AACb,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,MAAM;AACV,GAAG,CAAC;AACJ;;;;"}