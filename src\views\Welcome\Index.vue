<template>
	<div class="welcome-container">
		<el-card class="welcome-card">
			<div class="content-wrapper">
				<h1 class="welcome-title">欢迎使用{{ global?.system.name }}管理系统</h1>
				<p class="sub-text">让管理更高效，让工作更轻松</p>
				<el-button type="primary" class="start-button" @click="">立即开始使用</el-button>
				<el-col style="margin-top: 20px;">
					<el-button type="primary" class="start-button" @click="handlePrint(true)">打印预览</el-button>
				</el-col>
				<el-col style="margin-top: 20px;">
					<el-button type="primary" class="start-button" @click="handlePrint(false)">直接打印</el-button>
				</el-col>

			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
	import { ref, getCurrentInstance } from 'vue'
	import { createPrintTask } from '../../utils/print'
	const global = getCurrentInstance()?.appContext.config.globalProperties.$global

	// 打印队列
	const printQueue = ref<any[]>([])
	// 
	const isPrinting = ref(false)

	// 调用示例
	const printParams = {
		payment:100.00,
		freight:8.39,
		cityCode:'杭州 123A-456-789',
		packageCenter: "杭州",
		sender: {
			name: "张三",
			phone: "13800138000",
			address: "上海市浦东新区韵达总部大厦1号楼"
		},
		receiver: {
			name: "李四",
			phone: "13900139000",
			address: "北京市海淀区中关村大街1号院5号楼"
		},
		trackingNumber: "*************",
		weight: 1.5,
		remark: "易碎品，轻拿轻放"
	}

	const handlePrint = async (preview = true) => {
		try {
			const LODOP = await createPrintTask('yunda',printParams)
			if (preview) {
				LODOP.PREVIEW()
			} else {
				LODOP.PRINT()
			}
		} catch (error) {
			alert(error.message)
		}
	}
</script>

<style scoped>
	.welcome-container {
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.welcome-card {
		width: 500px;
		border-radius: 12px;
		box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
	}

	:deep(.el-card__body) {
		padding: 40px;
	}

	.content-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
	}

	.welcome-title {
		font-size: 24px;
		color: #303133;
		margin-bottom: 16px;
		font-weight: 500;
	}

	.sub-text {
		font-size: 14px;
		color: #909399;
		margin-bottom: 40px;
	}

	.start-button {
		width: 200px;
		height: 40px;
		font-size: 16px;
		border-radius: 20px;
		transition: all 0.3s;
	}

	.start-button:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
	}

	/* 响应式设计 */
	@media (max-width: 768px) {
		.welcome-card {
			width: 90%;
			margin: 0 20px;
		}

		.welcome-title {
			font-size: 20px;
		}

		.sub-text {
			font-size: 12px;
		}
	}
</style>