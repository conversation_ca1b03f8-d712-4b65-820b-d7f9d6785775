import { hasPermission, hasAnyPermission } from '@/utils/permission'

/**
 * 权限指令
 * v-permission="'system:user:add'" 单个权限
 * v-permission="['system:user:add', 'system:user:update']" 多个权限（任意一个）
 */
export const permission = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value) {
      let hasAuth = false
      
      if (Array.isArray(value)) {
        hasAuth = hasAnyPermission(value)
      } else {
        hasAuth = hasPermission(value)
      }
      
      if (!hasAuth) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}

/**
 * 权限指令（所有权限都需要）
 * v-permission-all="['system:user:add', 'system:user:update']"
 */
export const permissionAll = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value && Array.isArray(value)) {
      const hasAuth = value.every(code => hasPermission(code))
      
      if (!hasAuth) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}