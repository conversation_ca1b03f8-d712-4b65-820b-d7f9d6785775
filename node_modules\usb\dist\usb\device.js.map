{"version": 3, "file": "device.js", "sourceRoot": "", "sources": ["../../tsc/usb/device.ts"], "names": [], "mappings": ";;;AAAA,kCAAkC;AAClC,2CAAwC;AACxC,6CAA0C;AAG1C,MAAM,QAAQ,GAAG,CAAC,GAAoC,EAAqB,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,YAAY,UAAU,CAAC;AACjH,MAAM,eAAe,GAAG,IAAI,CAAC;AAE7B,MAAa,cAAc;IAA3B;QAMY,aAAQ,GAAG,eAAe,CAAC;IAgVvC,CAAC;IA/UG;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,IAAI,eAAe,CAAC;IAC5C,CAAC;IACD,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,IAAI,CAAC;YACD,OAAQ,IAA8B,CAAC,qBAAqB,EAAE,CAAC;QACnE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,0BAA0B;YAC1B,MAAM,KAAK,GAAI,CAAyB,CAAC,KAAK,CAAC;YAC/C,IACI,KAAK,KAAK,GAAG,CAAC,sBAAsB;gBACpC,KAAK,KAAK,GAAG,CAAC,sBAAsB,EACtC,CAAC;gBACC,OAAO,SAAS,CAAC;YACrB,CAAC;YACD,MAAM,CAAC,CAAC;QACZ,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,IAAI,CAAC;YACD,OAAQ,IAA8B,CAAC,yBAAyB,EAAE,CAAC;QACvE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,0BAA0B;YAC1B,MAAM,KAAK,GAAI,CAAyB,CAAC,KAAK,CAAC;YAC/C,IACI,KAAK,KAAK,GAAG,CAAC,sBAAsB;gBACpC,KAAK,KAAK,GAAG,CAAC,sBAAsB,EACtC,CAAC;gBACC,OAAO,EAAE,CAAC;YACd,CAAC;YACD,MAAM,CAAC,CAAC;QACZ,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAQ,IAA8B,CAAC,WAAW,EAAE,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,IAAI,CAAmB,aAAa,GAAG,IAAI;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,wEAAwE;QACxE,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,qBAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,KAAK;QACR,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAED;;;;;;;OAOG;IACI,gBAAgB,CAAmB,OAAe,EAAE,QAA2D;QAClH,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YACrC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;gBACrB,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,qBAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAChD,CAAC;YACL,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACX,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,MAAe;QAC5C,OAAQ,IAA8B,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,eAAe,CAAmB,aAAqB,EAAE,QAAgB,EAAE,MAAc,EAAE,MAAc,EAAE,cAA+B,EAC7I,QAAgG;QAChG,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,aAAa,GAAG,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,cAAwB,CAAC,CAAC,CAAE,cAAyB,CAAC,MAAM,CAAC;QAEpF,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;gBACd,MAAM,IAAI,SAAS,CAAC,+DAA+D,CAAC,CAAC;YACzF,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,SAAS,CAAC,2DAA2D,CAAC,CAAC;YACrF,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,2EAA2E;QAC3E,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAClE,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5B,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7B,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7B,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,GAAG,CAAC,GAAG,CAAC,cAAwB,EAAE,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,OAAO,EACrF,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;YACnB,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,IAAI,EAAE,CAAC;oBACP,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,yBAAyB,GAAG,MAAM,CAAC,CAAC,CAAC;gBACjH,CAAC;qBAAM,CAAC;oBACJ,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACvC,CAAC;YACL,CAAC;QACL,CAAC,CACJ,CAAC;QAEF,IAAI,CAAC;YACD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAwB,EAAE,SAAS,CAAC,CAAC,CAAC;YACrF,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAmB,IAAY;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAmB,UAAkB,EAAE,QAA+D;QAC5H,yBAAyB;QACzB,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;YACnB,QAAQ,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC;QACtB,MAAM,MAAM,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,eAAe,CAChB,GAAG,CAAC,kBAAkB,EACtB,GAAG,CAAC,6BAA6B,EACjC,CAAC,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,EAC1C,MAAM,EACN,MAAM,EACN,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACd,IAAI,KAAK,EAAE,CAAC;gBACR,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACtF,CAAC,CACJ,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAmB,QAA2E;QACjH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,oBAAoB;YACpB,OAAO,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;YACvC,uCAAuC;YACvC,OAAO,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,eAAe,CAChB,GAAG,CAAC,kBAAkB,EACtB,GAAG,CAAC,6BAA6B,EACjC,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,CAAC,EACxB,CAAC,EACD,GAAG,CAAC,kBAAkB,EACtB,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACd,IAAI,KAAK,EAAE,CAAC;gBACR,8BAA8B;gBAC9B,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,qBAAqB;oBAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACrF,OAAO,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpB,OAAO,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,eAAe,CAChB,GAAG,CAAC,kBAAkB,EACtB,GAAG,CAAC,6BAA6B,EACjC,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,CAAC,EACxB,CAAC,EACD,WAAW,EACX,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBACd,IAAI,KAAK,EAAE,CAAC;oBACR,8BAA8B;oBAC9B,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,qBAAqB;wBAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;oBACrF,OAAO,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACtC,CAAC;gBAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACpB,OAAO,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAC1C,CAAC;gBAED,MAAM,UAAU,GAAkB;oBAC9B,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC5B,eAAe,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;oBACpC,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;oBACpC,cAAc,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;oBACnC,YAAY,EAAE,EAAE;iBACnB,CAAC;gBAEF,IAAI,CAAC,GAAG,GAAG,CAAC,kBAAkB,CAAC;gBAC/B,OAAO,CAAC,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;oBACjC,MAAM,UAAU,GAAG;wBACf,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;wBAChC,eAAe,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;wBACxC,kBAAkB,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC3C,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;qBACxE,CAAC;oBAEF,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACzC,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC;gBAC5B,CAAC;gBAED,mBAAmB;gBACnB,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;gBACjC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7C,CAAC,CACJ,CAAC;QACN,CAAC,CACJ,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAmB,QAAuF;QAC5H,MAAM,YAAY,GAAiB,EAAE,CAAC;QAEtC,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YACxC,IAAI,KAAK;gBAAE,OAAO,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAE7C,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,YAAY,CAAC,IAAI,CAAC,IAAI,uBAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;YAED,QAAQ,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAtVD,wCAsVC"}