{"version": 3, "file": "cli.js", "sources": ["../../../../node_modules/cac/dist/index.mjs", "../../../../node_modules/negotiator/lib/charset.js", "../../../../node_modules/negotiator/lib/encoding.js", "../../../../node_modules/negotiator/lib/language.js", "../../../../node_modules/negotiator/lib/mediaType.js", "../../../../node_modules/negotiator/index.js", "../../../../node_modules/mime-db/index.js", "../../../../node_modules/mime-types/index.js", "../../../../node_modules/accepts/index.js", "../../../../node_modules/compression/node_modules/bytes/index.js", "../../../../node_modules/compressible/index.js", "../../../../node_modules/compression/node_modules/debug/src/debug.js", "../../../../node_modules/compression/node_modules/debug/src/browser.js", "../../../../node_modules/compression/node_modules/debug/src/node.js", "../../../../node_modules/compression/node_modules/debug/src/index.js", "../../../../node_modules/on-headers/index.js", "../../../../node_modules/compression/index.js", "../../src/node/preview.ts", "../../src/node/cli.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\n\nfunction toArr(any) {\n\treturn any == null ? [] : Array.isArray(any) ? any : [any];\n}\n\nfunction toVal(out, key, val, opts) {\n\tvar x, old=out[key], nxt=(\n\t\t!!~opts.string.indexOf(key) ? (val == null || val === true ? '' : String(val))\n\t\t: typeof val === 'boolean' ? val\n\t\t: !!~opts.boolean.indexOf(key) ? (val === 'false' ? false : val === 'true' || (out._.push((x = +val,x * 0 === 0) ? x : val),!!val))\n\t\t: (x = +val,x * 0 === 0) ? x : val\n\t);\n\tout[key] = old == null ? nxt : (Array.isArray(old) ? old.concat(nxt) : [old, nxt]);\n}\n\nfunction mri2 (args, opts) {\n\targs = args || [];\n\topts = opts || {};\n\n\tvar k, arr, arg, name, val, out={ _:[] };\n\tvar i=0, j=0, idx=0, len=args.length;\n\n\tconst alibi = opts.alias !== void 0;\n\tconst strict = opts.unknown !== void 0;\n\tconst defaults = opts.default !== void 0;\n\n\topts.alias = opts.alias || {};\n\topts.string = toArr(opts.string);\n\topts.boolean = toArr(opts.boolean);\n\n\tif (alibi) {\n\t\tfor (k in opts.alias) {\n\t\t\tarr = opts.alias[k] = toArr(opts.alias[k]);\n\t\t\tfor (i=0; i < arr.length; i++) {\n\t\t\t\t(opts.alias[arr[i]] = arr.concat(k)).splice(i, 1);\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (i=opts.boolean.length; i-- > 0;) {\n\t\tarr = opts.alias[opts.boolean[i]] || [];\n\t\tfor (j=arr.length; j-- > 0;) opts.boolean.push(arr[j]);\n\t}\n\n\tfor (i=opts.string.length; i-- > 0;) {\n\t\tarr = opts.alias[opts.string[i]] || [];\n\t\tfor (j=arr.length; j-- > 0;) opts.string.push(arr[j]);\n\t}\n\n\tif (defaults) {\n\t\tfor (k in opts.default) {\n\t\t\tname = typeof opts.default[k];\n\t\t\tarr = opts.alias[k] = opts.alias[k] || [];\n\t\t\tif (opts[name] !== void 0) {\n\t\t\t\topts[name].push(k);\n\t\t\t\tfor (i=0; i < arr.length; i++) {\n\t\t\t\t\topts[name].push(arr[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tconst keys = strict ? Object.keys(opts.alias) : [];\n\n\tfor (i=0; i < len; i++) {\n\t\targ = args[i];\n\n\t\tif (arg === '--') {\n\t\t\tout._ = out._.concat(args.slice(++i));\n\t\t\tbreak;\n\t\t}\n\n\t\tfor (j=0; j < arg.length; j++) {\n\t\t\tif (arg.charCodeAt(j) !== 45) break; // \"-\"\n\t\t}\n\n\t\tif (j === 0) {\n\t\t\tout._.push(arg);\n\t\t} else if (arg.substring(j, j + 3) === 'no-') {\n\t\t\tname = arg.substring(j + 3);\n\t\t\tif (strict && !~keys.indexOf(name)) {\n\t\t\t\treturn opts.unknown(arg);\n\t\t\t}\n\t\t\tout[name] = false;\n\t\t} else {\n\t\t\tfor (idx=j+1; idx < arg.length; idx++) {\n\t\t\t\tif (arg.charCodeAt(idx) === 61) break; // \"=\"\n\t\t\t}\n\n\t\t\tname = arg.substring(j, idx);\n\t\t\tval = arg.substring(++idx) || (i+1 === len || (''+args[i+1]).charCodeAt(0) === 45 || args[++i]);\n\t\t\tarr = (j === 2 ? [name] : name);\n\n\t\t\tfor (idx=0; idx < arr.length; idx++) {\n\t\t\t\tname = arr[idx];\n\t\t\t\tif (strict && !~keys.indexOf(name)) return opts.unknown('-'.repeat(j) + name);\n\t\t\t\ttoVal(out, name, (idx + 1 < arr.length) || val, opts);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (defaults) {\n\t\tfor (k in opts.default) {\n\t\t\tif (out[k] === void 0) {\n\t\t\t\tout[k] = opts.default[k];\n\t\t\t}\n\t\t}\n\t}\n\n\tif (alibi) {\n\t\tfor (k in out) {\n\t\t\tarr = opts.alias[k] || [];\n\t\t\twhile (arr.length > 0) {\n\t\t\t\tout[arr.shift()] = out[k];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn out;\n}\n\nconst removeBrackets = (v) => v.replace(/[<[].+/, \"\").trim();\nconst findAllBrackets = (v) => {\n  const ANGLED_BRACKET_RE_GLOBAL = /<([^>]+)>/g;\n  const SQUARE_BRACKET_RE_GLOBAL = /\\[([^\\]]+)\\]/g;\n  const res = [];\n  const parse = (match) => {\n    let variadic = false;\n    let value = match[1];\n    if (value.startsWith(\"...\")) {\n      value = value.slice(3);\n      variadic = true;\n    }\n    return {\n      required: match[0].startsWith(\"<\"),\n      value,\n      variadic\n    };\n  };\n  let angledMatch;\n  while (angledMatch = ANGLED_BRACKET_RE_GLOBAL.exec(v)) {\n    res.push(parse(angledMatch));\n  }\n  let squareMatch;\n  while (squareMatch = SQUARE_BRACKET_RE_GLOBAL.exec(v)) {\n    res.push(parse(squareMatch));\n  }\n  return res;\n};\nconst getMriOptions = (options) => {\n  const result = {alias: {}, boolean: []};\n  for (const [index, option] of options.entries()) {\n    if (option.names.length > 1) {\n      result.alias[option.names[0]] = option.names.slice(1);\n    }\n    if (option.isBoolean) {\n      if (option.negated) {\n        const hasStringTypeOption = options.some((o, i) => {\n          return i !== index && o.names.some((name) => option.names.includes(name)) && typeof o.required === \"boolean\";\n        });\n        if (!hasStringTypeOption) {\n          result.boolean.push(option.names[0]);\n        }\n      } else {\n        result.boolean.push(option.names[0]);\n      }\n    }\n  }\n  return result;\n};\nconst findLongest = (arr) => {\n  return arr.sort((a, b) => {\n    return a.length > b.length ? -1 : 1;\n  })[0];\n};\nconst padRight = (str, length) => {\n  return str.length >= length ? str : `${str}${\" \".repeat(length - str.length)}`;\n};\nconst camelcase = (input) => {\n  return input.replace(/([a-z])-([a-z])/g, (_, p1, p2) => {\n    return p1 + p2.toUpperCase();\n  });\n};\nconst setDotProp = (obj, keys, val) => {\n  let i = 0;\n  let length = keys.length;\n  let t = obj;\n  let x;\n  for (; i < length; ++i) {\n    x = t[keys[i]];\n    t = t[keys[i]] = i === length - 1 ? val : x != null ? x : !!~keys[i + 1].indexOf(\".\") || !(+keys[i + 1] > -1) ? {} : [];\n  }\n};\nconst setByType = (obj, transforms) => {\n  for (const key of Object.keys(transforms)) {\n    const transform = transforms[key];\n    if (transform.shouldTransform) {\n      obj[key] = Array.prototype.concat.call([], obj[key]);\n      if (typeof transform.transformFunction === \"function\") {\n        obj[key] = obj[key].map(transform.transformFunction);\n      }\n    }\n  }\n};\nconst getFileName = (input) => {\n  const m = /([^\\\\\\/]+)$/.exec(input);\n  return m ? m[1] : \"\";\n};\nconst camelcaseOptionName = (name) => {\n  return name.split(\".\").map((v, i) => {\n    return i === 0 ? camelcase(v) : v;\n  }).join(\".\");\n};\nclass CACError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = this.constructor.name;\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, this.constructor);\n    } else {\n      this.stack = new Error(message).stack;\n    }\n  }\n}\n\nclass Option {\n  constructor(rawName, description, config) {\n    this.rawName = rawName;\n    this.description = description;\n    this.config = Object.assign({}, config);\n    rawName = rawName.replace(/\\.\\*/g, \"\");\n    this.negated = false;\n    this.names = removeBrackets(rawName).split(\",\").map((v) => {\n      let name = v.trim().replace(/^-{1,2}/, \"\");\n      if (name.startsWith(\"no-\")) {\n        this.negated = true;\n        name = name.replace(/^no-/, \"\");\n      }\n      return camelcaseOptionName(name);\n    }).sort((a, b) => a.length > b.length ? 1 : -1);\n    this.name = this.names[this.names.length - 1];\n    if (this.negated && this.config.default == null) {\n      this.config.default = true;\n    }\n    if (rawName.includes(\"<\")) {\n      this.required = true;\n    } else if (rawName.includes(\"[\")) {\n      this.required = false;\n    } else {\n      this.isBoolean = true;\n    }\n  }\n}\n\nconst processArgs = process.argv;\nconst platformInfo = `${process.platform}-${process.arch} node-${process.version}`;\n\nclass Command {\n  constructor(rawName, description, config = {}, cli) {\n    this.rawName = rawName;\n    this.description = description;\n    this.config = config;\n    this.cli = cli;\n    this.options = [];\n    this.aliasNames = [];\n    this.name = removeBrackets(rawName);\n    this.args = findAllBrackets(rawName);\n    this.examples = [];\n  }\n  usage(text) {\n    this.usageText = text;\n    return this;\n  }\n  allowUnknownOptions() {\n    this.config.allowUnknownOptions = true;\n    return this;\n  }\n  ignoreOptionDefaultValue() {\n    this.config.ignoreOptionDefaultValue = true;\n    return this;\n  }\n  version(version, customFlags = \"-v, --version\") {\n    this.versionNumber = version;\n    this.option(customFlags, \"Display version number\");\n    return this;\n  }\n  example(example) {\n    this.examples.push(example);\n    return this;\n  }\n  option(rawName, description, config) {\n    const option = new Option(rawName, description, config);\n    this.options.push(option);\n    return this;\n  }\n  alias(name) {\n    this.aliasNames.push(name);\n    return this;\n  }\n  action(callback) {\n    this.commandAction = callback;\n    return this;\n  }\n  isMatched(name) {\n    return this.name === name || this.aliasNames.includes(name);\n  }\n  get isDefaultCommand() {\n    return this.name === \"\" || this.aliasNames.includes(\"!\");\n  }\n  get isGlobalCommand() {\n    return this instanceof GlobalCommand;\n  }\n  hasOption(name) {\n    name = name.split(\".\")[0];\n    return this.options.find((option) => {\n      return option.names.includes(name);\n    });\n  }\n  outputHelp() {\n    const {name, commands} = this.cli;\n    const {\n      versionNumber,\n      options: globalOptions,\n      helpCallback\n    } = this.cli.globalCommand;\n    let sections = [\n      {\n        body: `${name}${versionNumber ? `/${versionNumber}` : \"\"}`\n      }\n    ];\n    sections.push({\n      title: \"Usage\",\n      body: `  $ ${name} ${this.usageText || this.rawName}`\n    });\n    const showCommands = (this.isGlobalCommand || this.isDefaultCommand) && commands.length > 0;\n    if (showCommands) {\n      const longestCommandName = findLongest(commands.map((command) => command.rawName));\n      sections.push({\n        title: \"Commands\",\n        body: commands.map((command) => {\n          return `  ${padRight(command.rawName, longestCommandName.length)}  ${command.description}`;\n        }).join(\"\\n\")\n      });\n      sections.push({\n        title: `For more info, run any command with the \\`--help\\` flag`,\n        body: commands.map((command) => `  $ ${name}${command.name === \"\" ? \"\" : ` ${command.name}`} --help`).join(\"\\n\")\n      });\n    }\n    const options = this.isGlobalCommand ? globalOptions : [...this.options, ...globalOptions || []];\n    if (options.length > 0) {\n      const longestOptionName = findLongest(options.map((option) => option.rawName));\n      sections.push({\n        title: \"Options\",\n        body: options.map((option) => {\n          return `  ${padRight(option.rawName, longestOptionName.length)}  ${option.description} ${option.config.default === void 0 ? \"\" : `(default: ${option.config.default})`}`;\n        }).join(\"\\n\")\n      });\n    }\n    if (this.examples.length > 0) {\n      sections.push({\n        title: \"Examples\",\n        body: this.examples.map((example) => {\n          if (typeof example === \"function\") {\n            return example(name);\n          }\n          return example;\n        }).join(\"\\n\")\n      });\n    }\n    if (helpCallback) {\n      sections = helpCallback(sections) || sections;\n    }\n    console.log(sections.map((section) => {\n      return section.title ? `${section.title}:\n${section.body}` : section.body;\n    }).join(\"\\n\\n\"));\n  }\n  outputVersion() {\n    const {name} = this.cli;\n    const {versionNumber} = this.cli.globalCommand;\n    if (versionNumber) {\n      console.log(`${name}/${versionNumber} ${platformInfo}`);\n    }\n  }\n  checkRequiredArgs() {\n    const minimalArgsCount = this.args.filter((arg) => arg.required).length;\n    if (this.cli.args.length < minimalArgsCount) {\n      throw new CACError(`missing required args for command \\`${this.rawName}\\``);\n    }\n  }\n  checkUnknownOptions() {\n    const {options, globalCommand} = this.cli;\n    if (!this.config.allowUnknownOptions) {\n      for (const name of Object.keys(options)) {\n        if (name !== \"--\" && !this.hasOption(name) && !globalCommand.hasOption(name)) {\n          throw new CACError(`Unknown option \\`${name.length > 1 ? `--${name}` : `-${name}`}\\``);\n        }\n      }\n    }\n  }\n  checkOptionValue() {\n    const {options: parsedOptions, globalCommand} = this.cli;\n    const options = [...globalCommand.options, ...this.options];\n    for (const option of options) {\n      const value = parsedOptions[option.name.split(\".\")[0]];\n      if (option.required) {\n        const hasNegated = options.some((o) => o.negated && o.names.includes(option.name));\n        if (value === true || value === false && !hasNegated) {\n          throw new CACError(`option \\`${option.rawName}\\` value is missing`);\n        }\n      }\n    }\n  }\n}\nclass GlobalCommand extends Command {\n  constructor(cli) {\n    super(\"@@global@@\", \"\", {}, cli);\n  }\n}\n\nvar __assign = Object.assign;\nclass CAC extends EventEmitter {\n  constructor(name = \"\") {\n    super();\n    this.name = name;\n    this.commands = [];\n    this.rawArgs = [];\n    this.args = [];\n    this.options = {};\n    this.globalCommand = new GlobalCommand(this);\n    this.globalCommand.usage(\"<command> [options]\");\n  }\n  usage(text) {\n    this.globalCommand.usage(text);\n    return this;\n  }\n  command(rawName, description, config) {\n    const command = new Command(rawName, description || \"\", config, this);\n    command.globalCommand = this.globalCommand;\n    this.commands.push(command);\n    return command;\n  }\n  option(rawName, description, config) {\n    this.globalCommand.option(rawName, description, config);\n    return this;\n  }\n  help(callback) {\n    this.globalCommand.option(\"-h, --help\", \"Display this message\");\n    this.globalCommand.helpCallback = callback;\n    this.showHelpOnExit = true;\n    return this;\n  }\n  version(version, customFlags = \"-v, --version\") {\n    this.globalCommand.version(version, customFlags);\n    this.showVersionOnExit = true;\n    return this;\n  }\n  example(example) {\n    this.globalCommand.example(example);\n    return this;\n  }\n  outputHelp() {\n    if (this.matchedCommand) {\n      this.matchedCommand.outputHelp();\n    } else {\n      this.globalCommand.outputHelp();\n    }\n  }\n  outputVersion() {\n    this.globalCommand.outputVersion();\n  }\n  setParsedInfo({args, options}, matchedCommand, matchedCommandName) {\n    this.args = args;\n    this.options = options;\n    if (matchedCommand) {\n      this.matchedCommand = matchedCommand;\n    }\n    if (matchedCommandName) {\n      this.matchedCommandName = matchedCommandName;\n    }\n    return this;\n  }\n  unsetMatchedCommand() {\n    this.matchedCommand = void 0;\n    this.matchedCommandName = void 0;\n  }\n  parse(argv = processArgs, {\n    run = true\n  } = {}) {\n    this.rawArgs = argv;\n    if (!this.name) {\n      this.name = argv[1] ? getFileName(argv[1]) : \"cli\";\n    }\n    let shouldParse = true;\n    for (const command of this.commands) {\n      const parsed = this.mri(argv.slice(2), command);\n      const commandName = parsed.args[0];\n      if (command.isMatched(commandName)) {\n        shouldParse = false;\n        const parsedInfo = __assign(__assign({}, parsed), {\n          args: parsed.args.slice(1)\n        });\n        this.setParsedInfo(parsedInfo, command, commandName);\n        this.emit(`command:${commandName}`, command);\n      }\n    }\n    if (shouldParse) {\n      for (const command of this.commands) {\n        if (command.name === \"\") {\n          shouldParse = false;\n          const parsed = this.mri(argv.slice(2), command);\n          this.setParsedInfo(parsed, command);\n          this.emit(`command:!`, command);\n        }\n      }\n    }\n    if (shouldParse) {\n      const parsed = this.mri(argv.slice(2));\n      this.setParsedInfo(parsed);\n    }\n    if (this.options.help && this.showHelpOnExit) {\n      this.outputHelp();\n      run = false;\n      this.unsetMatchedCommand();\n    }\n    if (this.options.version && this.showVersionOnExit) {\n      this.outputVersion();\n      run = false;\n      this.unsetMatchedCommand();\n    }\n    const parsedArgv = {args: this.args, options: this.options};\n    if (run) {\n      this.runMatchedCommand();\n    }\n    if (!this.matchedCommand && this.args[0]) {\n      this.emit(\"command:*\");\n    }\n    return parsedArgv;\n  }\n  mri(argv, command) {\n    const cliOptions = [\n      ...this.globalCommand.options,\n      ...command ? command.options : []\n    ];\n    const mriOptions = getMriOptions(cliOptions);\n    let argsAfterDoubleDashes = [];\n    const doubleDashesIndex = argv.indexOf(\"--\");\n    if (doubleDashesIndex > -1) {\n      argsAfterDoubleDashes = argv.slice(doubleDashesIndex + 1);\n      argv = argv.slice(0, doubleDashesIndex);\n    }\n    let parsed = mri2(argv, mriOptions);\n    parsed = Object.keys(parsed).reduce((res, name) => {\n      return __assign(__assign({}, res), {\n        [camelcaseOptionName(name)]: parsed[name]\n      });\n    }, {_: []});\n    const args = parsed._;\n    const options = {\n      \"--\": argsAfterDoubleDashes\n    };\n    const ignoreDefault = command && command.config.ignoreOptionDefaultValue ? command.config.ignoreOptionDefaultValue : this.globalCommand.config.ignoreOptionDefaultValue;\n    let transforms = Object.create(null);\n    for (const cliOption of cliOptions) {\n      if (!ignoreDefault && cliOption.config.default !== void 0) {\n        for (const name of cliOption.names) {\n          options[name] = cliOption.config.default;\n        }\n      }\n      if (Array.isArray(cliOption.config.type)) {\n        if (transforms[cliOption.name] === void 0) {\n          transforms[cliOption.name] = Object.create(null);\n          transforms[cliOption.name][\"shouldTransform\"] = true;\n          transforms[cliOption.name][\"transformFunction\"] = cliOption.config.type[0];\n        }\n      }\n    }\n    for (const key of Object.keys(parsed)) {\n      if (key !== \"_\") {\n        const keys = key.split(\".\");\n        setDotProp(options, keys, parsed[key]);\n        setByType(options, transforms);\n      }\n    }\n    return {\n      args,\n      options\n    };\n  }\n  runMatchedCommand() {\n    const {args, options, matchedCommand: command} = this;\n    if (!command || !command.commandAction)\n      return;\n    command.checkUnknownOptions();\n    command.checkOptionValue();\n    command.checkRequiredArgs();\n    const actionArgs = [];\n    command.args.forEach((arg, index) => {\n      if (arg.variadic) {\n        actionArgs.push(args.slice(index));\n      } else {\n        actionArgs.push(args[index]);\n      }\n    });\n    actionArgs.push(options);\n    return command.commandAction.apply(this, actionArgs);\n  }\n}\n\nconst cac = (name = \"\") => new CAC(name);\nif (typeof module !== \"undefined\") {\n  module.exports = cac;\n  Object.assign(module.exports, {\n    default: cac,\n    cac,\n    CAC: CAC,\n    Command: Command\n  });\n}\n\nexport default cac;\nexport { CAC, Command, cac };\n", "/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredCharsets;\nmodule.exports.preferredCharsets = preferredCharsets;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleCharsetRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Charset header.\n * @private\n */\n\nfunction parseAcceptCharset(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var charset = parseCharset(accepts[i].trim(), i);\n\n    if (charset) {\n      accepts[j++] = charset;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a charset from the Accept-Charset header.\n * @private\n */\n\nfunction parseCharset(str, i) {\n  var match = simpleCharsetRegExp.exec(str);\n  if (!match) return null;\n\n  var charset = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    charset: charset,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a charset.\n * @private\n */\n\nfunction getCharsetPriority(charset, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(charset, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the charset.\n * @private\n */\n\nfunction specify(charset, spec, index) {\n  var s = 0;\n  if(spec.charset.toLowerCase() === charset.toLowerCase()){\n    s |= 1;\n  } else if (spec.charset !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n}\n\n/**\n * Get the preferred charsets from an Accept-Charset header.\n * @public\n */\n\nfunction preferredCharsets(accept, provided) {\n  // RFC 2616 sec 14.2: no header = *\n  var accepts = parseAcceptCharset(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all charsets\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullCharset);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getCharsetPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted charsets\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getCharset(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full charset string.\n * @private\n */\n\nfunction getFullCharset(spec) {\n  return spec.charset;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n", "/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredEncodings;\nmodule.exports.preferredEncodings = preferredEncodings;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleEncodingRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Encoding header.\n * @private\n */\n\nfunction parseAcceptEncoding(accept) {\n  var accepts = accept.split(',');\n  var hasIdentity = false;\n  var minQuality = 1;\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var encoding = parseEncoding(accepts[i].trim(), i);\n\n    if (encoding) {\n      accepts[j++] = encoding;\n      hasIdentity = hasIdentity || specify('identity', encoding);\n      minQuality = Math.min(minQuality, encoding.q || 1);\n    }\n  }\n\n  if (!hasIdentity) {\n    /*\n     * If identity doesn't explicitly appear in the accept-encoding header,\n     * it's added to the list of acceptable encoding with the lowest q\n     */\n    accepts[j++] = {\n      encoding: 'identity',\n      q: minQuality,\n      i: i\n    };\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse an encoding from the Accept-Encoding header.\n * @private\n */\n\nfunction parseEncoding(str, i) {\n  var match = simpleEncodingRegExp.exec(str);\n  if (!match) return null;\n\n  var encoding = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';');\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    encoding: encoding,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of an encoding.\n * @private\n */\n\nfunction getEncodingPriority(encoding, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(encoding, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the encoding.\n * @private\n */\n\nfunction specify(encoding, spec, index) {\n  var s = 0;\n  if(spec.encoding.toLowerCase() === encoding.toLowerCase()){\n    s |= 1;\n  } else if (spec.encoding !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred encodings from an Accept-Encoding header.\n * @public\n */\n\nfunction preferredEncodings(accept, provided) {\n  var accepts = parseAcceptEncoding(accept || '');\n\n  if (!provided) {\n    // sorted list of all encodings\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullEncoding);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getEncodingPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted encodings\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getEncoding(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full encoding string.\n * @private\n */\n\nfunction getFullEncoding(spec) {\n  return spec.encoding;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n", "/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n\n  var prefix = match[1],\n    suffix = match[2],\n    full = prefix;\n\n  if (suffix) full += \"-\" + suffix;\n\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language)\n  if (!p) return null;\n  var s = 0;\n  if(spec.full.toLowerCase() === p.full.toLowerCase()){\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all languages\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullLanguage);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n", "/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredMediaTypes;\nmodule.exports.preferredMediaTypes = preferredMediaTypes;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleMediaTypeRegExp = /^\\s*([^\\s\\/;]+)\\/([^;\\s]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept header.\n * @private\n */\n\nfunction parseAccept(accept) {\n  var accepts = splitMediaTypes(accept);\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var mediaType = parseMediaType(accepts[i].trim(), i);\n\n    if (mediaType) {\n      accepts[j++] = mediaType;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a media type from the Accept header.\n * @private\n */\n\nfunction parseMediaType(str, i) {\n  var match = simpleMediaTypeRegExp.exec(str);\n  if (!match) return null;\n\n  var params = Object.create(null);\n  var q = 1;\n  var subtype = match[2];\n  var type = match[1];\n\n  if (match[3]) {\n    var kvps = splitParameters(match[3]).map(splitKeyValuePair);\n\n    for (var j = 0; j < kvps.length; j++) {\n      var pair = kvps[j];\n      var key = pair[0].toLowerCase();\n      var val = pair[1];\n\n      // get the value, unwrapping quotes\n      var value = val && val[0] === '\"' && val[val.length - 1] === '\"'\n        ? val.substr(1, val.length - 2)\n        : val;\n\n      if (key === 'q') {\n        q = parseFloat(value);\n        break;\n      }\n\n      // store parameter\n      params[key] = value;\n    }\n  }\n\n  return {\n    type: type,\n    subtype: subtype,\n    params: params,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a media type.\n * @private\n */\n\nfunction getMediaTypePriority(type, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(type, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the media type.\n * @private\n */\n\nfunction specify(type, spec, index) {\n  var p = parseMediaType(type);\n  var s = 0;\n\n  if (!p) {\n    return null;\n  }\n\n  if(spec.type.toLowerCase() == p.type.toLowerCase()) {\n    s |= 4\n  } else if(spec.type != '*') {\n    return null;\n  }\n\n  if(spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {\n    s |= 2\n  } else if(spec.subtype != '*') {\n    return null;\n  }\n\n  var keys = Object.keys(spec.params);\n  if (keys.length > 0) {\n    if (keys.every(function (k) {\n      return spec.params[k] == '*' || (spec.params[k] || '').toLowerCase() == (p.params[k] || '').toLowerCase();\n    })) {\n      s |= 1\n    } else {\n      return null\n    }\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s,\n  }\n}\n\n/**\n * Get the preferred media types from an Accept header.\n * @public\n */\n\nfunction preferredMediaTypes(accept, provided) {\n  // RFC 2616 sec 14.2: no header = */*\n  var accepts = parseAccept(accept === undefined ? '*/*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all types\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullType);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getMediaTypePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted types\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getType(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full type string.\n * @private\n */\n\nfunction getFullType(spec) {\n  return spec.type + '/' + spec.subtype;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n\n/**\n * Count the number of quotes in a string.\n * @private\n */\n\nfunction quoteCount(string) {\n  var count = 0;\n  var index = 0;\n\n  while ((index = string.indexOf('\"', index)) !== -1) {\n    count++;\n    index++;\n  }\n\n  return count;\n}\n\n/**\n * Split a key value pair.\n * @private\n */\n\nfunction splitKeyValuePair(str) {\n  var index = str.indexOf('=');\n  var key;\n  var val;\n\n  if (index === -1) {\n    key = str;\n  } else {\n    key = str.substr(0, index);\n    val = str.substr(index + 1);\n  }\n\n  return [key, val];\n}\n\n/**\n * Split an Accept header into media types.\n * @private\n */\n\nfunction splitMediaTypes(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 1, j = 0; i < accepts.length; i++) {\n    if (quoteCount(accepts[j]) % 2 == 0) {\n      accepts[++j] = accepts[i];\n    } else {\n      accepts[j] += ',' + accepts[i];\n    }\n  }\n\n  // trim accepts\n  accepts.length = j + 1;\n\n  return accepts;\n}\n\n/**\n * Split a string of parameters.\n * @private\n */\n\nfunction splitParameters(str) {\n  var parameters = str.split(';');\n\n  for (var i = 1, j = 0; i < parameters.length; i++) {\n    if (quoteCount(parameters[j]) % 2 == 0) {\n      parameters[++j] = parameters[i];\n    } else {\n      parameters[j] += ';' + parameters[i];\n    }\n  }\n\n  // trim parameters\n  parameters.length = j + 1;\n\n  for (var i = 0; i < parameters.length; i++) {\n    parameters[i] = parameters[i].trim();\n  }\n\n  return parameters;\n}\n", "/*!\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Cached loaded submodules.\n * @private\n */\n\nvar modules = Object.create(null);\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Negotiator;\nmodule.exports.Negotiator = Negotiator;\n\n/**\n * Create a Negotiator instance from a request.\n * @param {object} request\n * @public\n */\n\nfunction Negotiator(request) {\n  if (!(this instanceof Negotiator)) {\n    return new Negotiator(request);\n  }\n\n  this.request = request;\n}\n\nNegotiator.prototype.charset = function charset(available) {\n  var set = this.charsets(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.charsets = function charsets(available) {\n  var preferredCharsets = loadModule('charset').preferredCharsets;\n  return preferredCharsets(this.request.headers['accept-charset'], available);\n};\n\nNegotiator.prototype.encoding = function encoding(available) {\n  var set = this.encodings(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.encodings = function encodings(available) {\n  var preferredEncodings = loadModule('encoding').preferredEncodings;\n  return preferredEncodings(this.request.headers['accept-encoding'], available);\n};\n\nNegotiator.prototype.language = function language(available) {\n  var set = this.languages(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.languages = function languages(available) {\n  var preferredLanguages = loadModule('language').preferredLanguages;\n  return preferredLanguages(this.request.headers['accept-language'], available);\n};\n\nNegotiator.prototype.mediaType = function mediaType(available) {\n  var set = this.mediaTypes(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.mediaTypes = function mediaTypes(available) {\n  var preferredMediaTypes = loadModule('mediaType').preferredMediaTypes;\n  return preferredMediaTypes(this.request.headers.accept, available);\n};\n\n// Backwards compatibility\nNegotiator.prototype.preferredCharset = Negotiator.prototype.charset;\nNegotiator.prototype.preferredCharsets = Negotiator.prototype.charsets;\nNegotiator.prototype.preferredEncoding = Negotiator.prototype.encoding;\nNegotiator.prototype.preferredEncodings = Negotiator.prototype.encodings;\nNegotiator.prototype.preferredLanguage = Negotiator.prototype.language;\nNegotiator.prototype.preferredLanguages = Negotiator.prototype.languages;\nNegotiator.prototype.preferredMediaType = Negotiator.prototype.mediaType;\nNegotiator.prototype.preferredMediaTypes = Negotiator.prototype.mediaTypes;\n\n/**\n * Load the given module.\n * @private\n */\n\nfunction loadModule(moduleName) {\n  var module = modules[moduleName];\n\n  if (module !== undefined) {\n    return module;\n  }\n\n  // This uses a switch for static require analysis\n  switch (moduleName) {\n    case 'charset':\n      module = require('./lib/charset');\n      break;\n    case 'encoding':\n      module = require('./lib/encoding');\n      break;\n    case 'language':\n      module = require('./lib/language');\n      break;\n    case 'mediaType':\n      module = require('./lib/mediaType');\n      break;\n    default:\n      throw new Error('Cannot find module \\'' + moduleName + '\\'');\n  }\n\n  // Store to prevent invoking require()\n  modules[moduleName] = module;\n\n  return module;\n}\n", "/*!\n * mime-db\n * Copyright(c) 2014 <PERSON>\n * MIT Licensed\n */\n\n/**\n * Module exports.\n */\n\nmodule.exports = require('./db.json')\n", "/*!\n * mime-types\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar db = require('mime-db')\nvar extname = require('path').extname\n\n/**\n * Module variables.\n * @private\n */\n\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\nvar TEXT_TYPE_REGEXP = /^text\\//i\n\n/**\n * Module exports.\n * @public\n */\n\nexports.charset = charset\nexports.charsets = { lookup: charset }\nexports.contentType = contentType\nexports.extension = extension\nexports.extensions = Object.create(null)\nexports.lookup = lookup\nexports.types = Object.create(null)\n\n// Populate the extensions/types maps\npopulateMaps(exports.extensions, exports.types)\n\n/**\n * Get the default charset for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction charset (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n  var mime = match && db[match[1].toLowerCase()]\n\n  if (mime && mime.charset) {\n    return mime.charset\n  }\n\n  // default text/* to utf-8\n  if (match && TEXT_TYPE_REGEXP.test(match[1])) {\n    return 'UTF-8'\n  }\n\n  return false\n}\n\n/**\n * Create a full Content-Type header given a MIME type or extension.\n *\n * @param {string} str\n * @return {boolean|string}\n */\n\nfunction contentType (str) {\n  // TODO: should this even be in this module?\n  if (!str || typeof str !== 'string') {\n    return false\n  }\n\n  var mime = str.indexOf('/') === -1\n    ? exports.lookup(str)\n    : str\n\n  if (!mime) {\n    return false\n  }\n\n  // TODO: use content-type or other module\n  if (mime.indexOf('charset') === -1) {\n    var charset = exports.charset(mime)\n    if (charset) mime += '; charset=' + charset.toLowerCase()\n  }\n\n  return mime\n}\n\n/**\n * Get the default extension for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction extension (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n\n  // get extensions\n  var exts = match && exports.extensions[match[1].toLowerCase()]\n\n  if (!exts || !exts.length) {\n    return false\n  }\n\n  return exts[0]\n}\n\n/**\n * Lookup the MIME type for a file path/extension.\n *\n * @param {string} path\n * @return {boolean|string}\n */\n\nfunction lookup (path) {\n  if (!path || typeof path !== 'string') {\n    return false\n  }\n\n  // get the extension (\"ext\" or \".ext\" or full path)\n  var extension = extname('x.' + path)\n    .toLowerCase()\n    .substr(1)\n\n  if (!extension) {\n    return false\n  }\n\n  return exports.types[extension] || false\n}\n\n/**\n * Populate the extensions and types maps.\n * @private\n */\n\nfunction populateMaps (extensions, types) {\n  // source preference (least -> most)\n  var preference = ['nginx', 'apache', undefined, 'iana']\n\n  Object.keys(db).forEach(function forEachMimeType (type) {\n    var mime = db[type]\n    var exts = mime.extensions\n\n    if (!exts || !exts.length) {\n      return\n    }\n\n    // mime -> extensions\n    extensions[type] = exts\n\n    // extension -> mime\n    for (var i = 0; i < exts.length; i++) {\n      var extension = exts[i]\n\n      if (types[extension]) {\n        var from = preference.indexOf(db[types[extension]].source)\n        var to = preference.indexOf(mime.source)\n\n        if (types[extension] !== 'application/octet-stream' &&\n          (from > to || (from === to && types[extension].substr(0, 12) === 'application/'))) {\n          // skip the remapping\n          continue\n        }\n      }\n\n      // set the extension -> mime\n      types[extension] = type\n    }\n  })\n}\n", "/*!\n * accepts\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar Negotiator = require('negotiator')\nvar mime = require('mime-types')\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Accepts\n\n/**\n * Create a new Accepts object for the given req.\n *\n * @param {object} req\n * @public\n */\n\nfunction Accepts (req) {\n  if (!(this instanceof Accepts)) {\n    return new Accepts(req)\n  }\n\n  this.headers = req.headers\n  this.negotiator = new Negotiator(req)\n}\n\n/**\n * Check if the given `type(s)` is acceptable, returning\n * the best match when true, otherwise `undefined`, in which\n * case you should respond with 406 \"Not Acceptable\".\n *\n * The `type` value may be a single mime type string\n * such as \"application/json\", the extension name\n * such as \"json\" or an array `[\"json\", \"html\", \"text/plain\"]`. When a list\n * or array is given the _best_ match, if any is returned.\n *\n * Examples:\n *\n *     // Accept: text/html\n *     this.types('html');\n *     // => \"html\"\n *\n *     // Accept: text/*, application/json\n *     this.types('html');\n *     // => \"html\"\n *     this.types('text/html');\n *     // => \"text/html\"\n *     this.types('json', 'text');\n *     // => \"json\"\n *     this.types('application/json');\n *     // => \"application/json\"\n *\n *     // Accept: text/*, application/json\n *     this.types('image/png');\n *     this.types('png');\n *     // => undefined\n *\n *     // Accept: text/*;q=.5, application/json\n *     this.types(['html', 'json']);\n *     this.types('html', 'json');\n *     // => \"json\"\n *\n * @param {String|Array} types...\n * @return {String|Array|Boolean}\n * @public\n */\n\nAccepts.prototype.type =\nAccepts.prototype.types = function (types_) {\n  var types = types_\n\n  // support flattened arguments\n  if (types && !Array.isArray(types)) {\n    types = new Array(arguments.length)\n    for (var i = 0; i < types.length; i++) {\n      types[i] = arguments[i]\n    }\n  }\n\n  // no types, return all requested types\n  if (!types || types.length === 0) {\n    return this.negotiator.mediaTypes()\n  }\n\n  // no accept header, return first given type\n  if (!this.headers.accept) {\n    return types[0]\n  }\n\n  var mimes = types.map(extToMime)\n  var accepts = this.negotiator.mediaTypes(mimes.filter(validMime))\n  var first = accepts[0]\n\n  return first\n    ? types[mimes.indexOf(first)]\n    : false\n}\n\n/**\n * Return accepted encodings or best fit based on `encodings`.\n *\n * Given `Accept-Encoding: gzip, deflate`\n * an array sorted by quality is returned:\n *\n *     ['gzip', 'deflate']\n *\n * @param {String|Array} encodings...\n * @return {String|Array}\n * @public\n */\n\nAccepts.prototype.encoding =\nAccepts.prototype.encodings = function (encodings_) {\n  var encodings = encodings_\n\n  // support flattened arguments\n  if (encodings && !Array.isArray(encodings)) {\n    encodings = new Array(arguments.length)\n    for (var i = 0; i < encodings.length; i++) {\n      encodings[i] = arguments[i]\n    }\n  }\n\n  // no encodings, return all requested encodings\n  if (!encodings || encodings.length === 0) {\n    return this.negotiator.encodings()\n  }\n\n  return this.negotiator.encodings(encodings)[0] || false\n}\n\n/**\n * Return accepted charsets or best fit based on `charsets`.\n *\n * Given `Accept-Charset: utf-8, iso-8859-1;q=0.2, utf-7;q=0.5`\n * an array sorted by quality is returned:\n *\n *     ['utf-8', 'utf-7', 'iso-8859-1']\n *\n * @param {String|Array} charsets...\n * @return {String|Array}\n * @public\n */\n\nAccepts.prototype.charset =\nAccepts.prototype.charsets = function (charsets_) {\n  var charsets = charsets_\n\n  // support flattened arguments\n  if (charsets && !Array.isArray(charsets)) {\n    charsets = new Array(arguments.length)\n    for (var i = 0; i < charsets.length; i++) {\n      charsets[i] = arguments[i]\n    }\n  }\n\n  // no charsets, return all requested charsets\n  if (!charsets || charsets.length === 0) {\n    return this.negotiator.charsets()\n  }\n\n  return this.negotiator.charsets(charsets)[0] || false\n}\n\n/**\n * Return accepted languages or best fit based on `langs`.\n *\n * Given `Accept-Language: en;q=0.8, es, pt`\n * an array sorted by quality is returned:\n *\n *     ['es', 'pt', 'en']\n *\n * @param {String|Array} langs...\n * @return {Array|String}\n * @public\n */\n\nAccepts.prototype.lang =\nAccepts.prototype.langs =\nAccepts.prototype.language =\nAccepts.prototype.languages = function (languages_) {\n  var languages = languages_\n\n  // support flattened arguments\n  if (languages && !Array.isArray(languages)) {\n    languages = new Array(arguments.length)\n    for (var i = 0; i < languages.length; i++) {\n      languages[i] = arguments[i]\n    }\n  }\n\n  // no languages, return all requested languages\n  if (!languages || languages.length === 0) {\n    return this.negotiator.languages()\n  }\n\n  return this.negotiator.languages(languages)[0] || false\n}\n\n/**\n * Convert extnames to mime.\n *\n * @param {String} type\n * @return {String}\n * @private\n */\n\nfunction extToMime (type) {\n  return type.indexOf('/') === -1\n    ? mime.lookup(type)\n    : type\n}\n\n/**\n * Check if mime is valid.\n *\n * @param {String} type\n * @return {String}\n * @private\n */\n\nfunction validMime (type) {\n  return typeof type === 'string'\n}\n", "/*!\n * bytes\n * Copyright(c) 2012-2014 <PERSON><PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = bytes;\nmodule.exports.format = format;\nmodule.exports.parse = parse;\n\n/**\n * Module variables.\n * @private\n */\n\nvar formatThousandsRegExp = /\\B(?=(\\d{3})+(?!\\d))/g;\n\nvar formatDecimalsRegExp = /(?:\\.0*|(\\.[^0]+)0+)$/;\n\nvar map = {\n  b:  1,\n  kb: 1 << 10,\n  mb: 1 << 20,\n  gb: 1 << 30,\n  tb: ((1 << 30) * 1024)\n};\n\nvar parseRegExp = /^((-|\\+)?(\\d+(?:\\.\\d+)?)) *(kb|mb|gb|tb)$/i;\n\n/**\n * Convert the given value in bytes into a string or parse to string to an integer in bytes.\n *\n * @param {string|number} value\n * @param {{\n *  case: [string],\n *  decimalPlaces: [number]\n *  fixedDecimals: [boolean]\n *  thousandsSeparator: [string]\n *  unitSeparator: [string]\n *  }} [options] bytes options.\n *\n * @returns {string|number|null}\n */\n\nfunction bytes(value, options) {\n  if (typeof value === 'string') {\n    return parse(value);\n  }\n\n  if (typeof value === 'number') {\n    return format(value, options);\n  }\n\n  return null;\n}\n\n/**\n * Format the given value in bytes into a string.\n *\n * If the value is negative, it is kept as such. If it is a float,\n * it is rounded.\n *\n * @param {number} value\n * @param {object} [options]\n * @param {number} [options.decimalPlaces=2]\n * @param {number} [options.fixedDecimals=false]\n * @param {string} [options.thousandsSeparator=]\n * @param {string} [options.unit=]\n * @param {string} [options.unitSeparator=]\n *\n * @returns {string|null}\n * @public\n */\n\nfunction format(value, options) {\n  if (!Number.isFinite(value)) {\n    return null;\n  }\n\n  var mag = Math.abs(value);\n  var thousandsSeparator = (options && options.thousandsSeparator) || '';\n  var unitSeparator = (options && options.unitSeparator) || '';\n  var decimalPlaces = (options && options.decimalPlaces !== undefined) ? options.decimalPlaces : 2;\n  var fixedDecimals = Boolean(options && options.fixedDecimals);\n  var unit = (options && options.unit) || '';\n\n  if (!unit || !map[unit.toLowerCase()]) {\n    if (mag >= map.tb) {\n      unit = 'TB';\n    } else if (mag >= map.gb) {\n      unit = 'GB';\n    } else if (mag >= map.mb) {\n      unit = 'MB';\n    } else if (mag >= map.kb) {\n      unit = 'KB';\n    } else {\n      unit = 'B';\n    }\n  }\n\n  var val = value / map[unit.toLowerCase()];\n  var str = val.toFixed(decimalPlaces);\n\n  if (!fixedDecimals) {\n    str = str.replace(formatDecimalsRegExp, '$1');\n  }\n\n  if (thousandsSeparator) {\n    str = str.replace(formatThousandsRegExp, thousandsSeparator);\n  }\n\n  return str + unitSeparator + unit;\n}\n\n/**\n * Parse the string value into an integer in bytes.\n *\n * If no unit is given, it is assumed the value is in bytes.\n *\n * @param {number|string} val\n *\n * @returns {number|null}\n * @public\n */\n\nfunction parse(val) {\n  if (typeof val === 'number' && !isNaN(val)) {\n    return val;\n  }\n\n  if (typeof val !== 'string') {\n    return null;\n  }\n\n  // Test if the string passed is valid\n  var results = parseRegExp.exec(val);\n  var floatValue;\n  var unit = 'b';\n\n  if (!results) {\n    // Nothing could be extracted from the given string\n    floatValue = parseInt(val, 10);\n    unit = 'b'\n  } else {\n    // Retrieve the value and the unit\n    floatValue = parseFloat(results[1]);\n    unit = results[4].toLowerCase();\n  }\n\n  return Math.floor(map[unit] * floatValue);\n}\n", "/*!\n * compressible\n * Copyright(c) 2013 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar db = require('mime-db')\n\n/**\n * Module variables.\n * @private\n */\n\nvar COMPRESSIBLE_TYPE_REGEXP = /^text\\/|\\+(?:json|text|xml)$/i\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = compressible\n\n/**\n * Checks if a type is compressible.\n *\n * @param {string} type\n * @return {Boolean} compressible\n * @public\n */\n\nfunction compressible (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // strip parameters\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n  var mime = match && match[1].toLowerCase()\n  var data = db[mime]\n\n  // return database information\n  if (data && data.compressible !== undefined) {\n    return data.compressible\n  }\n\n  // fallback to regexp or unknown\n  return COMPRESSIBLE_TYPE_REGEXP.test(mime) || undefined\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  'lightseagreen',\n  'forestgreen',\n  'goldenrod',\n  'dodgerblue',\n  'darkorchid',\n  'crimson'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n", "/**\n * Module dependencies.\n */\n\nvar tty = require('tty');\nvar util = require('util');\n\n/**\n * This is the Node.js implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\n\n/**\n * Colors.\n */\n\nexports.colors = [6, 2, 3, 4, 5, 1];\n\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */\n\nexports.inspectOpts = Object.keys(process.env).filter(function (key) {\n  return /^debug_/i.test(key);\n}).reduce(function (obj, key) {\n  // camel-case\n  var prop = key\n    .substring(6)\n    .toLowerCase()\n    .replace(/_([a-z])/g, function (_, k) { return k.toUpperCase() });\n\n  // coerce string value into JS value\n  var val = process.env[key];\n  if (/^(yes|on|true|enabled)$/i.test(val)) val = true;\n  else if (/^(no|off|false|disabled)$/i.test(val)) val = false;\n  else if (val === 'null') val = null;\n  else val = Number(val);\n\n  obj[prop] = val;\n  return obj;\n}, {});\n\n/**\n * The file descriptor to write the `debug()` calls to.\n * Set the `DEBUG_FD` env variable to override with another value. i.e.:\n *\n *   $ DEBUG_FD=3 node script.js 3>debug.log\n */\n\nvar fd = parseInt(process.env.DEBUG_FD, 10) || 2;\n\nif (1 !== fd && 2 !== fd) {\n  util.deprecate(function(){}, 'except for stderr(2) and stdout(1), any other usage of DEBUG_FD is deprecated. Override debug.log if you want to use a different log function (https://git.io/debug_fd)')()\n}\n\nvar stream = 1 === fd ? process.stdout :\n             2 === fd ? process.stderr :\n             createWritableStdioStream(fd);\n\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */\n\nfunction useColors() {\n  return 'colors' in exports.inspectOpts\n    ? Boolean(exports.inspectOpts.colors)\n    : tty.isatty(fd);\n}\n\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */\n\nexports.formatters.o = function(v) {\n  this.inspectOpts.colors = this.useColors;\n  return util.inspect(v, this.inspectOpts)\n    .split('\\n').map(function(str) {\n      return str.trim()\n    }).join(' ');\n};\n\n/**\n * Map %o to `util.inspect()`, allowing multiple lines if needed.\n */\n\nexports.formatters.O = function(v) {\n  this.inspectOpts.colors = this.useColors;\n  return util.inspect(v, this.inspectOpts);\n};\n\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var name = this.namespace;\n  var useColors = this.useColors;\n\n  if (useColors) {\n    var c = this.color;\n    var prefix = '  \\u001b[3' + c + ';1m' + name + ' ' + '\\u001b[0m';\n\n    args[0] = prefix + args[0].split('\\n').join('\\n' + prefix);\n    args.push('\\u001b[3' + c + 'm+' + exports.humanize(this.diff) + '\\u001b[0m');\n  } else {\n    args[0] = new Date().toUTCString()\n      + ' ' + name + ' ' + args[0];\n  }\n}\n\n/**\n * Invokes `util.format()` with the specified arguments and writes to `stream`.\n */\n\nfunction log() {\n  return stream.write(util.format.apply(util, arguments) + '\\n');\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  if (null == namespaces) {\n    // If you set a process.env field to null or undefined, it gets cast to the\n    // string 'null' or 'undefined'. Just delete instead.\n    delete process.env.DEBUG;\n  } else {\n    process.env.DEBUG = namespaces;\n  }\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  return process.env.DEBUG;\n}\n\n/**\n * Copied from `node/src/node.js`.\n *\n * XXX: It's lame that node doesn't expose this API out-of-the-box. It also\n * relies on the undocumented `tty_wrap.guessHandleType()` which is also lame.\n */\n\nfunction createWritableStdioStream (fd) {\n  var stream;\n  var tty_wrap = process.binding('tty_wrap');\n\n  // Note stream._type is used for test-module-load-list.js\n\n  switch (tty_wrap.guessHandleType(fd)) {\n    case 'TTY':\n      stream = new tty.WriteStream(fd);\n      stream._type = 'tty';\n\n      // Hack to have stream not keep the event loop alive.\n      // See https://github.com/joyent/node/issues/1726\n      if (stream._handle && stream._handle.unref) {\n        stream._handle.unref();\n      }\n      break;\n\n    case 'FILE':\n      var fs = require('fs');\n      stream = new fs.SyncWriteStream(fd, { autoClose: false });\n      stream._type = 'fs';\n      break;\n\n    case 'PIPE':\n    case 'TCP':\n      var net = require('net');\n      stream = new net.Socket({\n        fd: fd,\n        readable: false,\n        writable: true\n      });\n\n      // FIXME Should probably have an option in net.Socket to create a\n      // stream from an existing fd which is writable only. But for now\n      // we'll just add this hack and set the `readable` member to false.\n      // Test: ./node test/fixtures/echo.js < /etc/passwd\n      stream.readable = false;\n      stream.read = null;\n      stream._type = 'pipe';\n\n      // FIXME Hack to have stream not keep the event loop alive.\n      // See https://github.com/joyent/node/issues/1726\n      if (stream._handle && stream._handle.unref) {\n        stream._handle.unref();\n      }\n      break;\n\n    default:\n      // Probably an error on in uv_guess_handle()\n      throw new Error('Implement me. Unknown stream file type!');\n  }\n\n  // For supporting legacy API we put the FD here.\n  stream.fd = fd;\n\n  stream._isStdio = true;\n\n  return stream;\n}\n\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */\n\nfunction init (debug) {\n  debug.inspectOpts = {};\n\n  var keys = Object.keys(exports.inspectOpts);\n  for (var i = 0; i < keys.length; i++) {\n    debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n  }\n}\n\n/**\n * Enable namespaces listed in `process.env.DEBUG` initially.\n */\n\nexports.enable(load());\n", "/**\n * Detect Electron renderer process, which is node, but we should\n * treat as a browser.\n */\n\nif (typeof process !== 'undefined' && process.type === 'renderer') {\n  module.exports = require('./browser.js');\n} else {\n  module.exports = require('./node.js');\n}\n", "/*!\n * on-headers\n * Copyright(c) 2014 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = onHeaders\n\n/**\n * Create a replacement writeHead method.\n *\n * @param {function} prevWriteHead\n * @param {function} listener\n * @private\n */\n\nfunction createWriteHead (prevWriteHead, listener) {\n  var fired = false\n\n  // return function with core name and argument list\n  return function writeHead (statusCode) {\n    // set headers from arguments\n    var args = setWriteHeadHeaders.apply(this, arguments)\n\n    // fire listener\n    if (!fired) {\n      fired = true\n      listener.call(this)\n\n      // pass-along an updated status code\n      if (typeof args[0] === 'number' && this.statusCode !== args[0]) {\n        args[0] = this.statusCode\n        args.length = 1\n      }\n    }\n\n    return prevWriteHead.apply(this, args)\n  }\n}\n\n/**\n * Execute a listener when a response is about to write headers.\n *\n * @param {object} res\n * @return {function} listener\n * @public\n */\n\nfunction onHeaders (res, listener) {\n  if (!res) {\n    throw new TypeError('argument res is required')\n  }\n\n  if (typeof listener !== 'function') {\n    throw new TypeError('argument listener must be a function')\n  }\n\n  res.writeHead = createWriteHead(res.writeHead, listener)\n}\n\n/**\n * Set headers contained in array on the response object.\n *\n * @param {object} res\n * @param {array} headers\n * @private\n */\n\nfunction setHeadersFromArray (res, headers) {\n  for (var i = 0; i < headers.length; i++) {\n    res.setHeader(headers[i][0], headers[i][1])\n  }\n}\n\n/**\n * Set headers contained in object on the response object.\n *\n * @param {object} res\n * @param {object} headers\n * @private\n */\n\nfunction setHeadersFromObject (res, headers) {\n  var keys = Object.keys(headers)\n  for (var i = 0; i < keys.length; i++) {\n    var k = keys[i]\n    if (k) res.setHeader(k, headers[k])\n  }\n}\n\n/**\n * Set headers and other properties on the response object.\n *\n * @param {number} statusCode\n * @private\n */\n\nfunction setWriteHeadHeaders (statusCode) {\n  var length = arguments.length\n  var headerIndex = length > 1 && typeof arguments[1] === 'string'\n    ? 2\n    : 1\n\n  var headers = length >= headerIndex + 1\n    ? arguments[headerIndex]\n    : undefined\n\n  this.statusCode = statusCode\n\n  if (Array.isArray(headers)) {\n    // handle array case\n    setHeadersFromArray(this, headers)\n  } else if (headers) {\n    // handle object case\n    setHeadersFromObject(this, headers)\n  }\n\n  // copy leading arguments\n  var args = new Array(Math.min(length, headerIndex))\n  for (var i = 0; i < args.length; i++) {\n    args[i] = arguments[i]\n  }\n\n  return args\n}\n", "/*!\n * compression\n * Copyright(c) 2010 Sencha Inc.\n * Copyright(c) 2011 <PERSON><PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar accepts = require('accepts')\nvar Buffer = require('safe-buffer').Buffer\nvar bytes = require('bytes')\nvar compressible = require('compressible')\nvar debug = require('debug')('compression')\nvar onHeaders = require('on-headers')\nvar vary = require('vary')\nvar zlib = require('zlib')\n\n/**\n * Module exports.\n */\n\nmodule.exports = compression\nmodule.exports.filter = shouldCompress\n\n/**\n * Module variables.\n * @private\n */\n\nvar cacheControlNoTransformRegExp = /(?:^|,)\\s*?no-transform\\s*?(?:,|$)/\n\n/**\n * Compress response data with gzip / deflate.\n *\n * @param {Object} [options]\n * @return {Function} middleware\n * @public\n */\n\nfunction compression (options) {\n  var opts = options || {}\n\n  // options\n  var filter = opts.filter || shouldCompress\n  var threshold = bytes.parse(opts.threshold)\n\n  if (threshold == null) {\n    threshold = 1024\n  }\n\n  return function compression (req, res, next) {\n    var ended = false\n    var length\n    var listeners = []\n    var stream\n\n    var _end = res.end\n    var _on = res.on\n    var _write = res.write\n\n    // flush\n    res.flush = function flush () {\n      if (stream) {\n        stream.flush()\n      }\n    }\n\n    // proxy\n\n    res.write = function write (chunk, encoding) {\n      if (ended) {\n        return false\n      }\n\n      if (!this._header) {\n        this._implicitHeader()\n      }\n\n      return stream\n        ? stream.write(toBuffer(chunk, encoding))\n        : _write.call(this, chunk, encoding)\n    }\n\n    res.end = function end (chunk, encoding) {\n      if (ended) {\n        return false\n      }\n\n      if (!this._header) {\n        // estimate the length\n        if (!this.getHeader('Content-Length')) {\n          length = chunkLength(chunk, encoding)\n        }\n\n        this._implicitHeader()\n      }\n\n      if (!stream) {\n        return _end.call(this, chunk, encoding)\n      }\n\n      // mark ended\n      ended = true\n\n      // write Buffer for Node.js 0.8\n      return chunk\n        ? stream.end(toBuffer(chunk, encoding))\n        : stream.end()\n    }\n\n    res.on = function on (type, listener) {\n      if (!listeners || type !== 'drain') {\n        return _on.call(this, type, listener)\n      }\n\n      if (stream) {\n        return stream.on(type, listener)\n      }\n\n      // buffer listeners for future stream\n      listeners.push([type, listener])\n\n      return this\n    }\n\n    function nocompress (msg) {\n      debug('no compression: %s', msg)\n      addListeners(res, _on, listeners)\n      listeners = null\n    }\n\n    onHeaders(res, function onResponseHeaders () {\n      // determine if request is filtered\n      if (!filter(req, res)) {\n        nocompress('filtered')\n        return\n      }\n\n      // determine if the entity should be transformed\n      if (!shouldTransform(req, res)) {\n        nocompress('no transform')\n        return\n      }\n\n      // vary\n      vary(res, 'Accept-Encoding')\n\n      // content-length below threshold\n      if (Number(res.getHeader('Content-Length')) < threshold || length < threshold) {\n        nocompress('size below threshold')\n        return\n      }\n\n      var encoding = res.getHeader('Content-Encoding') || 'identity'\n\n      // already encoded\n      if (encoding !== 'identity') {\n        nocompress('already encoded')\n        return\n      }\n\n      // head\n      if (req.method === 'HEAD') {\n        nocompress('HEAD request')\n        return\n      }\n\n      // compression method\n      var accept = accepts(req)\n      var method = accept.encoding(['gzip', 'deflate', 'identity'])\n\n      // we really don't prefer deflate\n      if (method === 'deflate' && accept.encoding(['gzip'])) {\n        method = accept.encoding(['gzip', 'identity'])\n      }\n\n      // negotiation failed\n      if (!method || method === 'identity') {\n        nocompress('not acceptable')\n        return\n      }\n\n      // compression stream\n      debug('%s compression', method)\n      stream = method === 'gzip'\n        ? zlib.createGzip(opts)\n        : zlib.createDeflate(opts)\n\n      // add buffered listeners to stream\n      addListeners(stream, stream.on, listeners)\n\n      // header fields\n      res.setHeader('Content-Encoding', method)\n      res.removeHeader('Content-Length')\n\n      // compression\n      stream.on('data', function onStreamData (chunk) {\n        if (_write.call(res, chunk) === false) {\n          stream.pause()\n        }\n      })\n\n      stream.on('end', function onStreamEnd () {\n        _end.call(res)\n      })\n\n      _on.call(res, 'drain', function onResponseDrain () {\n        stream.resume()\n      })\n    })\n\n    next()\n  }\n}\n\n/**\n * Add bufferred listeners to stream\n * @private\n */\n\nfunction addListeners (stream, on, listeners) {\n  for (var i = 0; i < listeners.length; i++) {\n    on.apply(stream, listeners[i])\n  }\n}\n\n/**\n * Get the length of a given chunk\n */\n\nfunction chunkLength (chunk, encoding) {\n  if (!chunk) {\n    return 0\n  }\n\n  return !Buffer.isBuffer(chunk)\n    ? Buffer.byteLength(chunk, encoding)\n    : chunk.length\n}\n\n/**\n * Default filter function.\n * @private\n */\n\nfunction shouldCompress (req, res) {\n  var type = res.getHeader('Content-Type')\n\n  if (type === undefined || !compressible(type)) {\n    debug('%s not compressible', type)\n    return false\n  }\n\n  return true\n}\n\n/**\n * Determine if the entity should be transformed.\n * @private\n */\n\nfunction shouldTransform (req, res) {\n  var cacheControl = res.getHeader('Cache-Control')\n\n  // Don't compress for Cache-Control: no-transform\n  // https://tools.ietf.org/html/rfc7234#section-5.2.2.4\n  return !cacheControl ||\n    !cacheControlNoTransformRegExp.test(cacheControl)\n}\n\n/**\n * Coerce arguments to Buffer\n * @private\n */\n\nfunction toBuffer (chunk, encoding) {\n  return !Buffer.isBuffer(chunk)\n    ? Buffer.from(chunk, encoding)\n    : chunk\n}\n", "import path from 'path'\nimport sirv from 'sirv'\nimport chalk from 'chalk'\nimport connect from 'connect'\nimport compression from 'compression'\nimport { ResolvedConfig, ServerOptions } from '.'\nimport { Connect } from 'types/connect'\nimport {\n  resolveHttpsConfig,\n  resolveHttpServer,\n  httpServerStart\n} from './server/http'\nimport { openBrowser } from './server/openBrowser'\nimport corsMiddleware from 'cors'\nimport { proxyMiddleware } from './server/middlewares/proxy'\nimport { printServerUrls } from './logger'\nimport { resolveHostname } from './utils'\n\nexport async function preview(\n  config: ResolvedConfig,\n  serverOptions: Pick<ServerOptions, 'port' | 'host'>\n): Promise<void> {\n  const app = connect() as Connect.Server\n  const httpServer = await resolveHttpServer(\n    config.server,\n    app,\n    await resolveHttpsConfig(config)\n  )\n\n  // cors\n  const { cors } = config.server\n  if (cors !== false) {\n    app.use(corsMiddleware(typeof cors === 'boolean' ? {} : cors))\n  }\n\n  // proxy\n  if (config.server.proxy) {\n    app.use(proxyMiddleware(httpServer, config))\n  }\n\n  app.use(compression())\n\n  const distDir = path.resolve(config.root, config.build.outDir)\n  app.use(\n    config.base,\n    sirv(distDir, {\n      etag: true,\n      dev: true,\n      single: true\n    })\n  )\n\n  const options = config.server\n  const hostname = resolveHostname(serverOptions.host ?? options.host)\n  const port = serverOptions.port ?? 5000\n  const protocol = options.https ? 'https' : 'http'\n  const logger = config.logger\n  const base = config.base\n\n  const serverPort = await httpServerStart(httpServer, {\n    port,\n    strictPort: options.strictPort,\n    host: hostname.host,\n    logger\n  })\n\n  logger.info(\n    chalk.cyan(`\\n  vite v${require('vite/package.json').version}`) +\n      chalk.green(` build preview server running at:\\n`)\n  )\n\n  printServerUrls(hostname, protocol, serverPort, base, logger.info)\n\n  if (options.open) {\n    const path = typeof options.open === 'string' ? options.open : base\n    openBrowser(\n      `${protocol}://${hostname.name}:${serverPort}${path}`,\n      true,\n      logger\n    )\n  }\n}\n", "import { cac } from 'cac'\nimport chalk from 'chalk'\nimport { BuildOptions } from './build'\nimport { ServerOptions } from './server'\nimport { createLogger, LogLevel } from './logger'\nimport { resolveConfig } from '.'\nimport { preview } from './preview'\n\nconst cli = cac('vite')\n\n// global options\ninterface GlobalCLIOptions {\n  '--'?: string[]\n  c?: boolean | string\n  config?: string\n  r?: string\n  root?: string\n  base?: string\n  l?: LogLevel\n  logLevel?: LogLevel\n  clearScreen?: boolean\n  d?: boolean | string\n  debug?: boolean | string\n  f?: string\n  filter?: string\n  m?: string\n  mode?: string\n}\n\n/**\n * removing global flags before passing as command specific sub-configs\n */\nfunction cleanOptions<Options extends GlobalCLIOptions>(\n  options: Options\n): Omit<Options, keyof GlobalCLIOptions> {\n  const ret = { ...options }\n  delete ret['--']\n  delete ret.c\n  delete ret.config\n  delete ret.r\n  delete ret.root\n  delete ret.base\n  delete ret.l\n  delete ret.logLevel\n  delete ret.clearScreen\n  delete ret.d\n  delete ret.debug\n  delete ret.f\n  delete ret.filter\n  delete ret.m\n  delete ret.mode\n  return ret\n}\n\ncli\n  .option('-c, --config <file>', `[string] use specified config file`)\n  .option('-r, --root <path>', `[string] use specified root directory`)\n  .option('--base <path>', `[string] public base path (default: /)`)\n  .option('-l, --logLevel <level>', `[string] info | warn | error | silent`)\n  .option('--clearScreen', `[boolean] allow/disable clear screen when logging`)\n  .option('-d, --debug [feat]', `[string | boolean] show debug logs`)\n  .option('-f, --filter <filter>', `[string] filter debug logs`)\n  .option('-m, --mode <mode>', `[string] set env mode`)\n\n// dev\ncli\n  .command('[root]') // default command\n  .alias('serve')\n  .option('--host [host]', `[string] specify hostname`)\n  .option('--port <port>', `[number] specify port`)\n  .option('--https', `[boolean] use TLS + HTTP/2`)\n  .option('--open [path]', `[boolean | string] open browser on startup`)\n  .option('--cors', `[boolean] enable CORS`)\n  .option('--strictPort', `[boolean] exit if specified port is already in use`)\n  .option(\n    '--force',\n    `[boolean] force the optimizer to ignore the cache and re-bundle`\n  )\n  .action(async (root: string, options: ServerOptions & GlobalCLIOptions) => {\n    // output structure is preserved even after bundling so require()\n    // is ok here\n    const { createServer } = await import('./server')\n    try {\n      const server = await createServer({\n        root,\n        base: options.base,\n        mode: options.mode,\n        configFile: options.config,\n        logLevel: options.logLevel,\n        clearScreen: options.clearScreen,\n        server: cleanOptions(options)\n      })\n      await server.listen()\n    } catch (e) {\n      createLogger(options.logLevel).error(\n        chalk.red(`error when starting dev server:\\n${e.stack}`),\n        { error: e }\n      )\n      process.exit(1)\n    }\n  })\n\n// build\ncli\n  .command('build [root]')\n  .option('--target <target>', `[string] transpile target (default: 'modules')`)\n  .option('--outDir <dir>', `[string] output directory (default: dist)`)\n  .option(\n    '--assetsDir <dir>',\n    `[string] directory under outDir to place assets in (default: _assets)`\n  )\n  .option(\n    '--assetsInlineLimit <number>',\n    `[number] static asset base64 inline threshold in bytes (default: 4096)`\n  )\n  .option(\n    '--ssr [entry]',\n    `[string] build specified entry for server-side rendering`\n  )\n  .option(\n    '--sourcemap',\n    `[boolean] output source maps for build (default: false)`\n  )\n  .option(\n    '--minify [minifier]',\n    `[boolean | \"terser\" | \"esbuild\"] enable/disable minification, ` +\n      `or specify minifier to use (default: terser)`\n  )\n  .option('--manifest', `[boolean] emit build manifest json`)\n  .option('--ssrManifest', `[boolean] emit ssr manifest json`)\n  .option(\n    '--emptyOutDir',\n    `[boolean] force empty outDir when it's outside of root`\n  )\n  .option('-w, --watch', `[boolean] rebuilds when modules have changed on disk`)\n  .action(async (root: string, options: BuildOptions & GlobalCLIOptions) => {\n    const { build } = await import('./build')\n    const buildOptions: BuildOptions = cleanOptions(options)\n\n    try {\n      await build({\n        root,\n        base: options.base,\n        mode: options.mode,\n        configFile: options.config,\n        logLevel: options.logLevel,\n        clearScreen: options.clearScreen,\n        build: buildOptions\n      })\n    } catch (e) {\n      createLogger(options.logLevel).error(\n        chalk.red(`error during build:\\n${e.stack}`),\n        { error: e }\n      )\n      process.exit(1)\n    }\n  })\n\n// optimize\ncli\n  .command('optimize [root]')\n  .option(\n    '--force',\n    `[boolean] force the optimizer to ignore the cache and re-bundle`\n  )\n  .action(\n    async (root: string, options: { force?: boolean } & GlobalCLIOptions) => {\n      const { optimizeDeps } = await import('./optimizer')\n      try {\n        const config = await resolveConfig(\n          {\n            root,\n            base: options.base,\n            configFile: options.config,\n            logLevel: options.logLevel\n          },\n          'build',\n          'development'\n        )\n        await optimizeDeps(config, options.force, true)\n      } catch (e) {\n        createLogger(options.logLevel).error(\n          chalk.red(`error when optimizing deps:\\n${e.stack}`),\n          { error: e }\n        )\n        process.exit(1)\n      }\n    }\n  )\n\ncli\n  .command('preview [root]')\n  .option('--host [host]', `[string] specify hostname`)\n  .option('--port <port>', `[number] specify port`)\n  .option('--https', `[boolean] use TLS + HTTP/2`)\n  .option('--open [path]', `[boolean | string] open browser on startup`)\n  .option('--strictPort', `[boolean] exit if specified port is already in use`)\n  .action(\n    async (\n      root: string,\n      options: {\n        host?: string | boolean\n        port?: number\n        https?: boolean\n        open?: boolean | string\n        strictPort?: boolean\n      } & GlobalCLIOptions\n    ) => {\n      try {\n        const config = await resolveConfig(\n          {\n            root,\n            base: options.base,\n            configFile: options.config,\n            logLevel: options.logLevel,\n            server: {\n              open: options.open,\n              strictPort: options.strictPort,\n              https: options.https\n            }\n          },\n          'serve',\n          'production'\n        )\n        await preview(config, cleanOptions(options))\n      } catch (e) {\n        createLogger(options.logLevel).error(\n          chalk.red(`error when starting preview server:\\n${e.stack}`),\n          { error: e }\n        )\n        process.exit(1)\n      }\n    }\n  )\n\ncli.help()\ncli.version(require('../../package.json').version)\n\ncli.parse()\n"], "names": ["EventEmitter", "charsetModule", "specify", "isQuality", "compareSpecs", "encodingModule", "languageModule", "mediaTypeModule", "negotiatorModule", "Negotiator", "require$$0", "require$$1", "require$$2", "require$$3", "accepts", "bytesModule", "bytes", "compressible", "require$$4", "srcModule", "onHeaders", "require$$5", "require$$6", "require$$7", "compressionModule", "connect", "resolveHttpServer", "resolveHttpsConfig", "corsMiddleware", "proxyMiddleware", "compression", "path", "sirv", "resolveHostname", "httpServerStart", "chalk", "printServerUrls", "openBrowser", "createLogger", "build", "resolveConfig"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,KAAK,CAAC,GAAG,EAAE;AACpB,CAAC,OAAO,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5D,CAAC;AACD;AACA,SAAS,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;AACpC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG;AACzB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC;AAC/E,IAAI,OAAO,GAAG,KAAK,SAAS,GAAG,GAAG;AAClC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACpI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG;AACpC,EAAE,CAAC;AACH,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACpF,CAAC;AACD;AACA,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC3B,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACnB,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACnB;AACA,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;AAC1C,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC;AACA,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;AACrC,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC;AACxC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC;AAC1C;AACA,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC/B,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpC;AACA,CAAC,IAAI,KAAK,EAAE;AACZ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AACxB,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtD,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG;AACvC,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC1C,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,EAAE;AACF;AACA,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG;AACtC,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACzC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,EAAE;AACF;AACA,CAAC,IAAI,QAAQ,EAAE;AACf,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;AAC1B,GAAG,IAAI,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACjC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC7C,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACpD;AACA,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACzB,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAChB;AACA,EAAE,IAAI,GAAG,KAAK,IAAI,EAAE;AACpB,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzC,GAAG,MAAM;AACT,GAAG;AACH;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,MAAM;AACvC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,GAAG,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE;AAChD,GAAG,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI;AACJ,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AACrB,GAAG,MAAM;AACT,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AAC1C,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,MAAM;AAC1C,IAAI;AACJ;AACA,GAAG,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAChC,GAAG,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACnC;AACA,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AACxC,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAClF,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,CAAC;AAC1D,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,QAAQ,EAAE;AACf,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;AAC1B,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE;AAC1B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,KAAK,EAAE;AACZ,EAAE,KAAK,CAAC,IAAI,GAAG,EAAE;AACjB,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC7B,GAAG,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA,MAAM,cAAc,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAC7D,MAAM,eAAe,GAAG,CAAC,CAAC,KAAK;AAC/B,EAAE,MAAM,wBAAwB,GAAG,YAAY,CAAC;AAChD,EAAE,MAAM,wBAAwB,GAAG,eAAe,CAAC;AACnD,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;AACjB,EAAE,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK;AAC3B,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACjC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;AACxC,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,OAAO,WAAW,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACzD,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,OAAO,WAAW,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACzD,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,CAAC,OAAO,KAAK;AACnC,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;AAC1C,EAAE,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;AACnD,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE;AAC1B,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,MAAM,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC3D,UAAU,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC;AACvH,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,mBAAmB,EAAE;AAClC,UAAU,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK;AAC7B,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC5B,IAAI,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACxC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC;AACF,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK;AAClC,EAAE,OAAO,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC;AACF,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;AAC7B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK;AAC1D,IAAI,OAAO,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK;AACvC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;AACd,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,OAAO,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;AAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC5H,GAAG;AACH,CAAC,CAAC;AACF,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,UAAU,KAAK;AACvC,EAAE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAC7C,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,IAAI,IAAI,SAAS,CAAC,eAAe,EAAE;AACnC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,MAAM,IAAI,OAAO,SAAS,CAAC,iBAAiB,KAAK,UAAU,EAAE;AAC7D,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AAC7D,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AAC/B,EAAE,MAAM,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACvB,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG,CAAC,IAAI,KAAK;AACtC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACvC,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAM,QAAQ,SAAS,KAAK,CAAC;AAC7B,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACtC,IAAI,IAAI,OAAO,KAAK,CAAC,iBAAiB,KAAK,UAAU,EAAE;AACvD,MAAM,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACtD,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA,MAAM,MAAM,CAAC;AACb,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE;AAC5C,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAC5C,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AAC/D,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACjD,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAClC,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC5B,QAAQ,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACxC,OAAO;AACP,MAAM,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACvC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAClD,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;AACrD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC/B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,KAAK,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACtC,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC5B,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC5B,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;AACjC,MAAM,YAAY,GAAG,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AACnF;AACA,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,EAAE;AACtD,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,KAAK,CAAC,IAAI,EAAE;AACd,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,mBAAmB,GAAG;AACxB,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAC3C,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,wBAAwB,GAAG;AAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAC;AAChD,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE,WAAW,GAAG,eAAe,EAAE;AAClD,IAAI,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;AACjC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE;AACvC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,KAAK,CAAC,IAAI,EAAE;AACd,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,CAAC,QAAQ,EAAE;AACnB,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,IAAI,gBAAgB,GAAG;AACzB,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,YAAY,aAAa,CAAC;AACzC,GAAG;AACH,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACzC,MAAM,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AACtC,IAAI,MAAM;AACV,MAAM,aAAa;AACnB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,YAAY;AAClB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;AAC/B,IAAI,IAAI,QAAQ,GAAG;AACnB,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAClE,OAAO;AACP,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,IAAI,CAAC;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3D,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AAChG,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,MAAM,kBAAkB,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AACzF,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpB,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AACxC,UAAU,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AACrG,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpB,QAAQ,KAAK,EAAE,CAAC,uDAAuD,CAAC;AACxE,QAAQ,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACxH,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,GAAG,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,aAAa,IAAI,EAAE,CAAC,CAAC;AACrG,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,MAAM,MAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AACrF,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpB,QAAQ,KAAK,EAAE,SAAS;AACxB,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AACtC,UAAU,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnL,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpB,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AAC7C,UAAU,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AAC7C,YAAY,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACjC,WAAW;AACX,UAAU,OAAO,OAAO,CAAC;AACzB,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACpD,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AAC1C,MAAM,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC;AAC9C,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;AAChC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACrB,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AAC5B,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;AACnD,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;AAC9D,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,GAAG;AACtB,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;AAC5E,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,gBAAgB,EAAE;AACjD,MAAM,MAAM,IAAI,QAAQ,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAClF,KAAK;AACL,GAAG;AACH,EAAE,mBAAmB,GAAG;AACxB,IAAI,MAAM,CAAC,OAAO,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AAC9C,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE;AAC1C,MAAM,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAC/C,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AACtF,UAAU,MAAM,IAAI,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjG,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,MAAM,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AAC7D,IAAI,MAAM,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;AAChE,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAClC,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC3B,QAAQ,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3F,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,UAAU,EAAE;AAC9D,UAAU,MAAM,IAAI,QAAQ,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC9E,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,aAAa,SAAS,OAAO,CAAC;AACpC,EAAE,WAAW,CAAC,GAAG,EAAE;AACnB,IAAI,KAAK,CAAC,YAAY,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AACrC,GAAG;AACH,CAAC;AACD;AACA,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,MAAM,GAAG,SAASA,yBAAY,CAAC;AAC/B,EAAE,WAAW,CAAC,IAAI,GAAG,EAAE,EAAE;AACzB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,KAAK,CAAC,IAAI,EAAE;AACd,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE;AACxC,IAAI,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,EAAE,WAAW,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC1E,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE;AACvC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,QAAQ,CAAC;AAC/C,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE,WAAW,GAAG,eAAe,EAAE;AAClD,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACxC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACvC,GAAG;AACH,EAAE,aAAa,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACrE,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,mBAAmB,GAAG;AACxB,IAAI,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE;AAC5B,IAAI,GAAG,GAAG,IAAI;AACd,GAAG,GAAG,EAAE,EAAE;AACV,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACpB,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACzD,KAAK;AACL,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC;AAC3B,IAAI,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACzC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACtD,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;AAC1C,QAAQ,WAAW,GAAG,KAAK,CAAC;AAC5B,QAAQ,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;AAC1D,UAAU,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AAC7D,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACrD,OAAO;AACP,KAAK;AACL,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC3C,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;AACjC,UAAU,WAAW,GAAG,KAAK,CAAC;AAC9B,UAAU,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9C,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AAC1C,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAClD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,MAAM,GAAG,GAAG,KAAK,CAAC;AAClB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxD,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,MAAM,GAAG,GAAG,KAAK,CAAC;AAClB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAChE,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AAC9C,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE;AACrB,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO;AACnC,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,EAAE;AACvC,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;AACjD,IAAI,IAAI,qBAAqB,GAAG,EAAE,CAAC;AACnC,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjD,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE;AAChC,MAAM,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAChE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACxC,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AACvD,MAAM,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE;AACzC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;AACjD,OAAO,CAAC,CAAC;AACT,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChB,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;AAC1B,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,IAAI,EAAE,qBAAqB;AACjC,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,wBAAwB,GAAG,OAAO,CAAC,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,wBAAwB,CAAC;AAC5K,IAAI,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;AACjE,QAAQ,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AAC5C,UAAU,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC;AACnD,SAAS;AACT,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAChD,QAAQ,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AACnD,UAAU,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,UAAU,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;AAC/D,UAAU,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrF,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC3C,MAAM,IAAI,GAAG,KAAK,GAAG,EAAE;AACvB,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,QAAQ,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C,QAAQ,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,OAAO;AACb,KAAK,CAAC;AACN,GAAG;AACH,EAAE,iBAAiB,GAAG;AACtB,IAAI,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;AAC1D,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;AAC1C,MAAM,OAAO;AACb,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;AAClC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;AAC/B,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;AAChC,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;AAC1B,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AACzC,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE;AACxB,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3C,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACrC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,IAAI,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACzD,GAAG;AACH,CAAC;AACD;AACA,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;ACzlBxC;AACA;AACA;AACA;AACA;AACA;AACAC,eAAc,GAAG,iBAAiB,CAAC;iCACH,GAAG,kBAAkB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,GAAG,6BAA6B,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACpC,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;AAC7B,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACrB;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE;AAC9B,EAAE,IAAI,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5C,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAC1B;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACxB,QAAQ,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AACtD,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,IAAI,GAAGC,SAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACpD;AACA,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;AACzF,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,CAAC;AAC1D,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,GAAG;AACpC,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,KAAK;AACZ,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,CAAC;AACR,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC7C;AACA,EAAE,IAAI,OAAO,GAAG,kBAAkB,CAAC,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,IAAI,EAAE,CAAC,CAAC;AAC9E;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB;AACA,IAAI,OAAO,OAAO;AAClB,OAAO,MAAM,CAACC,WAAS,CAAC;AACxB,OAAO,IAAI,CAACC,cAAY,CAAC;AACzB,OAAO,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAClE,IAAI,OAAO,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACpD,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,OAAO,UAAU,CAAC,MAAM,CAACD,WAAS,CAAC,CAAC,IAAI,CAACC,cAAY,CAAC,CAAC,GAAG,CAAC,SAAS,UAAU,CAAC,QAAQ,EAAE;AAC3F,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,IAAI,EAAE;AAC9B,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC;AACtB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,WAAS,CAAC,IAAI,EAAE;AACzB,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB;;;;;;;;;;;AC/JA;AACA;AACA;AACA;AACA;AACA;AACAE,gBAAc,GAAG,kBAAkB,CAAC;mCACH,GAAG,mBAAmB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,GAAG,6BAA6B,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACrC,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC;AAC1B,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC;AACrB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,IAAI,IAAI,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;AAC9B,MAAM,WAAW,GAAG,WAAW,IAAIH,SAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACjE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACzD,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG;AACnB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,CAAC,EAAE,UAAU;AACnB,MAAM,CAAC,EAAE,CAAC;AACV,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACrB;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE;AAC/B,EAAE,IAAI,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAC1B;AACA,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACxB,QAAQ,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;AACxD,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,IAAI,GAAGA,SAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;AACzF,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE;AACxC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC5D,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,GAAG;AACrC,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,KAAK;AACZ,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,CAAC;AACR,GAAG;AACH,CACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC9C,EAAE,IAAI,OAAO,GAAG,mBAAmB,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;AAClD;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB;AACA,IAAI,OAAO,OAAO;AAClB,OAAO,MAAM,CAACC,WAAS,CAAC;AACxB,OAAO,IAAI,CAACC,cAAY,CAAC;AACzB,OAAO,GAAG,CAAC,eAAe,CAAC,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAClE,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACrD,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,OAAO,UAAU,CAAC,MAAM,CAACD,WAAS,CAAC,CAAC,IAAI,CAACC,cAAY,CAAC,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,QAAQ,EAAE;AAC5F,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,WAAS,CAAC,IAAI,EAAE;AACzB,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB;;;;;;;;;;;AC9KA;AACA;AACA;AACA;AACA;AACA;AACAG,gBAAc,GAAG,kBAAkB,CAAC;mCACH,GAAG,mBAAmB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,GAAG,8CAA8C,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACrC,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,IAAI,IAAI,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;AAC9B,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACrB;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE;AAC/B,EAAE,IAAI,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAC1B;AACA,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;AACvB,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;AACrB,IAAI,IAAI,GAAG,MAAM,CAAC;AAClB;AACA,EAAE,IAAI,MAAM,EAAE,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC;AACnC;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACnC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;AACxD,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,IAAI,GAAGJ,SAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;AACzF,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE;AACxC,EAAE,IAAI,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAC;AACjC,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;AACtB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AACtD,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACjE,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE;AACjE,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,GAAG;AACjC,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,KAAK;AACZ,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,CAAC;AACR,GAAG;AACH,CACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC9C;AACA,EAAE,IAAI,OAAO,GAAG,mBAAmB,CAAC,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,IAAI,EAAE,CAAC,CAAC;AAC/E;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB;AACA,IAAI,OAAO,OAAO;AAClB,OAAO,MAAM,CAACC,WAAS,CAAC;AACxB,OAAO,IAAI,CAACC,cAAY,CAAC;AACzB,OAAO,GAAG,CAAC,eAAe,CAAC,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAClE,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACrD,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,OAAO,UAAU,CAAC,MAAM,CAACD,WAAS,CAAC,CAAC,IAAI,CAACC,cAAY,CAAC,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,QAAQ,EAAE;AAC5F,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,WAAS,CAAC,IAAI,EAAE;AACzB,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB;;;;;;;;;;;ACzKA;AACA;AACA;AACA;AACA;AACA;AACAI,iBAAc,GAAG,mBAAmB,CAAC;qCACH,GAAG,oBAAoB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qBAAqB,GAAG,0CAA0C,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,EAAE,IAAI,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACxC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,IAAI,IAAI,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;AAC/B,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACrB;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE;AAChC,EAAE,IAAI,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAC1B;AACA,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,IAAI,IAAI,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAChE;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AACtC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB;AACA;AACA,MAAM,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;AACtE,UAAU,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,UAAU,GAAG,CAAC;AACd;AACA,MAAM,IAAI,GAAG,KAAK,GAAG,EAAE;AACvB,QAAQ,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAC9B,QAAQ,MAAM;AACd,OAAO;AACP;AACA;AACA,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC1B,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;AACrD,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACjD;AACA,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;AACzF,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;AACpC,EAAE,IAAI,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ;AACA,EAAE,IAAI,CAAC,CAAC,EAAE;AACV,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACtD,IAAI,CAAC,IAAI,EAAC;AACV,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE;AAC5D,IAAI,CAAC,IAAI,EAAC;AACV,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,IAAI,GAAG,EAAE;AACjC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtC,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AAChH,KAAK,CAAC,EAAE;AACR,MAAM,CAAC,IAAI,EAAC;AACZ,KAAK,MAAM;AACX,MAAM,OAAO,IAAI;AACjB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,KAAK;AACZ,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,IAAI,CAAC,EAAE,CAAC;AACR,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC/C;AACA,EAAE,IAAI,OAAO,GAAG,WAAW,CAAC,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,MAAM,IAAI,EAAE,CAAC,CAAC;AACzE;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB;AACA,IAAI,OAAO,OAAO;AAClB,OAAO,MAAM,CAAC,SAAS,CAAC;AACxB,OAAO,IAAI,CAAC,YAAY,CAAC;AACzB,OAAO,GAAG,CAAC,WAAW,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAClE,IAAI,OAAO,oBAAoB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACtD,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,OAAO,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE;AACxF,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,IAAI,EAAE;AAC3B,EAAE,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,IAAI,EAAE;AACzB,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB;AACA,EAAE,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE;AACtD,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,GAAG,EAAE;AAChC,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,GAAG,CAAC;AACV;AACA,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AACpB,IAAI,GAAG,GAAG,GAAG,CAAC;AACd,GAAG,MAAM;AACT,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAChC,GAAG;AACH;AACA,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,MAAM,EAAE;AACjC,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACzC,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACrC,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,IAAI,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,IAAI,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC5C,MAAM,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3C,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACzC,GAAG;AACH;AACA,EAAE,OAAO,UAAU,CAAC;AACpB;;;;;;;;;AC5RA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACAC,kBAAc,GAAGC,YAAU,CAAC;6BACH,GAAGA,aAAW;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAU,CAAC,OAAO,EAAE;AAC7B,EAAE,IAAI,EAAE,IAAI,YAAYA,YAAU,CAAC,EAAE;AACrC,IAAI,OAAO,IAAIA,YAAU,CAAC,OAAO,CAAC,CAAC;AACnC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACzB,CAAC;AACD;AACAA,YAAU,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,CAAC,SAAS,EAAE;AAC3D,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACrC,EAAE,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,SAAS,EAAE;AAC7D,EAAE,IAAI,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC;AAClE,EAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,SAAS,CAAC,CAAC;AAC9E,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,SAAS,EAAE;AAC7D,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACtC,EAAE,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,CAAC,SAAS,EAAE;AAC/D,EAAE,IAAI,kBAAkB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC;AACrE,EAAE,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,SAAS,CAAC,CAAC;AAChF,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,SAAS,EAAE;AAC7D,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACtC,EAAE,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,CAAC,SAAS,EAAE;AAC/D,EAAE,IAAI,kBAAkB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC;AACrE,EAAE,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,SAAS,CAAC,CAAC;AAChF,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,CAAC,SAAS,EAAE;AAC/D,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACvC,EAAE,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AACF;AACAA,YAAU,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,SAAS,EAAE;AACjE,EAAE,IAAI,mBAAmB,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC;AACxE,EAAE,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrE,CAAC,CAAC;AACF;AACA;AACAA,YAAU,CAAC,SAAS,CAAC,gBAAgB,GAAGA,YAAU,CAAC,SAAS,CAAC,OAAO,CAAC;AACrEA,YAAU,CAAC,SAAS,CAAC,iBAAiB,GAAGA,YAAU,CAAC,SAAS,CAAC,QAAQ,CAAC;AACvEA,YAAU,CAAC,SAAS,CAAC,iBAAiB,GAAGA,YAAU,CAAC,SAAS,CAAC,QAAQ,CAAC;AACvEA,YAAU,CAAC,SAAS,CAAC,kBAAkB,GAAGA,YAAU,CAAC,SAAS,CAAC,SAAS,CAAC;AACzEA,YAAU,CAAC,SAAS,CAAC,iBAAiB,GAAGA,YAAU,CAAC,SAAS,CAAC,QAAQ,CAAC;AACvEA,YAAU,CAAC,SAAS,CAAC,kBAAkB,GAAGA,YAAU,CAAC,SAAS,CAAC,SAAS,CAAC;AACzEA,YAAU,CAAC,SAAS,CAAC,kBAAkB,GAAGA,YAAU,CAAC,SAAS,CAAC,SAAS,CAAC;AACzEA,YAAU,CAAC,SAAS,CAAC,mBAAmB,GAAGA,YAAU,CAAC,SAAS,CAAC,UAAU,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,UAAU,EAAE;AAChC,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AACnC;AACA,EAAE,IAAI,MAAM,KAAK,SAAS,EAAE;AAC5B,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA,EAAE,QAAQ,UAAU;AACpB,IAAI,KAAK,SAAS;AAClB,MAAM,MAAM,GAAGC,eAAwB,CAAC;AACxC,MAAM,MAAM;AACZ,IAAI,KAAK,UAAU;AACnB,MAAM,MAAM,GAAGC,gBAAyB,CAAC;AACzC,MAAM,MAAM;AACZ,IAAI,KAAK,UAAU;AACnB,MAAM,MAAM,GAAGC,gBAAyB,CAAC;AACzC,MAAM,MAAM;AACZ,IAAI,KAAK,WAAW;AACpB,MAAM,MAAM,GAAGC,iBAA0B,CAAC;AAC1C,MAAM,MAAM;AACZ,IAAI;AACJ,MAAM,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,UAAU,GAAG,IAAI,CAAC,CAAC;AACnE,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;AAC/B;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrHA;AACA;AACA;AACA;IACA,MAAc,GAAG;;;;;;;;;;ACFjB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,EAAE,GAAGH,OAAkB;AAC3B,IAAI,OAAO,GAAGC,aAAe,CAAC,QAAO;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,GAAG,0BAAyB;AACnD,IAAI,gBAAgB,GAAG,WAAU;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,QAAO;AACzB,mBAAmB,EAAE,MAAM,EAAE,OAAO,GAAE;AACtC,sBAAsB,YAAW;AACjC,oBAAoB,UAAS;AAC7B,qBAAqB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AACxC,iBAAiB,OAAM;AACvB,gBAAgB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AACnC;AACA;AACA,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,EAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,EAAE,IAAI,EAAE;AACxB,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACzC,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAC;AAC5C,EAAE,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAC;AAChD;AACA,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AAChD,IAAI,OAAO,OAAO;AAClB,GAAG;AACH;AACA,EAAE,OAAO,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,EAAE,GAAG,EAAE;AAC3B;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACvC,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,MAAM,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;AACzB,MAAM,IAAG;AACT;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;AACtC,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAC;AACvC,IAAI,IAAI,OAAO,EAAE,IAAI,IAAI,YAAY,GAAG,OAAO,CAAC,WAAW,GAAE;AAC7D,GAAG;AACH;AACA,EAAE,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,EAAE,IAAI,EAAE;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACzC,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAC;AAC5C;AACA;AACA,EAAE,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAC;AAChE;AACA,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC7B,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,EAAE,IAAI,EAAE;AACvB,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACzC,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA;AACA,EAAE,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AACtC,KAAK,WAAW,EAAE;AAClB,KAAK,MAAM,CAAC,CAAC,EAAC;AACd;AACA,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK;AAC1C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE;AAC1C;AACA,EAAE,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAC;AACzD;AACA,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE;AAC1D,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAC;AACvB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,WAAU;AAC9B;AACA,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC/B,MAAM,MAAM;AACZ,KAAK;AACL;AACA;AACA,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,KAAI;AAC3B;AACA;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC,EAAC;AAC7B;AACA,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE;AAC5B,QAAQ,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAC;AAClE,QAAQ,IAAI,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAC;AAChD;AACA,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,0BAA0B;AAC3D,WAAW,IAAI,GAAG,EAAE,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,cAAc,CAAC,CAAC,EAAE;AAC7F;AACA,UAAU,QAAQ;AAClB,SAAS;AACT,OAAO;AACP;AACA;AACA,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG,KAAI;AAC7B,KAAK;AACL,GAAG,EAAC;AACJ;;;;;;;;;ACnLA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,UAAU,GAAGD,mBAAqB;AACtC,IAAI,IAAI,GAAGC,UAAqB;AAChC;AACA;AACA;AACA;AACA;AACA;IACAG,SAAc,GAAG,QAAO;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,EAAE,GAAG,EAAE;AACvB,EAAE,IAAI,EAAE,IAAI,YAAY,OAAO,CAAC,EAAE;AAClC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,QAAO;AAC5B,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,GAAG,EAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,SAAS,CAAC,IAAI;AACtB,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,MAAM,EAAE;AAC5C,EAAE,IAAI,KAAK,GAAG,OAAM;AACpB;AACA;AACA,EAAE,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACtC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAC;AACvC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAC;AAC7B,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;AACvC,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AAC5B,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC;AACnB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,EAAC;AAClC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,EAAC;AACnE,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,EAAC;AACxB;AACA,EAAE,OAAO,KAAK;AACd,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,MAAM,KAAK;AACX,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,SAAS,CAAC,QAAQ;AAC1B,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,UAAU,EAAE;AACpD,EAAE,IAAI,SAAS,GAAG,WAAU;AAC5B;AACA;AACA,EAAE,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC9C,IAAI,SAAS,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAC;AAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAC;AACjC,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5C,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AACtC,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK;AACzD,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,SAAS,CAAC,OAAO;AACzB,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,SAAS,EAAE;AAClD,EAAE,IAAI,QAAQ,GAAG,UAAS;AAC1B;AACA;AACA,EAAE,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC5C,IAAI,QAAQ,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAC;AAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAC;AAChC,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1C,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AACrC,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK;AACvD,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,SAAS,CAAC,IAAI;AACtB,OAAO,CAAC,SAAS,CAAC,KAAK;AACvB,OAAO,CAAC,SAAS,CAAC,QAAQ;AAC1B,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,UAAU,EAAE;AACpD,EAAE,IAAI,SAAS,GAAG,WAAU;AAC5B;AACA;AACA,EAAE,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC9C,IAAI,SAAS,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAC;AAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAC;AACjC,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5C,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AACtC,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK;AACzD,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,EAAE,IAAI,EAAE;AAC1B,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACvB,MAAM,IAAI;AACV,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,EAAE,IAAI,EAAE;AAC1B,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ;AACjC;;;;;;;;;;ACrOA;AACA;AACA;AACA;AACA;AACA;AACAC,eAAc,GAAGC,OAAK,CAAC;sBACF,GAAG,OAAO;qBACX,GAAG,MAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qBAAqB,GAAG,uBAAuB,CAAC;AACpD;AACA,IAAI,oBAAoB,GAAG,uBAAuB,CAAC;AACnD;AACA,IAAI,GAAG,GAAG;AACV,EAAE,CAAC,GAAG,CAAC;AACP,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE;AACb,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE;AACb,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE;AACb,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;AACxB,CAAC,CAAC;AACF;AACA,IAAI,WAAW,GAAG,4CAA4C,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAK,CAAC,KAAK,EAAE,OAAO,EAAE;AAC/B,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAClC,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE;AAChC,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,kBAAkB,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,kBAAkB,KAAK,EAAE,CAAC;AACzE,EAAE,IAAI,aAAa,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,KAAK,EAAE,CAAC;AAC/D,EAAE,IAAI,aAAa,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;AACnG,EAAE,IAAI,aAAa,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC;AAChE,EAAE,IAAI,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,CAAC;AAC7C;AACA,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;AACzC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE;AACvB,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAC5C,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACvC;AACA,EAAE,IAAI,CAAC,aAAa,EAAE;AACtB,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AAClD,GAAG;AACH;AACA,EAAE,IAAI,kBAAkB,EAAE;AAC1B,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;AACjE,GAAG;AACH;AACA,EAAE,OAAO,GAAG,GAAG,aAAa,GAAG,IAAI,CAAC;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,GAAG,EAAE;AACpB,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC9C,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtC,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC;AACjB;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB;AACA,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AACnC,IAAI,IAAI,GAAG,IAAG;AACd,GAAG,MAAM;AACT;AACA,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AACpC,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;AAC5C;;;;;;;;;ACrJA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,EAAE,GAAGN,OAAkB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,wBAAwB,GAAG,gCAA+B;AAC9D,IAAI,mBAAmB,GAAG,0BAAyB;AACnD;AACA;AACA;AACA;AACA;AACA;IACA,cAAc,GAAGO,eAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAY,EAAE,IAAI,EAAE;AAC7B,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACzC,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAC;AAC5C,EAAE,IAAI,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAE;AAC5C,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAC;AACrB;AACA;AACA,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;AAC/C,IAAI,OAAO,IAAI,CAAC,YAAY;AAC5B,GAAG;AACH;AACA;AACA,EAAE,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;AACzD;;;;;;;;;ACxDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAAG,iBAAiB,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;AACpF,iBAAiB,MAAM,CAAC;AACxB,kBAAkB,OAAO,CAAC;AAC1B,iBAAiB,MAAM,CAAC;AACxB,kBAAkB,OAAO,CAAC;AAC1B,mBAAmBP,QAAa,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA,gBAAgB,EAAE,CAAC;AACnB,gBAAgB,EAAE,CAAC;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,EAAE,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA,IAAI,QAAQ,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;AAClB;AACA,EAAE,KAAK,CAAC,IAAI,SAAS,EAAE;AACvB,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAI,IAAI,IAAI,CAAC,CAAC;AACd,GAAG;AACH;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAChE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC;AACA,EAAE,SAAS,KAAK,GAAG;AACnB;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO;AAC/B;AACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;AACrB;AACA;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;AAC3B,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB;AACA;AACA,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,KAAK;AACL;AACA,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC;AACA,IAAI,IAAI,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE;AACrC;AACA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL;AACA;AACA,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,SAAS,KAAK,EAAE,MAAM,EAAE;AACvE;AACA,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK,CAAC;AACvC,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,IAAI,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACjD,MAAM,IAAI,UAAU,KAAK,OAAO,SAAS,EAAE;AAC3C,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,QAAQ,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC1C;AACA;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC9B,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO;AACP,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC,CAAC;AACP;AACA;AACA,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC;AACA,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtE,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC9B,EAAE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7C,EAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;AACxC,EAAE,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;AACvC;AACA;AACA,EAAE,IAAI,UAAU,KAAK,OAAO,OAAO,CAAC,IAAI,EAAE;AAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,UAAU,EAAE;AAC5B,EAAE,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3B;AACA,EAAE,gBAAgB,EAAE,CAAC;AACrB,EAAE,gBAAgB,EAAE,CAAC;AACrB;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACjF,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AACzB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS;AAC5B,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAChD,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC/B,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACvE,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,IAAI,EAAE;AACvB,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;AACb,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACxD,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACrC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACxD,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,GAAG,EAAE;AACrB,EAAE,IAAI,GAAG,YAAY,KAAK,EAAE,OAAO,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC;AAC5D,EAAE,OAAO,GAAG,CAAC;AACb;;;;;;;;;;ACnMA,OAAO,GAAG,iBAAiBA,eAAkB,CAAC;AAC9C,cAAc,GAAG,CAAC;AAClB,qBAAqB,UAAU,CAAC;AAChC,eAAe,IAAI,CAAC;AACpB,eAAe,IAAI,CAAC;AACpB,oBAAoB,SAAS,CAAC;AAC9B,kBAAkB,WAAW,IAAI,OAAO,MAAM;AAC9C,kBAAkB,WAAW,IAAI,OAAO,MAAM,CAAC,OAAO;AACtD,oBAAoB,MAAM,CAAC,OAAO,CAAC,KAAK;AACxC,oBAAoB,YAAY,EAAE,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,EAAE,eAAe;AACjB,EAAE,aAAa;AACf,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,SAAS;AACX,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,GAAG;AACrB;AACA;AACA;AACA,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;AAC7F,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,CAAC,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,gBAAgB;AAC1J;AACA,KAAK,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACvI;AACA;AACA,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;AAC3J;AACA,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;AAC/H,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE;AACnC,EAAE,IAAI;AACN,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,8BAA8B,GAAG,GAAG,CAAC,OAAO,CAAC;AACxD,GAAG;AACH,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC;AACA,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE;AAClC,MAAM,IAAI,CAAC,SAAS;AACpB,OAAO,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC;AAC/B,MAAM,IAAI,CAAC,CAAC,CAAC;AACb,OAAO,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC;AAC/B,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO;AACzB;AACA,EAAE,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AACjC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,gBAAgB,EAAC;AACxC;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,KAAK,EAAE;AACjD,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,OAAO;AAC/B,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB;AACA;AACA,MAAM,KAAK,GAAG,KAAK,CAAC;AACpB,KAAK;AACL,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG,GAAG;AACf;AACA;AACA,EAAE,OAAO,QAAQ,KAAK,OAAO,OAAO;AACpC,OAAO,OAAO,CAAC,GAAG;AAClB,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,UAAU,EAAE;AAC1B,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,IAAI,UAAU,EAAE;AAC5B,MAAM,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;AACzC,KAAK;AACL,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,GAAG;AAChB,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI;AACN,IAAI,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;AACf;AACA;AACA,EAAE,IAAI,CAAC,CAAC,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,KAAK,IAAI,OAAO,EAAE;AAChE,IAAI,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,GAAG;AACxB,EAAE,IAAI;AACN,IAAI,OAAO,MAAM,CAAC,YAAY,CAAC;AAC/B,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE;AAChB;;;;;;;;;;ACpLA,IAAI,GAAG,GAAGA,mBAAc,CAAC;AACzB,IAAI,IAAI,GAAGC,qBAAe,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAAG,iBAAiBC,eAAkB,CAAC;AAC9C,eAAe,IAAI,CAAC;AACpB,cAAc,GAAG,CAAC;AAClB,qBAAqB,UAAU,CAAC;AAChC,eAAe,IAAI,CAAC;AACpB,eAAe,IAAI,CAAC;AACpB,oBAAoB,SAAS,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE;AACrE,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE;AAC9B;AACA,EAAE,IAAI,IAAI,GAAG,GAAG;AAChB,KAAK,SAAS,CAAC,CAAC,CAAC;AACjB,KAAK,WAAW,EAAE;AAClB,KAAK,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACtE;AACA;AACA,EAAE,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC;AACvD,OAAO,IAAI,4BAA4B,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC;AAC/D,OAAO,IAAI,GAAG,KAAK,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC;AACtC,OAAO,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB;AACA,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AAClB,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACjD;AACA,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;AAC1B,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,yKAAyK,CAAC,GAAE;AAC3M,CAAC;AACD;AACA,IAAI,MAAM,GAAG,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,MAAM;AACtC,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,MAAM;AACtC,aAAa,yBAAyB,CAAC,EAAE,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,GAAG;AACrB,EAAE,OAAO,QAAQ,IAAI,OAAO,CAAC,WAAW;AACxC,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC;AACzC,MAAM,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE;AACnC,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;AAC3C,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;AAC1C,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE;AACnC,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE;AACvB,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE;AACnC,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;AAC3C,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC3C,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;AAC5B,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC;AACA,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,IAAI,IAAI,MAAM,GAAG,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC;AACrE;AACA,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;AACjF,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACtC,QAAQ,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACnC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG,GAAG;AACf,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,UAAU,EAAE;AAC1B,EAAE,IAAI,IAAI,IAAI,UAAU,EAAE;AAC1B;AACA;AACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7B,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,UAAU,CAAC;AACnC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,GAAG;AAChB,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,EAAE,EAAE,EAAE;AACxC,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7C;AACA;AACA;AACA,EAAE,QAAQ,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;AACtC,IAAI,KAAK,KAAK;AACd,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACvC,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B;AACA;AACA;AACA,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AAClD,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM;AACZ;AACA,IAAI,KAAK,MAAM;AACf,MAAM,IAAI,EAAE,GAAGC,WAAa,CAAC;AAC7B,MAAM,MAAM,GAAG,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;AAChE,MAAM,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,MAAM,MAAM;AACZ;AACA,IAAI,KAAK,MAAM,CAAC;AAChB,IAAI,KAAK,KAAK;AACd,MAAM,IAAI,GAAG,GAAGK,mBAAc,CAAC;AAC/B,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;AAC9B,QAAQ,EAAE,EAAE,EAAE;AACd,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,QAAQ,EAAE,IAAI;AACtB,OAAO,CAAC,CAAC;AACT;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;AAC5B;AACA;AACA;AACA,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AAClD,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM;AACZ;AACA,IAAI;AACJ;AACA,MAAM,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AACjE,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB;AACA,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,EAAE,KAAK,EAAE;AACtB,EAAE,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC9C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;;;;;;;;AClPtB,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;AACnE,EAAEC,WAAc,GAAGT,eAAuB,CAAC;AAC3C,CAAC,MAAM;AACP,EAAES,WAAc,GAAGR,YAAoB,CAAC;AACxC;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;IACA,WAAc,GAAGS,YAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE;AACnD,EAAE,IAAI,KAAK,GAAG,MAAK;AACnB;AACA;AACA,EAAE,OAAO,SAAS,SAAS,EAAE,UAAU,EAAE;AACzC;AACA,IAAI,IAAI,IAAI,GAAG,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,EAAC;AACzD;AACA;AACA,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,KAAK,GAAG,KAAI;AAClB,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAC;AACzB;AACA;AACA,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;AACtE,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAU;AACjC,QAAQ,IAAI,CAAC,MAAM,GAAG,EAAC;AACvB,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;AAC1C,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAS,EAAE,GAAG,EAAE,QAAQ,EAAE;AACnC,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC;AACnD,GAAG;AACH;AACA,EAAE,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACtC,IAAI,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC;AAC/D,GAAG;AACH;AACA,EAAE,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAC;AAC1D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,EAAE,GAAG,EAAE,OAAO,EAAE;AAC5C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AAC/C,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,EAAE,GAAG,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAC;AACjC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAC;AACnB,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAC;AACvC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,EAAE,UAAU,EAAE;AAC1C,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,OAAM;AAC/B,EAAE,IAAI,WAAW,GAAG,MAAM,GAAG,CAAC,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ;AAClE,MAAM,CAAC;AACP,MAAM,EAAC;AACP;AACA,EAAE,IAAI,OAAO,GAAG,MAAM,IAAI,WAAW,GAAG,CAAC;AACzC,MAAM,SAAS,CAAC,WAAW,CAAC;AAC5B,MAAM,UAAS;AACf;AACA,EAAE,IAAI,CAAC,UAAU,GAAG,WAAU;AAC9B;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC9B;AACA,IAAI,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAC;AACtC,GAAG,MAAM,IAAI,OAAO,EAAE;AACtB;AACA,IAAI,oBAAoB,CAAC,IAAI,EAAE,OAAO,EAAC;AACvC,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,EAAC;AACrD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAC;AAC1B,GAAG;AACH;AACA,EAAE,OAAO,IAAI;AACb;;;;;;;;;;ACzHA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAGV,UAAkB;AAChC,IAAI,MAAM,GAAGC,wBAAsB,CAAC,OAAM;AAC1C,IAAI,KAAK,GAAGC,gBAAgB;AAC5B,IAAI,YAAY,GAAGC,eAAuB;AAC1C,IAAI,KAAK,GAAGK,WAAgB,CAAC,aAAa,EAAC;AAC3C,IAAI,SAAS,GAAGG,YAAqB;AACrC,IAAI,IAAI,GAAGC,mBAAe;AAC1B,IAAI,IAAI,GAAGC,sBAAe;AAC1B;AACA;AACA;AACA;AACA;AACAC,qBAAc,GAAG,YAAW;4BACP,GAAG,eAAc;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,6BAA6B,GAAG,qCAAoC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,EAAE,OAAO,EAAE;AAC/B,EAAE,IAAI,IAAI,GAAG,OAAO,IAAI,GAAE;AAC1B;AACA;AACA,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,eAAc;AAC5C,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAC;AAC7C;AACA,EAAE,IAAI,SAAS,IAAI,IAAI,EAAE;AACzB,IAAI,SAAS,GAAG,KAAI;AACpB,GAAG;AACH;AACA,EAAE,OAAO,SAAS,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;AAC/C,IAAI,IAAI,KAAK,GAAG,MAAK;AACrB,IAAI,IAAI,OAAM;AACd,IAAI,IAAI,SAAS,GAAG,GAAE;AACtB,IAAI,IAAI,OAAM;AACd;AACA,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,IAAG;AACtB,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAE;AACpB,IAAI,IAAI,MAAM,GAAG,GAAG,CAAC,MAAK;AAC1B;AACA;AACA,IAAI,GAAG,CAAC,KAAK,GAAG,SAAS,KAAK,IAAI;AAClC,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,MAAM,CAAC,KAAK,GAAE;AACtB,OAAO;AACP,MAAK;AACL;AACA;AACA;AACA,IAAI,GAAG,CAAC,KAAK,GAAG,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE;AACjD,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,OAAO,KAAK;AACpB,OAAO;AACP;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,CAAC,eAAe,GAAE;AAC9B,OAAO;AACP;AACA,MAAM,OAAO,MAAM;AACnB,UAAU,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjD,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;AAC5C,MAAK;AACL;AACA,IAAI,GAAG,CAAC,GAAG,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC7C,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,OAAO,KAAK;AACpB,OAAO;AACP;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACzB;AACA,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE;AAC/C,UAAU,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAC;AAC/C,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,eAAe,GAAE;AAC9B,OAAO;AACP;AACA,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;AAC/C,OAAO;AACP;AACA;AACA,MAAM,KAAK,GAAG,KAAI;AAClB;AACA;AACA,MAAM,OAAO,KAAK;AAClB,UAAU,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC/C,UAAU,MAAM,CAAC,GAAG,EAAE;AACtB,MAAK;AACL;AACA,IAAI,GAAG,CAAC,EAAE,GAAG,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC1C,MAAM,IAAI,CAAC,SAAS,IAAI,IAAI,KAAK,OAAO,EAAE;AAC1C,QAAQ,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;AAC7C,OAAO;AACP;AACA,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;AACxC,OAAO;AACP;AACA;AACA,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAC;AACtC;AACA,MAAM,OAAO,IAAI;AACjB,MAAK;AACL;AACA,IAAI,SAAS,UAAU,EAAE,GAAG,EAAE;AAC9B,MAAM,KAAK,CAAC,oBAAoB,EAAE,GAAG,EAAC;AACtC,MAAM,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,EAAC;AACvC,MAAM,SAAS,GAAG,KAAI;AACtB,KAAK;AACL;AACA,IAAI,SAAS,CAAC,GAAG,EAAE,SAAS,iBAAiB,IAAI;AACjD;AACA,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AAC7B,QAAQ,UAAU,CAAC,UAAU,EAAC;AAC9B,QAAQ,MAAM;AACd,OAAO;AACP;AACA;AACA,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AACtC,QAAQ,UAAU,CAAC,cAAc,EAAC;AAClC,QAAQ,MAAM;AACd,OAAO;AACP;AACA;AACA,MAAM,IAAI,CAAC,GAAG,EAAE,iBAAiB,EAAC;AAClC;AACA;AACA,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG,SAAS,IAAI,MAAM,GAAG,SAAS,EAAE;AACrF,QAAQ,UAAU,CAAC,sBAAsB,EAAC;AAC1C,QAAQ,MAAM;AACd,OAAO;AACP;AACA,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,WAAU;AACpE;AACA;AACA,MAAM,IAAI,QAAQ,KAAK,UAAU,EAAE;AACnC,QAAQ,UAAU,CAAC,iBAAiB,EAAC;AACrC,QAAQ,MAAM;AACd,OAAO;AACP;AACA;AACA,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE;AACjC,QAAQ,UAAU,CAAC,cAAc,EAAC;AAClC,QAAQ,MAAM;AACd,OAAO;AACP;AACA;AACA,MAAM,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,EAAC;AAC/B,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,EAAC;AACnE;AACA;AACA,MAAM,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;AAC7D,QAAQ,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,EAAC;AACtD,OAAO;AACP;AACA;AACA,MAAM,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,UAAU,EAAE;AAC5C,QAAQ,UAAU,CAAC,gBAAgB,EAAC;AACpC,QAAQ,MAAM;AACd,OAAO;AACP;AACA;AACA,MAAM,KAAK,CAAC,gBAAgB,EAAE,MAAM,EAAC;AACrC,MAAM,MAAM,GAAG,MAAM,KAAK,MAAM;AAChC,UAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AAC/B,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,EAAC;AAClC;AACA;AACA,MAAM,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,SAAS,EAAC;AAChD;AACA;AACA,MAAM,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,MAAM,EAAC;AAC/C,MAAM,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAC;AACxC;AACA;AACA,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,YAAY,EAAE,KAAK,EAAE;AACtD,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,KAAK,EAAE;AAC/C,UAAU,MAAM,CAAC,KAAK,GAAE;AACxB,SAAS;AACT,OAAO,EAAC;AACR;AACA,MAAM,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,WAAW,IAAI;AAC/C,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC;AACtB,OAAO,EAAC;AACR;AACA,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,eAAe,IAAI;AACzD,QAAQ,MAAM,CAAC,MAAM,GAAE;AACvB,OAAO,EAAC;AACR,KAAK,EAAC;AACN;AACA,IAAI,IAAI,GAAE;AACV,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE;AAC9C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,EAAC;AAClC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;AACvC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,CAAC;AACZ,GAAG;AACH;AACA,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;AAChC,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC;AACxC,MAAM,KAAK,CAAC,MAAM;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE;AACnC,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,cAAc,EAAC;AAC1C;AACA,EAAE,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AACjD,IAAI,KAAK,CAAC,qBAAqB,EAAE,IAAI,EAAC;AACtC,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA,EAAE,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,EAAE,GAAG,EAAE,GAAG,EAAE;AACpC,EAAE,IAAI,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,eAAe,EAAC;AACnD;AACA;AACA;AACA,EAAE,OAAO,CAAC,YAAY;AACtB,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,YAAY,CAAC;AACrD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;AACpC,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;AAChC,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC;AAClC,MAAM,KAAK;AACX;;;;AC7QO,eAAe,OAAO,CAC3B,MAAsB,EACtB,aAAmD;;IAEnD,MAAM,GAAG,GAAGC,aAAO,EAAoB,CAAA;IACvC,MAAM,UAAU,GAAG,MAAMC,uBAAiB,CACxC,MAAM,CAAC,MAAM,EACb,GAAG,EACH,MAAMC,wBAAkB,CAAC,MAAM,CAAC,CACjC,CAAA;;IAGD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,CAAA;IAC9B,IAAI,IAAI,KAAK,KAAK,EAAE;QAClB,GAAG,CAAC,GAAG,CAACC,oBAAc,CAAC,OAAO,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;KAC/D;;IAGD,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;QACvB,GAAG,CAAC,GAAG,CAACC,qBAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAA;KAC7C;IAED,GAAG,CAAC,GAAG,CAACC,aAAW,EAAE,CAAC,CAAA;IAEtB,MAAM,OAAO,GAAGC,aAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IAC9D,GAAG,CAAC,GAAG,CACL,MAAM,CAAC,IAAI,EACXC,UAAI,CAAC,OAAO,EAAE;QACZ,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,IAAI;QACT,MAAM,EAAE,IAAI;KACb,CAAC,CACH,CAAA;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAA;IAC7B,MAAM,QAAQ,GAAGC,qBAAe,CAAC,MAAA,aAAa,CAAC,IAAI,mCAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IACpE,MAAM,IAAI,GAAG,MAAA,aAAa,CAAC,IAAI,mCAAI,IAAI,CAAA;IACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,CAAA;IACjD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;IAC5B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;IAExB,MAAM,UAAU,GAAG,MAAMC,qBAAe,CAAC,UAAU,EAAE;QACnD,IAAI;QACJ,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,MAAM;KACP,CAAC,CAAA;IAEF,MAAM,CAAC,IAAI,CACTC,YAAK,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,CAAC;QAC7DA,YAAK,CAAC,KAAK,CAAC,qCAAqC,CAAC,CACrD,CAAA;IAEDC,qBAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;IAElE,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;QACnEC,iBAAW,CACT,GAAG,QAAQ,MAAM,QAAQ,CAAC,IAAI,IAAI,UAAU,GAAG,IAAI,EAAE,EACrD,IAAI,EACJ,MAAM,CACP,CAAA;KACF;AACH;;ACzEA,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAA;AAqBvB;;;AAGA,SAAS,YAAY,CACnB,OAAgB;IAEhB,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,EAAE,CAAA;IAC1B,OAAO,GAAG,CAAC,IAAI,CAAC,CAAA;IAChB,OAAO,GAAG,CAAC,CAAC,CAAA;IACZ,OAAO,GAAG,CAAC,MAAM,CAAA;IACjB,OAAO,GAAG,CAAC,CAAC,CAAA;IACZ,OAAO,GAAG,CAAC,IAAI,CAAA;IACf,OAAO,GAAG,CAAC,IAAI,CAAA;IACf,OAAO,GAAG,CAAC,CAAC,CAAA;IACZ,OAAO,GAAG,CAAC,QAAQ,CAAA;IACnB,OAAO,GAAG,CAAC,WAAW,CAAA;IACtB,OAAO,GAAG,CAAC,CAAC,CAAA;IACZ,OAAO,GAAG,CAAC,KAAK,CAAA;IAChB,OAAO,GAAG,CAAC,CAAC,CAAA;IACZ,OAAO,GAAG,CAAC,MAAM,CAAA;IACjB,OAAO,GAAG,CAAC,CAAC,CAAA;IACZ,OAAO,GAAG,CAAC,IAAI,CAAA;IACf,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,GAAG;KACA,MAAM,CAAC,qBAAqB,EAAE,oCAAoC,CAAC;KACnE,MAAM,CAAC,mBAAmB,EAAE,uCAAuC,CAAC;KACpE,MAAM,CAAC,eAAe,EAAE,wCAAwC,CAAC;KACjE,MAAM,CAAC,wBAAwB,EAAE,uCAAuC,CAAC;KACzE,MAAM,CAAC,eAAe,EAAE,mDAAmD,CAAC;KAC5E,MAAM,CAAC,oBAAoB,EAAE,oCAAoC,CAAC;KAClE,MAAM,CAAC,uBAAuB,EAAE,4BAA4B,CAAC;KAC7D,MAAM,CAAC,mBAAmB,EAAE,uBAAuB,CAAC,CAAA;AAEvD;AACA,GAAG;KACA,OAAO,CAAC,QAAQ,CAAC;KACjB,KAAK,CAAC,OAAO,CAAC;KACd,MAAM,CAAC,eAAe,EAAE,2BAA2B,CAAC;KACpD,MAAM,CAAC,eAAe,EAAE,uBAAuB,CAAC;KAChD,MAAM,CAAC,SAAS,EAAE,4BAA4B,CAAC;KAC/C,MAAM,CAAC,eAAe,EAAE,4CAA4C,CAAC;KACrE,MAAM,CAAC,QAAQ,EAAE,uBAAuB,CAAC;KACzC,MAAM,CAAC,cAAc,EAAE,oDAAoD,CAAC;KAC5E,MAAM,CACL,SAAS,EACT,iEAAiE,CAClE;KACA,MAAM,CAAC,OAAO,IAAY,EAAE,OAAyC;;;IAGpE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,oDAAO,0BAAU,8CAAC,CAAA;IACjD,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC;YAChC,IAAI;YACJ,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,UAAU,EAAE,OAAO,CAAC,MAAM;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC;SAC9B,CAAC,CAAA;QACF,MAAM,MAAM,CAAC,MAAM,EAAE,CAAA;KACtB;IAAC,OAAO,CAAC,EAAE;QACVC,kBAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAClCH,YAAK,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC,KAAK,EAAE,CAAC,EACxD,EAAE,KAAK,EAAE,CAAC,EAAE,CACb,CAAA;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;AACH,CAAC,CAAC,CAAA;AAEJ;AACA,GAAG;KACA,OAAO,CAAC,cAAc,CAAC;KACvB,MAAM,CAAC,mBAAmB,EAAE,gDAAgD,CAAC;KAC7E,MAAM,CAAC,gBAAgB,EAAE,2CAA2C,CAAC;KACrE,MAAM,CACL,mBAAmB,EACnB,uEAAuE,CACxE;KACA,MAAM,CACL,8BAA8B,EAC9B,wEAAwE,CACzE;KACA,MAAM,CACL,eAAe,EACf,0DAA0D,CAC3D;KACA,MAAM,CACL,aAAa,EACb,yDAAyD,CAC1D;KACA,MAAM,CACL,qBAAqB,EACrB,gEAAgE;IAC9D,8CAA8C,CACjD;KACA,MAAM,CAAC,YAAY,EAAE,oCAAoC,CAAC;KAC1D,MAAM,CAAC,eAAe,EAAE,kCAAkC,CAAC;KAC3D,MAAM,CACL,eAAe,EACf,wDAAwD,CACzD;KACA,MAAM,CAAC,aAAa,EAAE,sDAAsD,CAAC;KAC7E,MAAM,CAAC,OAAO,IAAY,EAAE,OAAwC;IACnE,MAAM,SAAEI,OAAK,EAAE,GAAG,MAAM,oDAAO,0BAAS,8CAAC,CAAA;IACzC,MAAM,YAAY,GAAiB,YAAY,CAAC,OAAO,CAAC,CAAA;IAExD,IAAI;QACF,MAAMA,OAAK,CAAC;YACV,IAAI;YACJ,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,UAAU,EAAE,OAAO,CAAC,MAAM;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,KAAK,EAAE,YAAY;SACpB,CAAC,CAAA;KACH;IAAC,OAAO,CAAC,EAAE;QACVD,kBAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAClCH,YAAK,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,KAAK,EAAE,CAAC,EAC5C,EAAE,KAAK,EAAE,CAAC,EAAE,CACb,CAAA;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;AACH,CAAC,CAAC,CAAA;AAEJ;AACA,GAAG;KACA,OAAO,CAAC,iBAAiB,CAAC;KAC1B,MAAM,CACL,SAAS,EACT,iEAAiE,CAClE;KACA,MAAM,CACL,OAAO,IAAY,EAAE,OAA+C;IAClE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,oDAAO,0BAAa,4CAAC,CAAA;IACpD,IAAI;QACF,MAAM,MAAM,GAAG,MAAMK,mBAAa,CAChC;YACE,IAAI;YACJ,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,UAAU,EAAE,OAAO,CAAC,MAAM;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,EACD,OAAO,EACP,aAAa,CACd,CAAA;QACD,MAAM,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;KAChD;IAAC,OAAO,CAAC,EAAE;QACVF,kBAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAClCH,YAAK,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,KAAK,EAAE,CAAC,EACpD,EAAE,KAAK,EAAE,CAAC,EAAE,CACb,CAAA;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;AACH,CAAC,CACF,CAAA;AAEH,GAAG;KACA,OAAO,CAAC,gBAAgB,CAAC;KACzB,MAAM,CAAC,eAAe,EAAE,2BAA2B,CAAC;KACpD,MAAM,CAAC,eAAe,EAAE,uBAAuB,CAAC;KAChD,MAAM,CAAC,SAAS,EAAE,4BAA4B,CAAC;KAC/C,MAAM,CAAC,eAAe,EAAE,4CAA4C,CAAC;KACrE,MAAM,CAAC,cAAc,EAAE,oDAAoD,CAAC;KAC5E,MAAM,CACL,OACE,IAAY,EACZ,OAMoB;IAEpB,IAAI;QACF,MAAM,MAAM,GAAG,MAAMK,mBAAa,CAChC;YACE,IAAI;YACJ,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,UAAU,EAAE,OAAO,CAAC,MAAM;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE;gBACN,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,KAAK,EAAE,OAAO,CAAC,KAAK;aACrB;SACF,EACD,OAAO,EACP,YAAY,CACb,CAAA;QACD,MAAM,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAA;KAC7C;IAAC,OAAO,CAAC,EAAE;QACVF,kBAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAClCH,YAAK,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC,KAAK,EAAE,CAAC,EAC5D,EAAE,KAAK,EAAE,CAAC,EAAE,CACb,CAAA;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;AACH,CAAC,CACF,CAAA;AAEH,GAAG,CAAC,IAAI,EAAE,CAAA;AACV,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAA;AAElD,GAAG,CAAC,KAAK,EAAE"}