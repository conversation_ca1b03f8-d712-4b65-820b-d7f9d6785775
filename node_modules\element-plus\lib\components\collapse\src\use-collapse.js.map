{"version": 3, "file": "use-collapse.js", "sources": ["../../../../../../packages/components/collapse/src/use-collapse.ts"], "sourcesContent": ["import { computed, provide, ref, watch } from 'vue'\nimport {\n  debug<PERSON>arn,\n  ensureArray,\n  isBoolean,\n  isPromise,\n  throwError,\n} from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { collapseContextKey } from './constants'\n\nimport type { SetupContext } from 'vue'\nimport type {\n  CollapseActiveName,\n  CollapseEmits,\n  CollapseProps,\n} from './collapse'\n\nconst SCOPE = 'ElCollapse'\nexport const useCollapse = (\n  props: CollapseProps,\n  emit: SetupContext<CollapseEmits>['emit']\n) => {\n  const activeNames = ref(ensureArray(props.modelValue))\n\n  const setActiveNames = (_activeNames: CollapseActiveName[]) => {\n    activeNames.value = _activeNames\n    const value = props.accordion ? activeNames.value[0] : activeNames.value\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n  }\n\n  const handleChange = (name: CollapseActiveName) => {\n    if (props.accordion) {\n      setActiveNames([activeNames.value[0] === name ? '' : name])\n    } else {\n      const _activeNames = [...activeNames.value]\n      const index = _activeNames.indexOf(name)\n\n      if (index > -1) {\n        _activeNames.splice(index, 1)\n      } else {\n        _activeNames.push(name)\n      }\n      setActiveNames(_activeNames)\n    }\n  }\n\n  const handleItemClick = async (name: CollapseActiveName) => {\n    const { beforeCollapse } = props\n    if (!beforeCollapse) {\n      handleChange(name)\n      return\n    }\n\n    const shouldChange = beforeCollapse(name)\n    const isPromiseOrBool = [\n      isPromise(shouldChange),\n      isBoolean(shouldChange),\n    ].includes(true)\n    if (!isPromiseOrBool) {\n      throwError(\n        SCOPE,\n        'beforeCollapse must return type `Promise<boolean>` or `boolean`'\n      )\n    }\n\n    if (isPromise(shouldChange)) {\n      shouldChange\n        .then((result) => {\n          if (result !== false) {\n            handleChange(name)\n          }\n        })\n        .catch((e) => {\n          debugWarn(SCOPE, `some error occurred: ${e}`)\n        })\n    } else if (shouldChange) {\n      handleChange(name)\n    }\n  }\n\n  watch(\n    () => props.modelValue,\n    () => (activeNames.value = ensureArray(props.modelValue)),\n    { deep: true }\n  )\n\n  provide(collapseContextKey, {\n    activeNames,\n    handleItemClick,\n  })\n  return {\n    activeNames,\n    setActiveNames,\n  }\n}\n\nexport const useCollapseDOM = (props: CollapseProps) => {\n  const ns = useNamespace('collapse')\n\n  const rootKls = computed(() => [\n    ns.b(),\n    ns.b(`icon-position-${props.expandIconPosition}`),\n  ])\n\n  return {\n    rootKls,\n  }\n}\n"], "names": ["ref", "ensureArray", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "isPromise", "isBoolean", "throwError", "debugWarn", "watch", "provide", "collapseContextKey", "useNamespace", "computed"], "mappings": ";;;;;;;;;;;;;AAWA,MAAM,KAAK,GAAG,YAAY,CAAC;AACf,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAC5C,EAAE,MAAM,WAAW,GAAGA,OAAG,CAACC,uBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACzD,EAAE,MAAM,cAAc,GAAG,CAAC,YAAY,KAAK;AAC3C,IAAI,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC;AACrC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;AAC7E,IAAI,IAAI,CAACC,wBAAkB,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAACC,kBAAY,EAAE,KAAK,CAAC,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK;AACjC,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE;AACzB,MAAM,cAAc,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClE,KAAK,MAAM;AACX,MAAM,MAAM,YAAY,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAClD,MAAM,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/C,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACtC,OAAO,MAAM;AACb,QAAQ,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,OAAO;AACP,MAAM,cAAc,CAAC,YAAY,CAAC,CAAC;AACnC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,OAAO,IAAI,KAAK;AAC1C,IAAI,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;AACrC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AACzB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,MAAM,eAAe,GAAG;AAC5B,MAAMC,gBAAS,CAAC,YAAY,CAAC;AAC7B,MAAMC,eAAS,CAAC,YAAY,CAAC;AAC7B,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAMC,gBAAU,CAAC,KAAK,EAAE,iEAAiE,CAAC,CAAC;AAC3F,KAAK;AACL,IAAI,IAAIF,gBAAS,CAAC,YAAY,CAAC,EAAE;AACjC,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACpC,QAAQ,IAAI,MAAM,KAAK,KAAK,EAAE;AAC9B,UAAU,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7B,SAAS;AACT,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;AACtB,QAAQG,eAAS,CAAC,KAAK,EAAE,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,OAAO,CAAC,CAAC;AACT,KAAK,MAAM,IAAI,YAAY,EAAE;AAC7B,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL,GAAG,CAAC;AACJ,EAAEC,SAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,MAAM,WAAW,CAAC,KAAK,GAAGP,uBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACzG,EAAEQ,WAAO,CAACC,4BAAkB,EAAE;AAC9B,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,cAAc,GAAG,CAAC,KAAK,KAAK;AACzC,EAAE,MAAM,EAAE,GAAGC,kBAAY,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,MAAM,OAAO,GAAGC,YAAQ,CAAC,MAAM;AACjC,IAAI,EAAE,CAAC,CAAC,EAAE;AACV,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;AACrD,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;;"}