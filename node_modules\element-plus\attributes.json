{"el-affix/offset": {"type": "number", "description": "offset distance, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/affix.html#attributes)"}, "el-affix/position": {"type": "'top' | 'bottom'", "options": ["top", "bottom"], "description": "position of affix, default: top.\n\n[Docs](https://element-plus.org/en-US/component/affix.html#attributes)"}, "el-affix/target": {"type": "string", "description": "target container (CSS selector)\n\n[Docs](https://element-plus.org/en-US/component/affix.html#attributes)"}, "el-affix/z-index": {"type": "number", "description": "`z-index` of affix, default: 100.\n\n[Docs](https://element-plus.org/en-US/component/affix.html#attributes)"}, "el-affix/change": {"type": "event", "description": "triggers when fixed state changed\n\n[Docs](https://element-plus.org/en-US/component/affix.html#events)"}, "el-affix/scroll": {"type": "event", "description": "triggers when scrolling\n\n[Docs](https://element-plus.org/en-US/component/affix.html#events)"}, "el-alert/title": {"type": "string", "description": "alert title.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/type": {"type": "'primary'  | 'success' | 'warning' | 'info' | 'error' ", "options": ["primary", "success", "warning", "info", "error"], "description": "alert type., default: info.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/description": {"type": "string", "description": "descriptive text.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/closable": {"type": "boolean", "description": "whether alert can be dismissed., default: true.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/center": {"type": "boolean", "description": "whether content is placed in the center., default: false.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/close-text": {"type": "string", "description": "customized close button text.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/show-icon": {"type": "boolean", "description": "whether a type icon is displayed., default: false.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/effect": {"type": "'light' | 'dark'", "options": ["light", "dark"], "description": "theme style., default: light.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#attributes)"}, "el-alert/close": {"type": "event", "description": "trigger when alert is closed.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#events)"}, "el-anchor/change": {"type": "event", "description": "callback when the step changes\n\n[Docs](https://element-plus.org/en-US/component/anchor.html#anchor-events)"}, "el-anchor/click": {"type": "event", "description": "Triggered when the user clicks on the link\n\n[Docs](https://element-plus.org/en-US/component/anchor.html#anchor-events)"}, "el-autocomplete/model-value": {"type": "string", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/placeholder": {"type": "string", "description": "the placeholder of Autocomplete\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/clearable": {"type": "boolean", "description": "whether to show clear button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/disabled": {"type": "boolean", "description": "whether Autocomplete is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/value-key": {"type": "string", "description": "key name of the input suggestion object for display, default: value.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/debounce": {"type": "number", "description": "debounce delay when typing, in milliseconds, default: 300.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/placement": {"type": "'top' | 'top- start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end'", "options": ["top", "bottom"], "description": "placement of the popup menu, default: bottom-start.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/fetch-suggestions": {"type": "Array | (queryString: string, callback: callbackfn) => void", "description": "a method to fetch input suggestions. When suggestions are ready, invoke `callback(data:[])` to return them to Autocomplete\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/trigger-on-focus": {"type": "boolean", "description": "whether show suggestions when input focus, default: true.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/select-when-unmatched": {"type": "boolean", "description": "whether to emit a `select` event on enter when there is no autocomplete match, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/name": {"type": "string", "description": "same as `name` in native input\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/aria-label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/hide-loading": {"type": "boolean", "description": "whether to hide the loading icon in remote search, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/popper-class": {"type": "string", "description": "custom class name for autocomplete's dropdown\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/teleported": {"type": "boolean", "description": "whether select dropdown is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/append-to": {"type": "CSSSelector | HTMLElement", "description": "which select dropdown appends to\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/highlight-first-item": {"type": "boolean", "description": "whether to highlight first item in remote search suggestions by default, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/fit-input-width": {"type": "boolean", "description": "whether the width of the dropdown is the same as the input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/popper-append-to-body": {"type": "boolean", "description": "whether to append the dropdown to body. If the positioning of the dropdown is wrong, you can try to set this prop to false, default: false.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#attributes)"}, "el-autocomplete/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#events)"}, "el-autocomplete/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#events)"}, "el-autocomplete/input": {"type": "event", "description": "triggers when the Input value change\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#events)"}, "el-autocomplete/clear": {"type": "event", "description": "triggers when the Input is cleared by clicking the clear button\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#events)"}, "el-autocomplete/select": {"type": "event", "description": "triggers when a suggestion is clicked\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#events)"}, "el-autocomplete/change": {"type": "event", "description": "triggers when the icon inside Input value change\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#events)"}, "el-avatar/icon": {"type": "string | Component", "description": "representation type to icon, more info on icon component.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/size": {"type": "number | 'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "avatar size., default: default.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/shape": {"type": "'circle' | 'square'", "options": ["circle", "square"], "description": "avatar shape., default: circle.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/src": {"type": "string", "description": "the source of the image for an image avatar.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/src-set": {"type": "string", "description": "native attribute `srcset` of image avatar.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/alt": {"type": "string", "description": "native attribute `alt` of image avatar.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/fit": {"type": "'fill' | 'contain' | 'cover' | 'none' | 'scale-down'", "options": ["fill", "contain", "cover", "none"], "description": "set how the image fit its container for an image avatar., default: cover.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#attributes)"}, "el-avatar/error": {"type": "event", "description": "trigger when image load error.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#events)"}, "el-backtop/target": {"type": "string", "description": "the target to trigger scroll.\n\n[Docs](https://element-plus.org/en-US/component/backtop.html#attributes)"}, "el-backtop/visibility-height": {"type": "number", "description": "the button will not show until the scroll height reaches this value., default: 200.\n\n[Docs](https://element-plus.org/en-US/component/backtop.html#attributes)"}, "el-backtop/right": {"type": "number", "description": "right distance., default: 40.\n\n[Docs](https://element-plus.org/en-US/component/backtop.html#attributes)"}, "el-backtop/bottom": {"type": "number", "description": "bottom distance., default: 40.\n\n[Docs](https://element-plus.org/en-US/component/backtop.html#attributes)"}, "el-backtop/click": {"type": "event", "description": "triggers when click.\n\n[Docs](https://element-plus.org/en-US/component/backtop.html#events)"}, "el-badge/value": {"type": "string | number", "description": "display value., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/max": {"type": "number", "description": "maximum value, shows `{max}+` when exceeded. Only works if value is a number., default: 99.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/is-dot": {"type": "boolean", "description": "if a little dot is displayed., default: false.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/hidden": {"type": "boolean", "description": "hidden badge., default: false.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info'", "options": ["primary", "success", "warning", "danger", "info"], "description": "badge type., default: danger.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/show-zero": {"type": "boolean", "description": "Whether to show badge when value is zero., default: true.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/color": {"type": "string", "description": "background color of the dot\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/offset": {"type": "[ `number` , `number` ]", "description": "offset of badge\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/badge-style": {"type": "CSSProperties", "description": "custom style of badge\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-badge/badge-class": {"type": "string", "description": "custom class of badge\n\n[Docs](https://element-plus.org/en-US/component/badge.html#attributes)"}, "el-breadcrumb/separator": {"type": "string", "description": "separator character, default: /.\n\n[Docs](https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb-attributes)"}, "el-breadcrumb/separator-icon": {"type": "string | Component", "description": "icon component of icon separator\n\n[Docs](https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb-attributes)"}, "el-breadcrumb-item/to": {"type": "string | RouteLocationRaw", "description": "target route of the link, same as `to` of `vue-router`, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem-attributes)"}, "el-breadcrumb-item/replace": {"type": "boolean", "description": "if `true`, the navigation will not leave a history record, default: false.\n\n[Docs](https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem-attributes)"}, "el-button/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "button size\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/type": {"type": "'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info' | '' | 'text' ", "options": ["default", "primary", "success", "warning", "danger", "info", "", "text"], "description": "button type, when setting `color`, the latter prevails\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/plain": {"type": "boolean", "description": "determine whether it's a plain button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/text": {"type": "boolean", "description": "determine whether it's a text button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/bg": {"type": "boolean", "description": "determine whether the text button background color is always on, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/link": {"type": "boolean", "description": "determine whether it's a link button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/round": {"type": "boolean", "description": "determine whether it's a round button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/circle": {"type": "boolean", "description": "determine whether it's a circle button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/loading": {"type": "boolean", "description": "determine whether it's loading, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/loading-icon": {"type": "string | Component", "description": "customize loading icon component, default: Loading.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/disabled": {"type": "boolean", "description": "disable the button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/icon": {"type": "string | Component", "description": "icon component\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/autofocus": {"type": "boolean", "description": "same as native button's `autofocus`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/native-type": {"type": "'button' | 'submit' | 'reset'", "options": ["button", "submit", "reset"], "description": "same as native button's `type`, default: button.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/auto-insert-space": {"type": "boolean", "description": "automatically insert a space between two chinese characters(this will only take effect when the text length is 2 and all characters are in Chinese.), default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/color": {"type": "string", "description": "custom button color, automatically calculate `hover` and `active` color\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/dark": {"type": "boolean", "description": "dark mode, which automatically converts `color` to dark mode colors, default: false.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button/tag": {"type": "string | Component", "description": "custom element tag, default: button.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button-attributes)"}, "el-button-group/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "control the size of buttons in this button-group\n\n[Docs](https://element-plus.org/en-US/component/button.html#buttongroup-attributes)"}, "el-button-group/type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info'", "options": ["primary", "success", "warning", "danger", "info"], "description": "control the type of buttons in this button-group\n\n[Docs](https://element-plus.org/en-US/component/button.html#buttongroup-attributes)"}, "el-calendar/model-value": {"type": "Date", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/calendar.html#attributes)"}, "el-calendar/range": {"type": "[Date, Date]", "description": "time range, including start time and end time. Start time must be start day of week, end time must be end day of week, the time span cannot exceed two months.\n\n[Docs](https://element-plus.org/en-US/component/calendar.html#attributes)"}, "el-card/header": {"type": "string", "description": "title of the card. Also accepts a DOM passed by `slot#header`\n\n[Docs](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-card/footer": {"type": "string", "description": "footer of the card. Also accepts a DOM passed by `slot#footer`\n\n[Docs](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-card/body-style": {"type": "CSSProperties", "description": "CSS style of card body\n\n[Docs](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-card/header-class": {"type": "string", "description": "custom class name of card header\n\n[Docs](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-card/body-class": {"type": "string", "description": "custom class name of card body\n\n[Docs](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-card/footer-class": {"type": "string", "description": "custom class name of card footer\n\n[Doc<PERSON>](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-card/shadow": {"type": "always | never | hover", "description": "when to show card shadows, default: always.\n\n[Docs](https://element-plus.org/en-US/component/card.html#attributes)"}, "el-carousel/height": {"type": "string", "description": "height of the carousel, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/initial-index": {"type": "number", "description": "index of the initially active slide (starting from 0), default: 0.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/trigger": {"type": "'hover' | 'click'", "options": ["hover", "click"], "description": "how indicators are triggered, default: hover.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/autoplay": {"type": "boolean", "description": "whether automatically loop the slides, default: true.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/interval": {"type": "number", "description": "interval of the auto loop, in milliseconds, default: 3000.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/indicator-position": {"type": "'' | 'none' | 'outside'", "options": ["", "none", "outside"], "description": "position of the indicators, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/arrow": {"type": "'always' | 'hover' | 'never'", "options": ["always", "hover", "never"], "description": "when arrows are shown, default: hover.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/type": {"type": "'' | 'card'", "options": ["", "card"], "description": "type of the Carousel, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/card-scale": {"type": "number", "description": "when type is card, scaled size of secondary cards, default: 0.83.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/loop": {"type": "boolean", "description": "display the items in loop, default: true.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/direction": {"type": "'horizontal' | 'vertical'", "options": ["horizontal", "vertical"], "description": "display direction, default: horizontal.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/pause-on-hover": {"type": "boolean", "description": "pause autoplay when hover, default: true.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/motion-blur": {"type": "boolean", "description": "infuse dynamism and smoothness into the carousel, default: false.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-attributes)"}, "el-carousel/change": {"type": "event", "description": "triggers when the active slide switches, it has two parameters, the one is the index of the new active slide, and other is index of the old active slide\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-events)"}, "el-carousel-item/name": {"type": "string", "description": "name of the item, can be used in `setActiveItem`, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-item-attributes)"}, "el-carousel-item/label": {"type": "string | number", "description": "text content for the corresponding indicator, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-item-attributes)"}, "el-cascader/model-value": {"type": "string|number|string[] | number[] | any", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/options": {"type": "Record<string, unknown>[]", "description": "data of the options, the key of `value` and `label` can be customize by `CascaderProps`.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/props": {"type": "CascaderProps", "description": "configuration options, see the following `CascaderProps` table.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of input\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/placeholder": {"type": "string", "description": "placeholder of input\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/disabled": {"type": "boolean", "description": "whether <PERSON><PERSON> is disabled\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/clearable": {"type": "boolean", "description": "whether selected value can be cleared\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/show-all-levels": {"type": "boolean", "description": "whether to display all levels of the selected value in the input, default: true.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/collapse-tags": {"type": "boolean", "description": "whether to collapse tags in multiple selection mode\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/collapse-tags-tooltip": {"type": "boolean", "description": "whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true, default: false.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/separator": {"type": "string", "description": "option label separator, default: ' / '.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/filterable": {"type": "boolean", "description": "whether the options can be searched\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/filter-method": {"type": "(node: <PERSON>r<PERSON><PERSON>, keyword: string) => boolean", "description": "customize search logic, the first parameter is `node`, the second is `keyword`, and need return a boolean value indicating whether it hits.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/debounce": {"type": "number", "description": "debounce delay when typing filter keyword, in milliseconds, default: 300.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/before-filter": {"type": "(value: string) => boolean", "description": "hook function before filtering with the value to be filtered as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, filtering will be aborted\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/popper-class": {"type": "string", "description": "custom class name for Cascader's dropdown, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/teleported": {"type": "boolean", "description": "whether cascader popup is teleported, default: true.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/tag-type": {"type": "'success' | 'info' | 'warning' | 'danger'", "options": ["success", "info", "warning", "danger"], "description": "tag type, default: info.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/tag-effect": {"type": "'light' | 'dark' | 'plain'", "options": ["light", "dark", "plain"], "description": "tag effect, default: light.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/max-collapse-tags": {"type": "number", "description": "The max tags number to be shown. To use this, `collpase-tags` must be true, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/empty-values": {"type": "array", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/value-on-clear": {"type": "string | number | boolean | Function", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/persistent": {"type": "boolean", "description": "when dropdown is inactive and `persistent` is `false`, dropdown will be destroyed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/fallback-placements": {"type": "Placement[]", "description": "list of possible positions for Tooltip [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "position of dropdown, default: bottom-start.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/popper-append-to-body": {"type": "boolean", "description": "whether to append the popper menu to body. If the positioning of the popper is wrong, you can try to set this prop to false, default: true.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-attributes)"}, "el-cascader/change": {"type": "event", "description": "triggers when the binding value changes\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader/expand-change": {"type": "event", "description": "triggers when expand option changes\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader/blur": {"type": "event", "description": "triggers when <PERSON><PERSON> blurs\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader/focus": {"type": "event", "description": "triggers when <PERSON><PERSON> focuses\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader/clear": {"type": "event", "description": "triggers when the clear icon is clicked in a clearable Select\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader/visible-change": {"type": "event", "description": "triggers when the dropdown appears/disappears\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader/remove-tag": {"type": "event", "description": "triggers when remove tag in multiple selection mode\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader-events)"}, "el-cascader-panel/model-value": {"type": "string|number|string[] | number[] | any", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel-attributes)"}, "el-cascader-panel/options": {"type": "Record<string, unknown>[]", "description": "data of the options, the key of `value` and `label` can be customize by `CascaderProps`.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel-attributes)"}, "el-cascader-panel/props": {"type": "CascaderProps", "description": "configuration options, see the following `CascaderProps` table.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel-attributes)"}, "el-cascader-panel/change": {"type": "event", "description": "triggers when the binding value changes\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel-events)"}, "el-cascader-panel/expand-change": {"type": "event", "description": "triggers when expand option changes\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel-events)"}, "el-cascader-panel/close": {"type": "event", "description": "close panel event, provided to <PERSON><PERSON> to put away the panel judgment.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel-events)"}, "el-checkbox/model-value": {"type": "string | number | boolean", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/value": {"type": "string | number | boolean | object", "description": "value of the Checkbox when used inside a `checkbox-group`\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/label": {"type": "string | number | boolean | object", "description": "label of the Checkbox when used inside a `checkbox-group`. If there's no value, `label` will act as `value`\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/true-value": {"type": "string | number", "description": "value of the Checkbox if it's checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/false-value": {"type": "string | number", "description": "value of the Checkbox if it's not checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/disabled": {"type": "boolean", "description": "whether the Checkbox is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/border": {"type": "boolean", "description": "whether to add a border around Checkbox, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of the Checkbox\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/name": {"type": "string", "description": "native 'name' attribute\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/checked": {"type": "boolean", "description": "if the Checkbox is checked, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/indeterminate": {"type": "boolean", "description": "Set indeterminate state, only responsible for style control, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/tabindex": {"type": "string | number", "description": "input tabindex\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/id": {"type": "string", "description": "input id\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/aria-controls": {"type": "string", "description": "same as [aria-controls](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-controls), takes effect when `indeterminate` is `true`\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/true-label": {"type": "string | number", "description": "value of the Checkbox if it's checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/false-label": {"type": "string | number", "description": "value of the Checkbox if it's not checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/controls": {"type": "string", "description": "same as [aria-controls](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-controls), takes effect when `indeterminate` is `true`\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes)"}, "el-checkbox/change": {"type": "event", "description": "triggers when the binding value changes\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox-events)"}, "el-checkbox-group/model-value": {"type": "string[] | number[]", "description": "binding value, default: [].\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of checkbox\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/disabled": {"type": "boolean", "description": "whether the nesting checkboxes are disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/min": {"type": "number", "description": "minimum number of checkbox checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/max": {"type": "number", "description": "maximum number of checkbox checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/aria-label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/text-color": {"type": "string", "description": "font color when button is active, default: #ffffff.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/fill": {"type": "string", "description": "border and background color when button is active, default: #409eff.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/tag": {"type": "string", "description": "element tag of the checkbox group, default: div.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes)"}, "el-checkbox-group/change": {"type": "event", "description": "triggers when the binding value changes\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-events)"}, "el-checkbox-button/value": {"type": "string | number | boolean | object", "description": "value of the checkbox when used inside a `checkbox-group`\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/label": {"type": "string | number | boolean | object", "description": "label of the checkbox when used inside a `checkbox-group`. If there's no value, `label` will act as `value`\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/true-value": {"type": "string | number", "description": "value of the checkbox if it's checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/false-value": {"type": "string | number", "description": "value of the checkbox if it's not checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/disabled": {"type": "boolean", "description": "whether the checkbox is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/name": {"type": "string", "description": "native 'name' attribute\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/checked": {"type": "boolean", "description": "if the checkbox is checked, default: false.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/true-label": {"type": "string | number", "description": "value of the checkbox if it's checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-checkbox-button/false-label": {"type": "string | number", "description": "value of the checkbox if it's not checked\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes)"}, "el-collapse/model-value": {"type": "string | array", "description": "currently active panel, the type is `string` in accordion mode, otherwise it is `array`, default: [].\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-attributes)"}, "el-collapse/accordion": {"type": "boolean", "description": "whether to activate accordion mode, default: false.\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-attributes)"}, "el-collapse/expand-icon-position": {"type": "'left' | 'right' ", "options": ["left", "right"], "description": "set expand icon position, default: right.\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-attributes)"}, "el-collapse/before-collapse": {"type": "() => Promise<boolean> | boolen", "description": "before-collapse hook before the collapse state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop collapsing\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-attributes)"}, "el-collapse/change": {"type": "event", "description": "triggers when active panels change, the parameter type is `string` in accordion mode, otherwise it is `array`\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-events)"}, "el-collapse-item/name": {"type": "string | number", "description": "unique identification of the panel\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes)"}, "el-collapse-item/title": {"type": "string", "description": "title of the panel, default: ''.\n\n[<PERSON><PERSON>](https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes)"}, "el-collapse-item/icon": {"type": "string | Component", "description": "icon of the collapse item, default: ArrowRight.\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes)"}, "el-collapse-item/disabled": {"type": "boolean", "description": "disable the collapse item, default: false.\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes)"}, "el-color-picker/model-value": {"type": "string", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/disabled": {"type": "boolean", "description": "whether to disable the ColorPicker, default: false.\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of ColorPicker\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/show-alpha": {"type": "boolean", "description": "whether to display the alpha slider, default: false.\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/color-format": {"type": "'hsl' | 'hsv' | 'hex' | 'rgb' | 'hex'  | 'rgb' ", "options": ["hsl", "hsv", "hex", "rgb", "hex", "rgb"], "description": "color format of v-model\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/popper-class": {"type": "string", "description": "custom class name for ColorPicker's dropdown\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/predefine": {"type": "string[]", "description": "predefined color options\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/tabindex": {"type": "string | number", "description": "ColorPicker tabindex, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/aria-label": {"type": "string", "description": "ColorPicker aria-label\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/id": {"type": "string", "description": "ColorPicker id\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/teleported": {"type": "boolean", "description": "whether color-picker popper is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/label": {"type": "string", "description": "ColorPicker aria-label\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#attributes)"}, "el-color-picker/change": {"type": "event", "description": "triggers when input value changes\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#events)"}, "el-color-picker/active-change": {"type": "event", "description": "triggers when the current active color changes\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#events)"}, "el-color-picker/focus": {"type": "event", "description": "triggers when Component focuses\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#events)"}, "el-color-picker/blur": {"type": "event", "description": "triggers when Component blurs\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#events)"}, "el-config-provider/locale": {"type": "{name: string, el: TranslatePair} ", "description": "Locale Object, default: [en](https://github.com/element-plus/element-plus/blob/dev/packages/locale/lang/en.ts).\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "global component size, default: default.\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/z-index": {"type": "number", "description": "global Initial zIndex\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/namespace": {"type": "string", "description": "global component className prefix (cooperated with [$namespace](https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/mixins/config.scss#L1)), default: el.\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/button": {"type": "{autoInsertSpace?: boolean, type?: string, plain?: boolean, round?: boolean}", "description": "button related configuration, [see the following table](#button-attribute), default: see the following table.\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/link": {"type": "{type?: string, underline?: boolean | string}", "description": "link related configuration, [see the following table](#link-attribute), default: see the following table.\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/message": {"type": "{max?: number}", "description": "message related configuration, [see the following table](#message-attribute), default: see the following table.\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/experimental-features": {"type": "object", "description": "features at experimental stage to be added, all features are default to be set to false\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/empty-values": {"type": "array", "description": "global empty values of components\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-config-provider/value-on-clear": {"type": "string | number | boolean | Function", "description": "global clear return value\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes)"}, "el-container/direction": {"type": "'horizontal' | 'vertical'", "options": ["horizontal", "vertical"], "description": "layout direction for child elements, default: vertical when nested with `el-header` or `el-footer`; horizontal otherwise.\n\n[Docs](https://element-plus.org/en-US/component/container.html#container-attributes)"}, "el-header/height": {"type": "string", "description": "height of the header, default: 60px.\n\n[Docs](https://element-plus.org/en-US/component/container.html#header-attributes)"}, "el-aside/width": {"type": "string", "description": "width of the side section, default: 300px.\n\n[Docs](https://element-plus.org/en-US/component/container.html#aside-attributes)"}, "el-footer/height": {"type": "string", "description": "height of the footer, default: 60px.\n\n[Docs](https://element-plus.org/en-US/component/container.html#footer-attributes)"}, "el-date-picker/model-value": {"type": "number | string | Date | [Date, Date] | [string, string]", "description": "binding value, if it is an array, the length should be 2, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/readonly": {"type": "boolean", "description": "whether DatePicker is read only, default: false.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/disabled": {"type": "boolean", "description": "whether DatePicker is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of Input\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/editable": {"type": "boolean", "description": "whether the input is editable, default: true.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/clearable": {"type": "boolean", "description": "whether to show clear button, default: true.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/placeholder": {"type": "string", "description": "placeholder in non-range mode, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/start-placeholder": {"type": "string", "description": "placeholder for the start date in range mode\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/end-placeholder": {"type": "string", "description": "placeholder for the end date in range mode\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/type": {"type": "'year' | 'years' |'month' | 'months' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange' | 'yearrange'", "options": ["year", "years", "month", "months", "date", "dates", "datetime", "week", "datetimerange", "daterange", "monthrange", "yearrange"], "description": "type of the picker, default: date.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/format": {"type": "string see ", "description": "format of the displayed value in the input box, default: YYYY-MM-DD.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/popper-class": {"type": "string", "description": "custom class name for Date<PERSON><PERSON>'s dropdown\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/popper-options": {"type": "Partial<PopperOptions>", "description": "Customized popper option see more at [popper.js](https://popper.js.org/docs/v2/), default: {}.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/range-separator": {"type": "string", "description": "range separator, default: '-'.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/default-value": {"type": "Date | [Date, Date]", "description": "optional, default date of the calendar\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/default-time": {"type": "Date | [Date, Date]", "description": "optional, the time value to use when selecting date range\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/value-format": {"type": "string see ", "description": "optional, format of binding value. If not specified, the binding value will be a Date object\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/id": {"type": "string | [string, string]", "description": "same as `id` in native input\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/name": {"type": "string | [string, string]", "description": "same as `name` in native input, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/unlink-panels": {"type": "boolean", "description": "unlink two date-panels in range-picker, default: false.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/prefix-icon": {"type": "string | Component", "description": "custom prefix icon component. By default, if the value of `type` is `TimeLikeType`, the value is `Clock`, else is `Calendar`, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/clear-icon": {"type": "string | Component", "description": "custom clear icon component, default: CircleClose.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/disabled-date": {"type": "(data: Date) => boolean", "description": "a function determining if a date is disabled with that date as its parameter. Should return a Boolean\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/shortcuts": {"type": "Array<{ text: string, value: Date | Function }>", "description": "an object array to set shortcut options, default: [].\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/cell-class-name": {"type": "(data: Date) => string", "description": "set custom className\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/teleported": {"type": "boolean", "description": "whether date-picker dropdown is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/empty-values": {"type": "array", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/value-on-clear": {"type": "string | number | boolean | Function", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/fallback-placements": {"type": "Placement[]", "description": "list of possible positions for Tooltip [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/placement": {"type": "Placement", "description": "position of dropdown, default: bottom.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#attributes)"}, "el-date-picker/change": {"type": "event", "description": "triggers when user confirms the value\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-date-picker/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-date-picker/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-date-picker/clear": {"type": "event", "description": "triggers when the clear icon is clicked in a clearable DatePicker\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-date-picker/calendar-change": {"type": "event", "description": "triggers when the calendar selected date is changed.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-date-picker/panel-change": {"type": "event", "description": "triggers when the navigation button click.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-date-picker/visible-change": {"type": "event", "description": "triggers when the DatePicker's dropdown appears/disappears\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#events)"}, "el-descriptions/border": {"type": "boolean", "description": "with or without border, default: false.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions/column": {"type": "number", "description": "numbers of `Descriptions Item` in one line, default: 3.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions/direction": {"type": "'vertical' | 'horizontal'", "options": ["vertical", "horizontal"], "description": "direction of list, default: horizontal.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of list\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions/title": {"type": "string", "description": "title text, display on the top left, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions/extra": {"type": "string", "description": "extra text, display on the top right, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions/label-width": {"type": "string | number", "description": "label width of every column, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes)"}, "el-descriptions-item/label": {"type": "string", "description": "label text, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/span": {"type": "number", "description": "colspan of column, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/rowspan": {"type": "number", "description": "the number of rows a cell should span, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/width": {"type": "string | number", "description": "column width, the width of the same column in different rows is set by the max value (If no `border`, width contains label and content), default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/min-width": {"type": "string | number", "description": "column minimum width, columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion (If no`border`, width contains label and content), default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/label-width": {"type": "string | number", "description": "column label width, if not set, it will be the same as the width of the column. Higher priority than the `label-width` of `Descriptions`, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/align": {"type": "'left' | 'center' | 'right'", "options": ["left", "center", "right"], "description": "column content alignment (If no `border`, effective for both label and content), default: left.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/label-align": {"type": "'left' | 'center' | 'right'", "options": ["left", "center", "right"], "description": "column label alignment, if omitted, the value of the above `align` attribute will be applied (If no `border`, please use `align` attribute)\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/class-name": {"type": "string", "description": "column content custom class name, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-descriptions-item/label-class-name": {"type": "string", "description": "column label custom class name, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes)"}, "el-dialog/model-value": {"type": "boolean", "description": "visibility of Dialog\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/title": {"type": "string", "description": "title of Dialog. Can also be passed with a named slot (see the following table), default: ''.\n\n[<PERSON>s](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/width": {"type": "string | number", "description": "width of Dialog, default is 50%, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/fullscreen": {"type": "boolean", "description": "whether the Dialog takes up full screen, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/top": {"type": "string", "description": "value for `margin-top` of Dialog CSS, default is 15vh, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/modal": {"type": "boolean", "description": "whether a mask is displayed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/modal-class": {"type": "string", "description": "custom class names for mask\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/header-class": {"type": "string", "description": "custom class names for header wrapper\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/body-class": {"type": "string", "description": "custom class names for body wrapper\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/footer-class": {"type": "string", "description": "custom class names for footer wrapper\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/append-to-body": {"type": "boolean", "description": "whether to append Dialog itself to body. A nested Dialog should have this attribute set to `true`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/append-to": {"type": "CSSSelector | HTMLElement", "description": "which element the Dialog appends to. <PERSON> override `append-to-body`, default: body.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/lock-scroll": {"type": "boolean", "description": "whether scroll of body is disabled while <PERSON><PERSON> is displayed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/open-delay": {"type": "number", "description": "the Time(milliseconds) before open, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/close-delay": {"type": "number", "description": "the Time(milliseconds) before close, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/close-on-click-modal": {"type": "boolean", "description": "whether the Dialog can be closed by clicking the mask, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/close-on-press-escape": {"type": "boolean", "description": "whether the Dialog can be closed by pressing ESC, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/show-close": {"type": "boolean", "description": "whether to show a close button, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/before-close": {"type": "(done: DoneFn) => void", "description": "callback before Dialog closes, and it will prevent Dialog from closing, use done to close the dialog\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/draggable": {"type": "boolean", "description": "enable dragging feature for Dialog, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/overflow": {"type": "boolean", "description": "draggable Dialog can overflow the viewport, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/center": {"type": "boolean", "description": "whether to align the header and footer in center, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/align-center": {"type": "boolean", "description": "whether to align the dialog both horizontally and vertically, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/destroy-on-close": {"type": "boolean", "description": "destroy elements in Dialog when closed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/close-icon": {"type": "string | Component", "description": "custom close icon, default is Close\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/z-index": {"type": "number", "description": "same as z-index in native CSS, z-order of dialog\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/header-aria-level": {"type": "string", "description": "header's `aria-level` attribute, default: 2.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/custom-class": {"type": "string", "description": "custom class names for Dialog, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#attributes)"}, "el-dialog/open": {"type": "event", "description": "triggers when the Dialog opens\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#events)"}, "el-dialog/opened": {"type": "event", "description": "triggers when the Dialog opening animation ends\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#events)"}, "el-dialog/close": {"type": "event", "description": "triggers when the Dialog closes\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#events)"}, "el-dialog/closed": {"type": "event", "description": "triggers when the Dialog closing animation ends\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#events)"}, "el-dialog/open-auto-focus": {"type": "event", "description": "triggers after Dialog opens and content focused\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#events)"}, "el-dialog/close-auto-focus": {"type": "event", "description": "triggers after Dialog closed and content focused\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#events)"}, "el-divider/direction": {"type": "'horizontal' | 'vertical'", "options": ["horizontal", "vertical"], "description": "Set divider's direction, default: horizontal.\n\n[Docs](https://element-plus.org/en-US/component/divider.html#attributes)"}, "el-divider/border-style": {"type": "'none' | 'solid' | 'hidden' | 'dashed' | ... ", "options": ["none", "solid", "hidden", "dashed"], "description": "Set the style of divider, default: solid.\n\n[Docs](https://element-plus.org/en-US/component/divider.html#attributes)"}, "el-divider/content-position": {"type": "'left' | 'right' | 'center' ", "options": ["left", "right", "center"], "description": "the position of the customized content on the divider line, default: center.\n\n[Docs](https://element-plus.org/en-US/component/divider.html#attributes)"}, "el-drawer/model-value": {"type": "boolean", "description": "Should Drawer be displayed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/append-to-body": {"type": "boolean", "description": "Controls should Drawer be inserted to DocumentBody Element, nested Drawer must assign this param to **true**, default: false.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/append-to": {"type": "CSSSelector | HTMLElement", "description": "which element the Drawer appends to. <PERSON> override `append-to-body`, default: body.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/lock-scroll": {"type": "boolean", "description": "whether scroll of body is disabled while <PERSON><PERSON> is displayed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/before-close": {"type": "(done: (cancel?: boolean) => void) => void", "description": "If set, closing procedure will be halted\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/close-on-click-modal": {"type": "boolean", "description": "whether the Drawer can be closed by clicking the mask, default: true.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/close-on-press-escape": {"type": "boolean", "description": "Indicates whether Draw<PERSON> can be closed by pressing ESC, default: true.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/open-delay": {"type": "number", "description": "Time(milliseconds) before open, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/close-delay": {"type": "number", "description": "Time(milliseconds) before close, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/destroy-on-close": {"type": "boolean", "description": "Indicates whether children should be destroyed after <PERSON><PERSON> closed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/modal": {"type": "boolean", "description": "Should show shadowing layer, default: true.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/direction": {"type": "'rtl' | 'ltr' | 'ttb' | 'btt'", "options": ["rtl", "ltr", "ttb", "btt"], "description": "Drawer's opening direction, default: rtl.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/show-close": {"type": "boolean", "description": "Should show close button at the top right of Drawer, default: true.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/size": {"type": "number | string", "description": "Drawer's size, if Drawer is horizontal mode, it effects the width property, otherwise it effects the height property, when size is `number` type, it describes the size by unit of pixels; when size is `string` type, it should be used with `x%` notation, other wise it will be interpreted to pixel unit, default: 30%.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/title": {"type": "string", "description": "Drawer's title, can also be set by named slot, detailed descriptions can be found in the slot form\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/with-header": {"type": "boolean", "description": "Flag that controls the header section's existance, default to true, when withHeader set to false, both `title attribute` and `title slot` won't work, default: true.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/modal-class": {"type": "string", "description": "Extra class names for shadowing layer\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/header-class": {"type": "string", "description": "custom class names for header wrapper\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/body-class": {"type": "string", "description": "custom class names for body wrapper\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/footer-class": {"type": "string", "description": "custom class names for footer wrapper\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/z-index": {"type": "number", "description": "set z-index\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/header-aria-level": {"type": "string", "description": "header's `aria-level` attribute, default: 2.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/custom-class": {"type": "string", "description": "Extra class names for Drawer\n\n[<PERSON><PERSON>](https://element-plus.org/en-US/component/drawer.html#attributes)"}, "el-drawer/open": {"type": "event", "description": "Triggered before Drawer opening animation begins\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#events)"}, "el-drawer/opened": {"type": "event", "description": "Triggered after <PERSON>er opening animation ended\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#events)"}, "el-drawer/close": {"type": "event", "description": "Triggered before Drawer closing animation begins\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#events)"}, "el-drawer/closed": {"type": "event", "description": "Triggered after Drawer closing animation ended\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#events)"}, "el-drawer/open-auto-focus": {"type": "event", "description": "triggers after Drawer opens and content focused\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#events)"}, "el-drawer/close-auto-focus": {"type": "event", "description": "triggers after <PERSON><PERSON> closed and content focused\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#events)"}, "el-dropdown/type": {"type": "'' | 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text' ", "options": ["", "default", "primary", "success", "warning", "info", "danger", "text"], "description": "menu button type, refer to `Button` Component, only works when `split-button` is true, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "menu size, also works on the split button, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/button-props": {"type": "object", "description": "props for the button component, refer to [<PERSON><PERSON> Attributes](./button.html#button-attributes)\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/max-height": {"type": "string | number", "description": "the max height of menu, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/split-button": {"type": "boolean", "description": "whether a button group is displayed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/disabled": {"type": "boolean", "description": "whether to disable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end'", "options": ["top", "bottom"], "description": "placement of pop menu, default: bottom.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/trigger": {"type": "'hover' | 'click' | 'contextmenu'", "options": ["hover", "click", "contextmenu"], "description": "how to trigger, default: hover.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/trigger-keys": {"type": "string[]", "description": "specify which keys on the keyboard can trigger when pressed, default: ['Enter', 'Space', 'ArrowDown', 'NumpadEnter'].\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/hide-on-click": {"type": "boolean", "description": "whether to hide menu after clicking menu-item, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/show-timeout": {"type": "number", "description": "delay time before show a dropdown (only works when trigger is `hover`), default: 150.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/hide-timeout": {"type": "number", "description": "delay time before hide a dropdown (only works when trigger is `hover`), default: 150.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/role": {"type": "'dialog' | 'grid' | 'group' | 'listbox' | 'menu' | 'navigation' | 'tooltip' | 'tree'", "options": ["dialog", "grid", "group", "listbox", "menu", "navigation", "tooltip", "tree"], "description": "the ARIA role attribute for the dropdown menu. Depending on the use case, you may want to change this to 'navigation', default: menu.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/tabindex": {"type": "number | string", "description": "[tabindex](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex) of Dropdown, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/popper-class": {"type": "string", "description": "custom class name for Dropdown's dropdown, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/popper-options": {"type": "object", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters, default: {modifiers: [{name: 'computeStyles',options: {gpuAcceleration: false}}]}.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/teleported": {"type": "boolean", "description": "whether the dropdown popup is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/persistent": {"type": "boolean", "description": "when dropdown inactive and `persistent` is `false` , dropdown menu will be destroyed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes)"}, "el-dropdown/click": {"type": "event", "description": "if `split-button` is `true`, triggers when left button is clicked\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-events)"}, "el-dropdown/command": {"type": "event", "description": "triggers when a dropdown item is clicked, the parameters is the command dispatched from the dropdown item\n\n[<PERSON>s](https://element-plus.org/en-US/component/dropdown.html#dropdown-events)"}, "el-dropdown/visible-change": {"type": "event", "description": "triggers when the dropdown appears/disappears, the param is true when it appears, and false otherwise\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-events)"}, "el-dropdown-item/command": {"type": "string | number | object", "description": "a command to be dispatched to Dropdown's `command` callback\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes)"}, "el-dropdown-item/disabled": {"type": "boolean", "description": "whether the item is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes)"}, "el-dropdown-item/divided": {"type": "boolean", "description": "whether a divider is displayed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes)"}, "el-dropdown-item/icon": {"type": "string | Component", "description": "custom icon\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes)"}, "el-empty/image": {"type": "string", "description": "image URL of empty, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/empty.html#attributes)"}, "el-empty/image-size": {"type": "number", "description": "image size (width) of empty\n\n[Docs](https://element-plus.org/en-US/component/empty.html#attributes)"}, "el-empty/description": {"type": "string", "description": "description of empty, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/empty.html#attributes)"}, "el-form/model": {"type": "Record<string, any>", "description": "Data of form component.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/rules": {"type": "FormRules", "description": "Validation rules of form.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/inline": {"type": "boolean", "description": "Whether the form is inline., default: false.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/label-position": {"type": "'left' | 'right' | 'top'", "options": ["left", "right", "top"], "description": "Position of label. If set to `'left'` or `'right'`, `label-width` prop is also required., default: right.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/label-width": {"type": "string | number", "description": "Width of label, e.g. `'50px'`. All its direct child form items will inherit this value. `auto` is supported., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/label-suffix": {"type": "string", "description": "Suffix of the label., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/hide-required-asterisk": {"type": "boolean", "description": "Whether to hide required fields should have a red asterisk (star) beside their labels., default: false.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/require-asterisk-position": {"type": "'left' | 'right'", "options": ["left", "right"], "description": "Position of asterisk., default: left.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/show-message": {"type": "boolean", "description": "Whether to show the error message., default: true.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/inline-message": {"type": "boolean", "description": "Whether to display the error message inline with the form item., default: false.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/status-icon": {"type": "boolean", "description": "Whether to display an icon indicating the validation result., default: false.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/validate-on-rule-change": {"type": "boolean", "description": "Whether to trigger validation when the `rules` prop is changed., default: true.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "Control the size of components in this form.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/disabled": {"type": "boolean", "description": "Whether to disable all components in this form. If set to `true`, it will override the `disabled` prop of the inner component., default: false.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/scroll-to-error": {"type": "boolean", "description": "When validation fails, scroll to the first error form entry., default: false.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/scroll-into-view-options": {"type": "Record<string, any> | boolean", "description": "When validation fails, it scrolls to the first error item based on the scrollIntoView option. [scrollIntoView](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView)., default: true.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-attributes)"}, "el-form/validate": {"type": "event", "description": "triggers after a form item is validated\n\n[Docs](https://element-plus.org/en-US/component/form.html#form-events)"}, "el-form-item/prop": {"type": "string | string&#91;&#93;", "description": "A key of `model`. It could be a path of the property (e.g `a.b.0` or `['a', 'b', '0']`). In the use of `validate` and `resetFields` method, the attribute is required.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/label": {"type": "string", "description": "Label text.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/label-position": {"type": "'left' | 'right' | 'top'", "options": ["left", "right", "top"], "description": "Position of item label. If set to `'left'` or `'right'`, `label-width` prop is also required. Default extend `label-postion` of `form`., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/label-width": {"type": "string | number", "description": "Width of label, e.g. `'50px'`. `'auto'` is supported., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/required": {"type": "boolean", "description": "Whether the field is required or not, will be determined by validation rules if omitted.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/rules": {"type": "Arrayable<FormItemRule>", "description": "Validation rules of form, see the [following table](#formitemrule), more advanced usage at [async-validator](https://github.com/yiminghe/async-validator).\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/error": {"type": "string", "description": "Field error message, set its value and the field will validate error and show this message immediately.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/show-message": {"type": "boolean", "description": "Whether to show the error message., default: true.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/inline-message": {"type": "string | boolean", "description": "Inline style validate message., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "Control the size of components in this form-item.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/for": {"type": "string", "description": "Same as for in native label.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-form-item/validate-status": {"type": "'' | 'error' | 'validating' | 'success'", "options": ["", "error", "validating", "success"], "description": "Validation state of formItem.\n\n[Docs](https://element-plus.org/en-US/component/form.html#formitem-attributes)"}, "el-icon/color": {"type": "string", "description": "SVG tag's fill attribute, default: inherit from color.\n\n[Docs](https://element-plus.org/en-US/component/icon.html#attributes)"}, "el-icon/size": {"type": "number | string", "description": "SVG icon size, size x size, default: inherit from font size.\n\n[Docs](https://element-plus.org/en-US/component/icon.html#attributes)"}, "el-image/src": {"type": "string", "description": "image source, same as native., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/fit": {"type": "'' | 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'", "options": ["", "fill", "contain", "cover", "none"], "description": "indicate how the image should be resized to fit its container, same as [object-fit](https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit)., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/hide-on-click-modal": {"type": "boolean", "description": "when enabling preview, use this flag to control whether clicking on backdrop can exit preview mode., default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/loading": {"type": "'eager' | 'lazy'", "options": ["eager", "lazy"], "description": "Indicates how the browser should load the image, same as [native](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attr-loading).\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/lazy": {"type": "boolean", "description": "whether to use lazy load., default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/scroll-container": {"type": "string | HTMLElement", "description": "the container to add scroll listener when using lazy load. By default, the container to add scroll listener when using lazy load.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/alt": {"type": "string", "description": "native attribute `alt`.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/referrerpolicy": {"type": "string", "description": "native attribute [referrerPolicy](https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/referrerPolicy).\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/crossorigin": {"type": "'' | 'anonymous' | 'use-credentials'", "options": ["", "anonymous"], "description": "native attribute [crossorigin](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/crossorigin).\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/preview-src-list": {"type": "string[]", "description": "allow big image preview., default: [].\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/z-index": {"type": "number", "description": "set image preview z-index.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/initial-index": {"type": "number", "description": "initial preview image index, less than the length of `url-list`., default: 0.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/close-on-press-escape": {"type": "boolean", "description": "whether the image-viewer can be closed by pressing ESC., default: true.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/preview-teleported": {"type": "boolean", "description": "whether to append image-viewer to body. A nested parent element attribute transform should have this attribute set to `true`., default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/infinite": {"type": "boolean", "description": "whether the viewer preview is infinite., default: true.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/zoom-rate": {"type": "number", "description": "the zoom rate of the image viewer zoom event., default: 1.2.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/min-scale": {"type": "number", "description": "the min scale of the image viewer zoom event., default: 0.2.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/max-scale": {"type": "number", "description": "the max scale of the image viewer zoom event., default: 7.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/show-progress": {"type": "boolean", "description": "whether to display the preview image progress content., default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-attributes)"}, "el-image/load": {"type": "event", "description": "same as native load.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-events)"}, "el-image/error": {"type": "event", "description": "same as native error.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-events)"}, "el-image/switch": {"type": "event", "description": "trigger when switching images.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-events)"}, "el-image/close": {"type": "event", "description": "trigger when clicking on close button or when `hide-on-click-modal` enabled clicking on backdrop.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-events)"}, "el-image/show": {"type": "event", "description": "trigger when the viewer displays\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-events)"}, "el-image-viewer/url-list": {"type": "string[]", "description": "preview link list., default: [].\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/z-index": {"type": "number | string", "description": "preview backdrop z-index.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/initial-index": {"type": "number", "description": "the initial preview image index, less than or equal to the length of `url-list`., default: 0.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/infinite": {"type": "boolean", "description": "whether preview is infinite., default: true.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/hide-on-click-modal": {"type": "boolean", "description": "whether user can emit close event when clicking backdrop., default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/teleported": {"type": "boolean", "description": "whether to append image itself to body. A nested parent element attribute transform should have this attribute set to `true`., default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/zoom-rate": {"type": "number", "description": "the zoom rate of the image viewer zoom event., default: 1.2.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/min-scale": {"type": "number", "description": "the min scale of the image viewer zoom event., default: 0.2.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/max-scale": {"type": "number", "description": "the max scale of the image viewer zoom event., default: 7.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/close-on-press-escape": {"type": "boolean", "description": "whether the image-viewer can be closed by pressing ESC., default: true.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/show-progress": {"type": "boolean", "description": "whether to display the preview image progress content, default: false.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-attributes)"}, "el-image-viewer/close": {"type": "event", "description": "trigger when clicking on close button or when `hide-on-click-modal` enabled clicking on backdrop.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-events)"}, "el-image-viewer/switch": {"type": "event", "description": "trigger when switching images.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-events)"}, "el-image-viewer/rotate": {"type": "event", "description": "trigger when rotating images.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image-viewer-events)"}, "el-input-number/model-value": {"type": "number | null", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/min": {"type": "number", "description": "the minimum allowed value, default: -Infinity.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/max": {"type": "number", "description": "the maximum allowed value, default: Infinity.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/step": {"type": "number", "description": "incremental step, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/step-strictly": {"type": "boolean", "description": "whether input value can only be multiple of step, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/precision": {"type": "number", "description": "precision of input value\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of the component, default: default.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/readonly": {"type": "boolean", "description": "same as `readonly` in native input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/disabled": {"type": "boolean", "description": "whether the component is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/controls": {"type": "boolean", "description": "whether to enable the control buttons, default: true.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/controls-position": {"type": "'' | 'right'", "options": ["", "right"], "description": "position of the control buttons\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/name": {"type": "string", "description": "same as `name` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/aria-label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/placeholder": {"type": "string", "description": "same as `placeholder` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/id": {"type": "string", "description": "same as `id` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/value-on-clear": {"type": "number | null | 'min' | 'max'", "options": ["min", "max"], "description": "value should be set when input box is cleared\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#attributes)"}, "el-input-number/change": {"type": "event", "description": "triggers when the value changes\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#events)"}, "el-input-number/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#events)"}, "el-input-number/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#events)"}, "el-input-tag/model-value": {"type": "string[]", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/max": {"type": "number", "description": "max number tags that can be enter\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/tag-type": {"type": "'' | 'success' | 'info' | 'warning' | 'danger'", "options": ["", "success", "info", "warning", "danger"], "description": "tag type, default: info.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/tag-effect": {"type": "'' | 'light' | 'dark' | 'plain'", "options": ["", "light", "dark", "plain"], "description": "tag effect, default: light.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/trigger": {"type": "'Enter' | 'Space'", "options": ["Enter", "Space"], "description": "the key to trigger input tag, default: Enter.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/draggable": {"type": "boolean", "description": "whether tags can be dragged, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/delimiter": {"type": "string | regex", "description": "add a tag when a delimiter is matched\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "input box size\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/save-on-blur": {"type": "boolean", "description": "whether to save the input value when the input loses focus, default: true.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/clearable": {"type": "boolean", "description": "whether to show clear button, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/disabled": {"type": "boolean", "description": "whether to disable input-tag, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/readonly": {"type": "boolean", "description": "same as `readonly` in native input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/autofocus": {"type": "boolean", "description": "same as `autofocus` in native input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/id": {"type": "string", "description": "same as `id` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/tabindex": {"type": "string | number", "description": "same as `tabindex` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/maxlength": {"type": "string | number", "description": "same as `maxlength` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/minlength": {"type": "string | number", "description": "same as `minlength` in native input\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/placeholder": {"type": "string", "description": "placeholder of input\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/autocomplete": {"type": "string", "description": "same as `autocomplete` in native input, default: off.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/aria-label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#attributes)"}, "el-input-tag/change": {"type": "event", "description": "triggers when the modelValue change\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input-tag/input": {"type": "event", "description": "triggers when the input value change\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input-tag/add-tag": {"type": "event", "description": "triggers when a tag is added\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input-tag/remove-tag": {"type": "event", "description": "triggers when a tag is removed\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input-tag/focus": {"type": "event", "description": "triggers when InputTag focuses\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input-tag/blur": {"type": "event", "description": "triggers when InputTag blurs\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input-tag/clear": {"type": "event", "description": "triggers when the clear icon is clicked\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#events)"}, "el-input/type": {"type": "'text' | 'textarea' | 'password' | 'button' | 'checkbox' | 'file' | 'number' | 'radio' | ... ", "options": ["text", "textarea", "password", "button", "checkbox", "file", "number", "radio"], "description": "type of input, default: text.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/model-value": {"type": "string | number", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/maxlength": {"type": "string | number", "description": "same as `maxlength` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/minlength": {"type": "string | number", "description": "same as `minlength` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/show-word-limit": {"type": "boolean", "description": "whether show word count, only works when `type` is 'text' or 'textarea', default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/placeholder": {"type": "string", "description": "placeholder of Input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/clearable": {"type": "boolean", "description": "whether to show clear button, only works when `type` is not 'textarea', default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/formatter": {"type": "(value: string | number) => string", "description": "specifies the format of the value presented input.(only works when `type` is 'text')\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/parser": {"type": "(value: string) => string", "description": "specifies the value extracted from formatter input.(only works when `type` is 'text')\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/show-password": {"type": "boolean", "description": "whether to show toggleable password input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/disabled": {"type": "boolean", "description": "whether Input is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of Input, works when `type` is not 'textarea'\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/prefix-icon": {"type": "string | Component", "description": "prefix icon component\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/suffix-icon": {"type": "string | Component", "description": "suffix icon component\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/rows": {"type": "number", "description": "number of rows of textarea, only works when `type` is 'textarea', default: 2.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/autosize": {"type": "boolean | { minRows?: number, maxRows?: number }", "description": "whether textarea has an adaptive height, only works when `type` is 'textarea'. Can accept an object, e.g. `{ minRows: 2, maxRows: 6 }`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/autocomplete": {"type": "string", "description": "same as `autocomplete` in native input, default: off.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/name": {"type": "string", "description": "same as `name` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/readonly": {"type": "boolean", "description": "same as `readonly` in native input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/max": {"description": "same as `max` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/min": {"description": "same as `min` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/step": {"description": "same as `step` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/resize": {"type": "'none' | 'both' | 'horizontal' | 'vertical'", "options": ["none", "both", "horizontal", "vertical"], "description": "control the resizability\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/autofocus": {"type": "boolean", "description": "same as `autofocus` in native input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/form": {"type": "string", "description": "same as `form` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/aria-label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/tabindex": {"type": "string | number", "description": "input tabindex\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/input-style": {"type": "string | CSSProperties | CSSProperties[] | string[]", "description": "the style of the input element or textarea element, default: {}.\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/input.html#attributes)"}, "el-input/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/input.html#events)"}, "el-input/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/input.html#events)"}, "el-input/change": {"type": "event", "description": "triggers when the input box loses focus or the user presses Enter, only if the modelValue has changed\n\n[Docs](https://element-plus.org/en-US/component/input.html#events)"}, "el-input/input": {"type": "event", "description": "triggers when the Input value change\n\n[Docs](https://element-plus.org/en-US/component/input.html#events)"}, "el-input/clear": {"type": "event", "description": "triggers when the Input is cleared by clicking the clear button\n\n[Docs](https://element-plus.org/en-US/component/input.html#events)"}, "el-row/gutter": {"type": "number", "description": "grid spacing, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#row-attributes)"}, "el-row/justify": {"type": "'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly'", "options": ["start", "end", "center"], "description": "horizontal alignment of flex layout, default: start.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#row-attributes)"}, "el-row/align": {"type": "'top' | 'middle' | 'bottom'", "options": ["top", "middle", "bottom"], "description": "vertical alignment of flex layout\n\n[Docs](https://element-plus.org/en-US/component/layout.html#row-attributes)"}, "el-row/tag": {"type": "string", "description": "custom element tag, default: div.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#row-attributes)"}, "el-col/span": {"type": "number", "description": "number of column the grid spans, default: 24.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/offset": {"type": "number", "description": "number of spacing on the left side of the grid, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/push": {"type": "number", "description": "number of columns that grid moves to the right, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/pull": {"type": "number", "description": "number of columns that grid moves to the left, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/xs": {"type": "number | {span?: number, offset?: number, pull?: number, push?: number}", "description": "`<768px` Responsive columns or column props object\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/sm": {"type": "number | {span?: number, offset?: number, pull?: number, push?: number}", "description": "`≥768px` Responsive columns or column props object\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/md": {"type": "number | {span?: number, offset?: number, pull?: number, push?: number}", "description": "`≥992px` Responsive columns or column props object\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/lg": {"type": "number | {span?: number, offset?: number, pull?: number, push?: number}", "description": "`≥1200px` Responsive columns or column props object\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/xl": {"type": "number | {span?: number, offset?: number, pull?: number, push?: number}", "description": "`≥1920px` Responsive columns or column props object\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-col/tag": {"type": "string", "description": "custom element tag, default: div.\n\n[Docs](https://element-plus.org/en-US/component/layout.html#col-attributes)"}, "el-link/type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'", "options": ["primary", "success", "warning", "danger", "info", "default"], "description": "type, default: default.\n\n[Docs](https://element-plus.org/en-US/component/link.html#attributes)"}, "el-link/underline": {"type": "'always' | 'hover' | 'never' | boolean", "options": ["always", "hover", "never"], "description": "when underlines should appear, default: hover.\n\n[Docs](https://element-plus.org/en-US/component/link.html#attributes)"}, "el-link/disabled": {"type": "boolean", "description": "whether the component is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/link.html#attributes)"}, "el-link/href": {"type": "string", "description": "same as native hyperlink's `href`\n\n[Docs](https://element-plus.org/en-US/component/link.html#attributes)"}, "el-link/target": {"type": "'_blank' | '_parent' | '_self' | '_top'", "options": ["_blank", "_parent", "_self", "_top"], "description": "same as native hyperlink's `target`, default: \\_self.\n\n[Docs](https://element-plus.org/en-US/component/link.html#attributes)"}, "el-link/icon": {"type": "string | Component", "description": "icon component\n\n[Docs](https://element-plus.org/en-US/component/link.html#attributes)"}, "el-mention/options": {"type": "MentionOption[]", "description": "mention options list, default: [].\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/prefix": {"type": "string | string[]", "description": "prefix character to trigger mentions. The string length must be exactly 1, default: '@'.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/split": {"type": "string", "description": "character to split mentions. The string length must be exactly 1, default: ' '.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/filter-option": {"type": "false | (pattern: string, option: MentionOption) => boolean", "description": "customize filter option logic\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/placement": {"type": "'bottom' | 'top'", "options": ["bottom", "top"], "description": "set popup placement, default: 'bottom'.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/show-arrow": {"type": "boolean", "description": "whether the dropdown panel has an arrow, default: false.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/offset": {"type": "number", "description": "offset of the dropdown panel, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/whole": {"type": "boolean", "description": "when backspace is pressed to delete, whether the mention content is deleted as a whole, default: false.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/check-is-whole": {"type": "(pattern: string, prefix: string) => boolean", "description": "when backspace is pressed to delete, check if the mention is a whole\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/loading": {"type": "boolean", "description": "whether the dropdown panel of mentions is in a loading state, default: false.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/model-value": {"type": "string", "description": "input value\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/popper-class": {"type": "string", "description": "custom class name for dropdown panel\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/popper-options": {"type": "object refer to ", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters\n\n[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/[input props]": {"description": "[Docs](https://element-plus.org/en-US/component/mention.html#attributes)"}, "el-mention/search": {"type": "event", "description": "trigger when prefix hit\n\n[Docs](https://element-plus.org/en-US/component/mention.html#events)"}, "el-mention/select": {"type": "event", "description": "trigger when user select the option\n\n[Docs](https://element-plus.org/en-US/component/mention.html#events)"}, "el-mention/[input events]": {"type": "event", "description": "[Docs](https://element-plus.org/en-US/component/mention.html#events)"}, "el-menu/mode": {"type": "'horizontal' | 'vertical'", "options": ["horizontal", "vertical"], "description": "menu display mode, default: vertical.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/collapse": {"type": "boolean", "description": "whether the menu is collapsed (available only in vertical mode), default: false.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/ellipsis": {"type": "boolean", "description": "whether the menu is ellipsis (available only in horizontal mode), default: true.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/ellipsis-icon": {"type": "string | Component", "description": "custom ellipsis icon (available only in horizontal mode and ellipsis is true)\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/popper-offset": {"type": "number", "description": "offset of the popper (effective for all submenus), default: 6.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/default-active": {"type": "string", "description": "index of active menu on page load, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/default-openeds": {"type": "string[]", "description": "array that contains indexes of currently active sub-menus, default: [].\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/unique-opened": {"type": "boolean", "description": "whether only one sub-menu can be active, default: false.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/menu-trigger": {"type": "'hover' | 'click'", "options": ["hover", "click"], "description": "how sub-menus are triggered, only works when `mode` is 'horizontal', default: hover.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/router": {"type": "boolean", "description": "whether `vue-router` mode is activated. If true, index will be used as 'path' to activate the route action. Use with `default-active` to set the active item on load., default: false.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/collapse-transition": {"type": "boolean", "description": "whether to enable the collapse transition, default: true.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/popper-effect": {"type": "'dark' | 'light' | string", "options": ["dark", "light"], "description": "Tooltip theme, built-in theme: `dark` / `light` when menu is collapsed, default: dark.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/close-on-click-outside": {"type": "boolean", "description": "optional, whether menu is collapsed when clicking outside, default: false.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/popper-class": {"type": "string", "description": "custom class name for all popup menus\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/show-timeout": {"type": "number", "description": "control timeout for all menus before showing, default: 300.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/hide-timeout": {"type": "number", "description": "control timeout for all menus before hiding, default: 300.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/background-color": {"type": "string", "description": "background color of Menu (hex format) (use `--el-menu-bg-color` in a style class instead), default: #ffffff.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/text-color": {"type": "string", "description": "text color of Menu (hex format) ( use `--el-menu-text-color` in a style class instead), default: #303133.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/active-text-color": {"type": "string", "description": "text color of currently active menu item (hex format) ( use `--el-menu-active-color` in a style class instead), default: #409eff.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/persistent": {"type": "boolean", "description": "when menu inactive and `persistent` is `false` , dropdown menu will be destroyed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-attributes)"}, "el-menu/select": {"type": "event", "description": "callback function when menu is activated\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-events)"}, "el-menu/open": {"type": "event", "description": "callback function when sub-menu expands\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-events)"}, "el-menu/close": {"type": "event", "description": "callback function when sub-menu collapses\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-events)"}, "el-sub-menu/index": {"type": "string", "description": "unique identification\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/popper-class": {"type": "string", "description": "custom class name for the popup menu\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/show-timeout": {"type": "number", "description": "timeout before showing a sub-menu(inherit `show-timeout` of the menu by default.)\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/hide-timeout": {"type": "number", "description": "timeout before hiding a sub-menu(inherit `hide-timeout` of the menu by default.)\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/disabled": {"type": "boolean", "description": "whether the sub-menu is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/teleported": {"type": "boolean", "description": "whether popup menu is teleported to the body, the default is true for the level one SubMenu, false for other SubMenus, default: undefined.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/popper-offset": {"type": "number", "description": "offset of the popper (overrides the `popper` of menu)\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/expand-close-icon": {"type": "string | Component", "description": "Icon when menu are expanded and submenu are closed, `expand-close-icon` and `expand-open-icon` need to be passed together to take effect\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/expand-open-icon": {"type": "string | Component", "description": "Icon when menu are expanded and submenu are opened, `expand-open-icon` and `expand-close-icon` need to be passed together to take effect\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/collapse-close-icon": {"type": "string | Component", "description": "Icon when menu are collapsed and submenu are closed, `collapse-close-icon` and `collapse-open-icon` need to be passed together to take effect\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-sub-menu/collapse-open-icon": {"type": "string | Component", "description": "Icon when menu are collapsed and submenu are opened, `collapse-open-icon` and `collapse-close-icon` need to be passed together to take effect\n\n[Docs](https://element-plus.org/en-US/component/menu.html#submenu-attributes)"}, "el-menu-item/index": {"type": "string | null", "description": "unique identification, default: null.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-item-attributes)"}, "el-menu-item/route": {"type": "string | object", "description": "Vue Router Route Location Parameters\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-item-attributes)"}, "el-menu-item/disabled": {"type": "boolean", "description": "whether disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-item-attributes)"}, "el-menu-item/click": {"type": "event", "description": "callback function when menu-item is clicked, the param is menu-item instance\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-item-events)"}, "el-menu-item-group/title": {"type": "string", "description": "group title\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu-item-group-attributes)"}, "el-page-header/icon": {"type": "string | Component", "description": "icon component of page header, default: Back.\n\n[Docs](https://element-plus.org/en-US/component/page-header.html#attributes)"}, "el-page-header/title": {"type": "string", "description": "main title of page header, default is Back that built-in a11y, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/page-header.html#attributes)"}, "el-page-header/content": {"type": "string", "description": "content of page header, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/page-header.html#attributes)"}, "el-page-header/back": {"type": "event", "description": "triggers when right side is clicked\n\n[Docs](https://element-plus.org/en-US/component/page-header.html#events)"}, "el-pagination/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "pagination size, default: 'default'.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/background": {"type": "boolean", "description": "whether the buttons have a background color, default: false.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/page-size": {"type": "number", "description": "item count of each page\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/default-page-size": {"type": "number", "description": "default initial value of page size, not setting is the same as setting 10\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/total": {"type": "number", "description": "total item count\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/page-count": {"type": "number", "description": "total page count. Set either `total` or `page-count` and pages will be displayed; if you need `page-sizes`, `total` is required\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/pager-count": {"type": "5 | 7 | 9 | 11 | 13 | 15 | 17 | 19 | 21", "description": "number of pagers. Pagination collapses when the total page count exceeds this value, default: 7.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/current-page": {"type": "number", "description": "current page number\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/default-current-page": {"type": "number", "description": "default initial value of current-page, not setting is the same as setting 1\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/layout": {"type": "string ", "description": "layout of Pagination, elements separated with a comma, default: prev, pager, next, jumper, ->, total.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/page-sizes": {"type": "number[]", "description": "options of item count per page, default: [10, 20, 30, 40, 50, 100].\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/append-size-to": {"type": "string", "description": "which element the size dropdown appends to\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/popper-class": {"type": "string", "description": "custom class name for the page size Select's dropdown, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/prev-text": {"type": "string", "description": "text for the prev button, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/prev-icon": {"type": "string | Component", "description": "icon for the prev button, has a lower priority than `prev-text`, default: ArrowLeft.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/next-text": {"type": "string", "description": "text for the next button, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/next-icon": {"type": "string | Component", "description": "icon for the next button, has a lower priority than `next-text`, default: ArrowRight.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/disabled": {"type": "boolean", "description": "whether Pagination is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/teleported": {"type": "boolean", "description": "whether Pagination select dropdown is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/hide-on-single-page": {"type": "boolean", "description": "whether to hide when there's only one page, default: false.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/small": {"type": "boolean", "description": "whether to use small pagination, default: false.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#attributes)"}, "el-pagination/size-change": {"type": "event", "description": "triggers when `page-size` changes\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#events)"}, "el-pagination/current-change": {"type": "event", "description": "triggers when `current-page` changes\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#events)"}, "el-pagination/change": {"type": "event", "description": "triggers when `current-page` or `page-size` changes\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#events)"}, "el-pagination/prev-click": {"type": "event", "description": "triggers when the prev button is clicked and current page changes\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#events)"}, "el-pagination/next-click": {"type": "event", "description": "triggers when the next button is clicked and current page changes\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#events)"}, "el-popconfirm/title": {"type": "string", "description": "Title\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/confirm-button-text": {"type": "string", "description": "Confirm button text\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/cancel-button-text": {"type": "string", "description": "Cancel button text\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/confirm-button-type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'", "options": ["primary", "success", "warning", "danger", "info", "text"], "description": "Confirm button type, default: primary.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/cancel-button-type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'", "options": ["primary", "success", "warning", "danger", "info", "text"], "description": "Cancel button type, default: text.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/icon": {"type": "string | Component", "description": "Icon Component, default: QuestionFilled.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/icon-color": {"type": "string", "description": "Icon color, default: #f90.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/hide-icon": {"type": "boolean", "description": "is hide Icon, default: false.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/hide-after": {"type": "number", "description": "delay of disappear, in millisecond, default: 200.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/teleported": {"type": "boolean", "description": "whether popconfirm is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/persistent": {"type": "boolean", "description": "when popconfirm inactive and `persistent` is `false` , popconfirm will be destroyed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/width": {"type": "string | number", "description": "popconfirm width, min width 150px, default: 150.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#attributes)"}, "el-popconfirm/confirm": {"type": "event", "description": "triggers when click confirm button\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#events)"}, "el-popconfirm/cancel": {"type": "event", "description": "triggers when click cancel button\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#events)"}, "el-popover/trigger": {"type": "'click' | 'focus' | 'hover' | 'contextmenu'", "options": ["click", "focus", "hover", "contextmenu"], "description": "how the popover is triggered, default: hover.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/trigger-keys": {"type": "Array", "description": "When you click the mouse to focus on the trigger element, you can define a set of keyboard codes to control the display of popover through the keyboard, default: ['Enter','Space'].\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/title": {"type": "string", "description": "popover title\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/effect": {"type": "'dark' | 'light' | string", "options": ["dark", "light"], "description": "Tooltip theme, built-in theme: `dark` / `light`, default: light.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/content": {"type": "string", "description": "popover content, can be replaced with a default `slot`, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/width": {"type": "string | number", "description": "popover width, default: 150.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "popover placement, default: bottom.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/disabled": {"type": "boolean", "description": "whether Pop<PERSON> is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/visible": {"type": "boolean | null", "description": "whether popover is visible, default: null.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/offset": {"type": "number", "description": "popover offset, `Popover` is built with `Tooltip`, offset of `Popover` is `undefined`, but offset of `Tooltip` is 12, default: undefined.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/transition": {"type": "string", "description": "popover transition animation, the default is el-fade-in-linear\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/show-arrow": {"type": "boolean", "description": "whether a tooltip arrow is displayed or not. For more info, please refer to [ElPopper](https://github.com/element-plus/element-plus/tree/dev/packages/components/popper), default: true.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/popper-options": {"type": "object", "description": "parameters for [popper.js](https://popper.js.org/docs/v2/), default: {modifiers: [{name: 'computeStyles',options: {gpuAcceleration: false}}]}.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/popper-class": {"type": "string", "description": "custom class name for popover\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/popper-style": {"type": "string | object", "description": "custom style for popover\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/show-after": {"type": "number", "description": "delay of appearance, in millisecond, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/hide-after": {"type": "number", "description": "delay of disappear, in millisecond, default: 200.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/auto-close": {"type": "number", "description": "timeout in milliseconds to hide tooltip, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/tabindex": {"type": "number | string", "description": "[tabindex](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex) of Popover, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/teleported": {"type": "boolean", "description": "whether popover dropdown is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/append-to": {"type": "CSSSelector | HTMLElement", "description": "which element the popover CONTENT appends to, default: body.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/persistent": {"type": "boolean", "description": "when popover inactive and `persistent` is `false` , popover will be destroyed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/virtual-triggering": {"type": "boolean", "description": "Indicates whether virtual triggering is enabled\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/virtual-ref": {"type": "HTMLElement", "description": "Indicates the reference element to which the popover is attached\n\n[Docs](https://element-plus.org/en-US/component/popover.html#attributes)"}, "el-popover/show": {"type": "event", "description": "triggers when popover shows\n\n[Docs](https://element-plus.org/en-US/component/popover.html#events)"}, "el-popover/before-enter": {"type": "event", "description": "triggers when the entering transition before\n\n[Docs](https://element-plus.org/en-US/component/popover.html#events)"}, "el-popover/after-enter": {"type": "event", "description": "triggers when the entering transition ends\n\n[Docs](https://element-plus.org/en-US/component/popover.html#events)"}, "el-popover/hide": {"type": "event", "description": "triggers when popover hides\n\n[Docs](https://element-plus.org/en-US/component/popover.html#events)"}, "el-popover/before-leave": {"type": "event", "description": "triggers when the leaving transition before\n\n[Docs](https://element-plus.org/en-US/component/popover.html#events)"}, "el-popover/after-leave": {"type": "event", "description": "triggers when the leaving transition ends\n\n[Docs](https://element-plus.org/en-US/component/popover.html#events)"}, "el-progress/percentage": {"description": "percentage, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/type": {"type": "'line' | 'circle' | 'dashboard'", "options": ["line", "circle", "dashboard"], "description": "the type of progress bar, default: line.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/stroke-width": {"type": "number", "description": "the width of progress bar, default: 6.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/text-inside": {"type": "boolean", "description": "whether to place the percentage inside progress bar, only works when `type` is 'line', default: false.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/status": {"type": "'success' | 'exception' | 'warning'", "options": ["success", "exception", "warning"], "description": "the current status of progress bar\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/indeterminate": {"type": "boolean", "description": "set indeterminate progress, default: false.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/duration": {"type": "number", "description": "control the animation duration of indeterminate progress or striped flow progress, default: 3.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/color": {"type": "string | (percentage: number) => string | { color: string; percentage: number }[]", "description": "background color of progress bar. Overrides `status` prop, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/width": {"type": "number", "description": "the canvas width of circle progress bar, default: 126.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/show-text": {"type": "boolean", "description": "whether to show percentage, default: true.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/stroke-linecap": {"type": "'butt' | 'round' | 'square'", "options": ["butt", "round", "square"], "description": "circle/dashboard type shape at the end path, default: round.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/format": {"type": "(percentage: number) => string", "description": "custom text format\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/striped": {"type": "boolean", "description": "stripe over the progress bar's color, default: false.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-progress/striped-flow": {"type": "boolean", "description": "get the stripes to flow, default: false.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#attributes)"}, "el-radio/model-value": {"type": "string | number | boolean", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/value": {"type": "string | number | boolean", "description": "the value of Radio\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/label": {"type": "string | number | boolean", "description": "the label of Radio. If there's no `value`, `label` will act as `value`\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/disabled": {"type": "boolean", "description": "whether Radio is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/border": {"type": "boolean", "description": "whether to add a border around Radio, default: false.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of the Radio\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/name": {"type": "string", "description": "native `name` attribute\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-attributes)"}, "el-radio/change": {"type": "event", "description": "triggers when the bound value changes\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio-events)"}, "el-radio-group/model-value": {"type": "string | number | boolean", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/size": {"type": "string", "description": "the size of radio buttons or bordered radios, default: default.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/disabled": {"type": "boolean", "description": "whether the nesting radios are disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/aria-label": {"type": "string", "description": "same as `aria-label` in RadioGroup\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/name": {"type": "string", "description": "native `name` attribute\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/id": {"type": "string", "description": "native `id` attribute\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/label": {"type": "string", "description": "same as `aria-label` in RadioGroup\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-attributes)"}, "el-radio-group/change": {"type": "event", "description": "triggers when the bound value changes\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup-events)"}, "el-radio-button/value": {"type": "string | number | boolean", "description": "the value of Radio\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton-attributes)"}, "el-radio-button/label": {"type": "string | number | boolean", "description": "the label of Radio. If there's no `value`, `label` will act as `value`\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton-attributes)"}, "el-radio-button/disabled": {"type": "boolean", "description": "whether Radio is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton-attributes)"}, "el-radio-button/name": {"type": "string", "description": "native 'name' attribute\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton-attributes)"}, "el-radio-button/text-color": {"type": "string", "description": "font color when button is active, default: #ffffff.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton-attributes)"}, "el-radio-button/fill": {"type": "string", "description": "border and background color when button is active, default: #409eff.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton-attributes)"}, "el-rate/model-value": {"type": "number", "description": "binding value, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/max": {"type": "number", "description": "max rating score, default: 5.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of Rate\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/disabled": {"type": "boolean", "description": "whether Rate is read-only, default: false.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/allow-half": {"type": "boolean", "description": "whether picking half start is allowed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/low-threshold": {"type": "number", "description": "threshold value between low and medium level. The value itself will be included in low level, default: 2.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/high-threshold": {"type": "number", "description": "threshold value between medium and high level. The value itself will be included in high level, default: 4.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/colors": {"type": "string[] | Record<number, string>", "description": "colors for icons. If array, it should have 3 elements, each of which corresponds with a score level, else if object, the key should be threshold value between two levels, and the value should be corresponding color, default: ['#f7ba2a', '#f7ba2a', '#f7ba2a'].\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/void-color": {"type": "string", "description": "color of unselected icons, default: #c6d1de.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/disabled-void-color": {"type": "string", "description": "color of unselected read-only icons, default: #eff2f7.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/icons": {"type": "string[] | Component[] | Record<number, string | Component>", "description": "icon components. If array, it should have 3 elements, each of which corresponds with a score level, else if object, the key should be threshold value between two levels, and the value should be corresponding icon component, default: [StarFilled, StarFilled, StarFilled].\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/void-icon": {"type": "string | Component", "description": "component of unselected icons, default: Star.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/disabled-void-icon": {"type": "string | Component", "description": "component of unselected read-only icons, default: StarFilled.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/show-text": {"type": "boolean", "description": "whether to display texts, default: false.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/show-score": {"type": "boolean", "description": "whether to display current score. show-score and show-text cannot be true at the same time, default: false.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/text-color": {"type": "string", "description": "color of texts, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/texts": {"type": "string[]", "description": "text array, default: ['Extremely bad', 'Disappointed', 'Fair', 'Satisfied', 'Surprise'].\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/score-template": {"type": "string", "description": "score template, default: {value}.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/clearable": {"type": "boolean", "description": "whether value can be reset to `0`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/id": {"type": "string", "description": "native `id` attribute\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/aria-label": {"type": "string", "description": "same as `aria-label` in Rate\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/label": {"type": "string", "description": "same as `aria-label` in Rate\n\n[Docs](https://element-plus.org/en-US/component/rate.html#attributes)"}, "el-rate/change": {"type": "event", "description": "Triggers when rate value is changed\n\n[Docs](https://element-plus.org/en-US/component/rate.html#events)"}, "el-result/title": {"type": "string", "description": "title of result, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/result.html#attributes)"}, "el-result/sub-title": {"type": "string", "description": "sub title of result, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/result.html#attributes)"}, "el-result/icon": {"type": "'primary'  | 'success' | 'warning' | 'info' | 'error'", "options": ["primary", "success", "warning", "info", "error"], "description": "icon type of result, default: info.\n\n[Docs](https://element-plus.org/en-US/component/result.html#attributes)"}, "el-scrollbar/height": {"type": "string | number", "description": "height of scrollbar\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/max-height": {"type": "string | number", "description": "max height of scrollbar\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/native": {"type": "boolean", "description": "whether to use the native scrollbar style, default: false.\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/wrap-style": {"type": "string | CSSProperties | CSSProperties[] | string[]", "description": "style of wrap container\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/wrap-class": {"type": "string", "description": "class of wrap container\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/view-style": {"type": "string | CSSProperties | CSSProperties[] | string[]", "description": "style of view\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/view-class": {"type": "string", "description": "class of view\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/noresize": {"type": "boolean", "description": "do not respond to container size changes, if the container size does not change, it is better to set it to optimize performance, default: false.\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/tag": {"type": "string", "description": "element tag of the view, default: div.\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/always": {"type": "boolean", "description": "always show scrollbar, default: false.\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/min-size": {"type": "number", "description": "minimum size of scrollbar, default: 20.\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/id": {"type": "string", "description": "id of view\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/role": {"type": "string", "description": "role of view\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/aria-label": {"type": "string", "description": "aria-label of view\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/aria-orientation": {"type": "'horizontal' | 'vertical'", "options": ["horizontal", "vertical"], "description": "aria-orientation of view\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/tabindex": {"type": "number | string", "description": "tabindex of wrap container\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#attributes)"}, "el-scrollbar/scroll": {"type": "event", "description": "triggers when scrolling, return distance of scrolling\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#events)"}, "el-segmented/model-value": {"type": "string | number | boolean", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/options": {"type": "Option[]", "description": "data of the options, default: [].\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/props": {"type": "object", "description": "configuration options, see the following table\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of component, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/block": {"type": "boolean", "description": "fit width of parent content\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/disabled": {"type": "boolean", "description": "whether segmented is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/name": {"type": "string", "description": "native `name` attribute\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/id": {"type": "string", "description": "native `id` attribute\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/aria-label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/direction": {"type": "'horizontal' | 'vertical'", "options": ["horizontal", "vertical"], "description": "display direction, default: horizontal.\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#attributes)"}, "el-segmented/change": {"type": "event", "description": "triggers when the selected value changes, the param is current selected value\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#events)"}, "el-virtualized-select/model-value": {"type": "string | number | boolean | object | array", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/options": {"type": "array", "description": "data of the options, the key of `value` and `label` can be customize by `props`\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/props": {"type": "object", "description": "configuration options, see the following table\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/multiple": {"type": "boolean", "description": "is multiple, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/disabled": {"type": "boolean", "description": "is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/value-key": {"type": "string", "description": "unique identity key name for value, required when value is an object, default: value.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of component, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/clearable": {"type": "boolean", "description": "whether select can be cleared, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/clear-icon": {"type": "string | Component", "description": "custom clear icon, default: CircleClose.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/collapse-tags": {"type": "boolean", "description": "whether to collapse tags to a text when multiple selecting, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/multiple-limit": {"type": "number", "description": "maximum number of options user can select when multiple is true. No limit when set to 0, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/name": {"type": "string", "description": "the name attribute of select input\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/effect": {"type": "'dark' | 'light' | string", "options": ["dark", "light"], "description": "tooltip theme, built-in theme: `dark` / `light`, default: light.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/autocomplete": {"type": "string", "description": "autocomplete of select input, default: off.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/placeholder": {"type": "string", "description": "placeholder, default: Please select.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/filterable": {"type": "boolean", "description": "is filterable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/allow-create": {"type": "boolean", "description": "whether creating new items is allowed. To use this, `filterable` must be true, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/filter-method": {"type": "() => void", "description": "custom filter method\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/loading": {"type": "boolean", "description": "whether Select is loading data from server, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/loading-text": {"type": "string", "description": "displayed text while loading data from server, default is 'Loading'\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/reserve-keyword": {"type": "boolean", "description": "whether reserve the keyword after select filtered option., default: true.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/no-match-text": {"type": "string", "description": "displayed text when no data matches the filtering query, you can also use slot `empty`, default is 'No matching data'\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/no-data-text": {"type": "string", "description": "displayed text when there is no options, you can also use slot empty, default: No Data.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/popper-class": {"type": "string", "description": "custom class name for Select's dropdown, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/teleported": {"type": "boolean", "description": "whether select dropdown is teleported, if `true` it will be teleported to where `append-to` sets, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/append-to": {"type": "CSSSelector | HTMLElement", "description": "which element the select dropdown appends to\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/persistent": {"type": "boolean", "description": "when select dropdown is inactive and `persistent` is `false`, select dropdown will be destroyed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/popper-options": {"type": "objectrefer to  doc", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters, default: {}.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/automatic-dropdown": {"type": "boolean", "description": "for non-filterable Select, this prop decides if the option menu pops up when the input is focused, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/fit-input-width": {"type": "boolean | number", "description": "whether the width of the dropdown is the same as the input, if the value is `number`, then the width is fixed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/suffix-icon": {"type": "string | Component", "description": "custom suffix icon component, default: ArrowDown.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/height": {"type": "number", "description": "The height of the dropdown panel, 34px for each item, default: 274.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/item-height": {"type": "number", "description": "The height of the dropdown item, default: 34.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/scrollbar-always-on": {"type": "boolean", "description": "Controls whether the scrollbar is always displayed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/remote": {"type": "boolean", "description": "whether search data from server, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/remote-method": {"type": "(keyword: string) => void", "description": "function that gets called when the input value changes. Its parameter is the current input value. To use this, `filterable` must be true\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/offset": {"type": "number", "description": "offset of the dropdown, default: 12.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/show-arrow": {"type": "boolean", "description": "whether the dropdown has an arrow, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "position of dropdown, default: bottom-start.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/fallback-placements": {"type": "Placement[]", "description": "list of possible positions for dropdown [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements), default: ['bottom-start', 'top-start', 'right', 'left'].\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/collapse-tags-tooltip": {"type": "boolean", "description": "whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/max-collapse-tags": {"type": "number", "description": "The max tags number to be shown. To use this, `collapse-tags` must be true, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/tag-type": {"type": "'' | 'success' | 'info' | 'warning' | 'danger'", "options": ["", "success", "info", "warning", "danger"], "description": "tag type, default: info.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/tag-effect": {"type": "'' | 'light' | 'dark' | 'plain'", "options": ["", "light", "dark", "plain"], "description": "tag effect, default: light.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/aria-label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/empty-values": {"type": "array", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/value-on-clear": {"type": "string | number | boolean | Function", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/popper-append-to-body": {"type": "boolean", "description": "whether to append the popper menu to body. If the positioning of the popper is wrong, you can try to set this prop to false, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/tabindex": {"type": "string | number", "description": "tabindex for input\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#attributes)"}, "el-virtualized-select/change": {"type": "event", "description": "triggers when the selected value changes, the param is current selected value\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#events)"}, "el-virtualized-select/visible-change": {"type": "event", "description": "triggers when the dropdown appears/disappears, the param will be true when it appears, and false otherwise\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#events)"}, "el-virtualized-select/remove-tag": {"type": "event", "description": "triggers when a tag is removed in multiple mode, the param is removed tag value\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#events)"}, "el-virtualized-select/clear": {"type": "event", "description": "triggers when the clear icon is clicked in a clearable Select\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#events)"}, "el-virtualized-select/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#events)"}, "el-virtualized-select/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#events)"}, "el-select/model-value": {"type": "string | number | boolean | object | array", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/multiple": {"type": "boolean", "description": "whether multiple-select is activated, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/disabled": {"type": "boolean", "description": "whether Select is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/value-key": {"type": "string", "description": "unique identity key name for value, required when value is an object, default: value.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of Input\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/clearable": {"type": "boolean", "description": "whether select can be cleared, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/collapse-tags": {"type": "boolean", "description": "whether to collapse tags to a text when multiple selecting, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/collapse-tags-tooltip": {"type": "boolean", "description": "whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/multiple-limit": {"type": "number", "description": "maximum number of options user can select when `multiple` is `true`. No limit when set to 0, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/name": {"type": "string", "description": "the name attribute of select input\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/effect": {"type": "'dark' | 'light' | string", "options": ["dark", "light"], "description": "tooltip theme, built-in theme: `dark` / `light`, default: light.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/autocomplete": {"type": "string", "description": "the autocomplete attribute of select input, default: off.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/placeholder": {"type": "string", "description": "placeholder, default is 'Select'\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/filterable": {"type": "boolean", "description": "whether Select is filterable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/allow-create": {"type": "boolean", "description": "whether creating new items is allowed. To use this, `filterable` must be true, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/filter-method": {"type": "() => void", "description": "custom filter method\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/remote": {"type": "boolean", "description": "whether options are loaded from server, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/remote-method": {"type": "() => void", "description": "custom remote search method\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/remote-show-suffix": {"type": "boolean", "description": "in remote search method show suffix icon, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/loading": {"type": "boolean", "description": "whether Select is loading data from server, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/loading-text": {"type": "string", "description": "displayed text while loading data from server, default is 'Loading'\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/no-match-text": {"type": "string", "description": "displayed text when no data matches the filtering query, you can also use slot `empty`, default is 'No matching data'\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/no-data-text": {"type": "string", "description": "displayed text when there is no options, you can also use slot `empty`, default is 'No data'\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/popper-class": {"type": "string", "description": "custom class name for Select's dropdown, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/reserve-keyword": {"type": "boolean", "description": "when `multiple` and `filterable` is true, whether to reserve current keyword after selecting an option, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/default-first-option": {"type": "boolean", "description": "select first matching option on enter key. Use with `filterable` or `remote`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/teleported": {"type": "boolean", "description": "whether select dropdown is teleported, if `true` it will be teleported to where `append-to` sets, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/append-to": {"type": "CSSSelector | HTMLElement", "description": "which element the select dropdown appends to\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/persistent": {"type": "boolean", "description": "when select dropdown is inactive and `persistent` is `false`, select dropdown will be destroyed, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/automatic-dropdown": {"type": "boolean", "description": "for non-filterable Select, this prop decides if the option menu pops up when the input is focused, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/clear-icon": {"type": "string | Component", "description": "custom clear icon component, default: CircleClose.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/fit-input-width": {"type": "boolean", "description": "whether the width of the dropdown is the same as the input, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/suffix-icon": {"type": "string | Component", "description": "custom suffix icon component, default: ArrowDown.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/tag-type": {"type": "'' | 'success' | 'info' | 'warning' | 'danger'", "options": ["", "success", "info", "warning", "danger"], "description": "tag type, default: info.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/tag-effect": {"type": "'' | 'light' | 'dark' | 'plain'", "options": ["", "light", "dark", "plain"], "description": "tag effect, default: light.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/offset": {"type": "number", "description": "offset of the dropdown, default: 12.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/show-arrow": {"type": "boolean", "description": "whether the dropdown has an arrow, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "position of dropdown, default: bottom-start.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/fallback-placements": {"type": "Placement[]", "description": "list of possible positions for dropdown [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements), default: ['bottom-start', 'top-start', 'right', 'left'].\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/max-collapse-tags": {"type": "number", "description": "the max tags number to be shown. To use this, `collapse-tags` must be true, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/popper-options": {"type": "objectrefer to  doc", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters, default: {}.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/aria-label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/empty-values": {"type": "array", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/value-on-clear": {"type": "string | number | boolean | Function", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/suffix-transition": {"type": "boolean", "description": "animation when dropdown appears/disappears icon, default: true.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/tabindex": {"type": "string | number", "description": "tabindex for input\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-attributes)"}, "el-select/change": {"type": "event", "description": "triggers when the selected value changes\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-select/visible-change": {"type": "event", "description": "triggers when the dropdown appears/disappears\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-select/remove-tag": {"type": "event", "description": "triggers when a tag is removed in multiple mode\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-select/clear": {"type": "event", "description": "triggers when the clear icon is clicked in a clearable Select\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-select/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-select/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-select/popup-scroll": {"type": "event", "description": "triggers when dropdown scrolls\n\n[Docs](https://element-plus.org/en-US/component/select.html#select-events)"}, "el-option-group/label": {"type": "string", "description": "name of the group\n\n[Docs](https://element-plus.org/en-US/component/select.html#option-group-attributes)"}, "el-option-group/disabled": {"type": "boolean", "description": "whether to disable all options in this group, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#option-group-attributes)"}, "el-option/value": {"type": "string | number | boolean | object", "description": "value of option\n\n[Docs](https://element-plus.org/en-US/component/select.html#option-attributes)"}, "el-option/label": {"type": "string | number", "description": "label of option, same as `value` if omitted\n\n[Docs](https://element-plus.org/en-US/component/select.html#option-attributes)"}, "el-option/disabled": {"type": "boolean", "description": "whether option is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/select.html#option-attributes)"}, "el-skeleton/animated": {"type": "boolean", "description": "whether showing the animation, default: false.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes)"}, "el-skeleton/count": {"type": "number", "description": "how many fake items to render to the DOM, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes)"}, "el-skeleton/loading": {"type": "boolean", "description": "whether showing the real DOM, default: false.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes)"}, "el-skeleton/rows": {"type": "number", "description": "numbers of the row, only useful when no template slot were given, default: 3.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes)"}, "el-skeleton/throttle": {"type": "number | { leading?: number, trailing?: number, initVal?: boolean }", "description": "rendering delay in milliseconds. Numbers represent delayed display, and can also be set to delay hide, for example `{ leading: 500, trailing: 500 }`. When needing to control the initial value of loading, you can set `{ initVal: true }`, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes)"}, "el-skeleton-item/variant": {"type": "'p' | 'text' | 'h1' | 'h3' | 'caption' | 'button' | 'image' | 'circle' | 'rect'", "options": ["p", "text", "h1", "h3", "caption", "button", "image", "circle", "rect"], "description": "the current rendering skeleton type, default: text.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeletonitem-attributes)"}, "el-slider/model-value": {"type": "number | number[]", "description": "binding value, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/min": {"type": "number", "description": "minimum value, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/max": {"type": "number", "description": "maximum value, default: 100.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/disabled": {"type": "boolean", "description": "whether <PERSON><PERSON><PERSON> is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/step": {"type": "number", "description": "step size, default: 1.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/show-input": {"type": "boolean", "description": "whether to display an input box, works when `range` is false, default: false.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/show-input-controls": {"type": "boolean", "description": "whether to display control buttons when `show-input` is true, default: true.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of the slider wrapper, will not work in vertical mode, default: default.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/input-size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of the input box, when set `size`, the default is the value of `size`, default: default.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/show-stops": {"type": "boolean", "description": "whether to display breakpoints, default: false.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/show-tooltip": {"type": "boolean", "description": "whether to display tooltip value, default: true.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/format-tooltip": {"type": "(value: number) => number | string", "description": "format to display tooltip value\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/range": {"type": "boolean", "description": "whether to select a range, default: false.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/vertical": {"type": "boolean", "description": "vertical mode, default: false.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/height": {"type": "string", "description": "slider height, required in vertical mode\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/aria-label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/range-start-label": {"type": "string", "description": "when `range` is true, screen reader label for the start of the range\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/range-end-label": {"type": "string", "description": "when `range` is true, screen reader label for the end of the range\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/format-value-text": {"type": "(value: number) => string", "description": "format to display the `aria-valuenow` attribute for screen readers\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/debounce": {"type": "number", "description": "debounce delay when typing, in milliseconds, works when `show-input` is true, default: 300.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/tooltip-class": {"type": "string", "description": "custom class name for the tooltip\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "position of Tooltip, default: top.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/marks": {"type": "SliderMarks", "description": "marks, type of key must be `number` and must in closed interval `[min, max]`, each mark can custom style\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/persistent": {"type": "boolean", "description": "when slider tooltip inactive and `persistent` is `false` , popconfirm will be destroyed. `persistent` always be `false` when `show-tooltip ` is `false`, default: true.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/label": {"type": "string", "description": "native `aria-label` attribute\n\n[Docs](https://element-plus.org/en-US/component/slider.html#attributes)"}, "el-slider/change": {"type": "event", "description": "triggers when the value changes (if the mouse is being dragged, this event only fires when the mouse is released)\n\n[Docs](https://element-plus.org/en-US/component/slider.html#events)"}, "el-slider/input": {"type": "event", "description": "triggers when the data changes (It'll be emitted in real time during sliding)\n\n[Docs](https://element-plus.org/en-US/component/slider.html#events)"}, "el-space/alignment": {"type": "'center' | 'normal' | 'stretch' | ... ", "options": ["center", "normal", "stretch"], "description": "Controls the alignment of items, default: center.\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/class": {"type": "string | object | array", "description": "className\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/direction": {"type": "'vertical' | 'horizontal'", "options": ["vertical", "horizontal"], "description": "Placement direction, default: horizontal.\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/prefix-cls": {"type": "string", "description": "Prefix for space-items\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/style": {"type": "string | CSSProperties | CSSProperties[] | string[]", "description": "Extra style rules\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/spacer": {"type": "string | number | VNode", "description": "Spacer\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/size": {"type": "'default' | 'small' | 'large' | number | [number, number]", "options": ["default", "small", "large"], "description": "Spacing size, default: small.\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/wrap": {"type": "boolean", "description": "Auto wrapping, default: false.\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/fill": {"type": "boolean", "description": "Whether to fill the container, default: false.\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-space/fill-ratio": {"type": "number", "description": "Ratio of fill, default: 100.\n\n[Docs](https://element-plus.org/en-US/component/space.html#attributes)"}, "el-steps/space": {"type": "number | string", "description": "the spacing of each step, will be responsive if omitted. Supports percentage., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-steps/direction": {"type": "'vertical' | 'horizontal'", "options": ["vertical", "horizontal"], "description": "display direction, default: horizontal.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-steps/active": {"type": "number", "description": "current activation step, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-steps/process-status": {"type": "'wait' | 'process' | 'finish' | 'error' | 'success'", "options": ["wait", "process", "finish", "error", "success"], "description": "status of current step, default: process.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-steps/finish-status": {"type": "'wait' | 'process' | 'finish' | 'error' | 'success'", "options": ["wait", "process", "finish", "error", "success"], "description": "status of end step, default: finish.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-steps/align-center": {"type": "boolean", "description": "center title and description\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-steps/simple": {"type": "boolean", "description": "whether to apply simple theme\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps-attributes)"}, "el-step/title": {"type": "string", "description": "step title, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#step-attributes)"}, "el-step/description": {"type": "string", "description": "step description, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#step-attributes)"}, "el-step/icon": {"type": "string | Component", "description": "step custom icon. Icons can be passed via named slot as well\n\n[Docs](https://element-plus.org/en-US/component/steps.html#step-attributes)"}, "el-step/status": {"type": "'' | 'wait' | 'process' | 'finish' | 'error' | 'success'", "options": ["", "wait", "process", "finish", "error", "success"], "description": "current status. It will be automatically set by Steps if not configured., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#step-attributes)"}, "el-switch/model-value": {"type": "boolean | string | number", "description": "binding value, it should be equivalent to either `active-value` or `inactive-value`, by default it's `boolean` type, default: false.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/disabled": {"type": "boolean", "description": "whether Switch is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/loading": {"type": "boolean", "description": "whether Switch is in loading state, default: false.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of Switch, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/width": {"type": "number | string", "description": "width of Switch, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/inline-prompt": {"type": "boolean", "description": "whether icon or text is displayed inside dot, only the first character will be rendered for text, default: false.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/active-icon": {"type": "string | Component", "description": "component of the icon displayed when in `on` state, overrides `active-text`\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/inactive-icon": {"type": "string | Component", "description": "component of the icon displayed when in `off` state, overrides `inactive-text`\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/active-action-icon": {"type": "string | Component", "description": "component of the icon displayed in action when in `on` state\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/inactive-action-icon": {"type": "string | Component", "description": "component of the icon displayed in action when in `off` state\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/active-text": {"type": "string", "description": "text displayed when in `on` state, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/inactive-text": {"type": "string", "description": "text displayed when in `off` state, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/active-value": {"type": "boolean | string | number", "description": "switch value when in `on` state, default: true.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/inactive-value": {"type": "boolean | string | number", "description": "switch value when in `off` state, default: false.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/name": {"type": "string", "description": "input name of Switch, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/before-change": {"type": "() => Promise<boolean> | boolen", "description": "before-change hook before the switch state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop switching\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/id": {"type": "string", "description": "id for input\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/tabindex": {"type": "string | number", "description": "tabindex for input\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/aria-label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/active-color": {"type": "string", "description": "background color when in `on` state ( use CSS var `--el-switch-on-color` instead ), default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/inactive-color": {"type": "string", "description": "background color when in `off` state ( use CSS var `--el-switch-off-color` instead ), default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/border-color": {"type": "string", "description": "border color of the switch ( use CSS var `--el-switch-border-color` instead ), default: ''.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/switch.html#attributes)"}, "el-switch/change": {"type": "event", "description": "triggers when value changes\n\n[Docs](https://element-plus.org/en-US/component/switch.html#events)"}, "el-table-v2/cache": {"type": "number", "description": "Number of rows rendered in advance to boost the performance, default: 2.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/estimated-row-height": {"type": "number", "description": "The estimated row height for rendering dynamic height rows\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/header-class": {"description": "Customized class name passed to header wrapper\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/header-props": {"description": "Customized props name passed to header component\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/header-cell-props": {"description": "Customized props name passed to header cell component\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/header-height": {"type": "number`| `number[]", "description": "The height of the header is set by `height`. If given an array, it renders header rows equal to its length, default: 50.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/footer-height": {"type": "number", "description": "The height of the footer element, when provided, will be part to the calculation of the table's height., default: 0.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/row-class": {"description": "Customized class name passed to row wrapper\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/row-key": {"type": "string` | `Symbol` | `number", "description": "The key of each row, if not provided, will be the index of the row, default: id.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/row-props": {"description": "Customized props name passed to row component\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/row-height": {"type": "number", "description": "The height of each row, used for calculating the total height of the table, default: 50.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/row-event-handlers": {"description": "A collection of handlers attached to each row\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/cell-props": {"description": "extra props passed to each cell (except header cells)\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/columns": {"type": "[Column[]]", "description": "An array of column definitions.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/data": {"type": "[Data[]]", "description": "An array of data to be rendered in the table., default: [].\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/data-getter": {"description": "A method to customize data fetch from the data source.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/fixed-data": {"description": "Data for rendering rows above the main content and below the header\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/expand-column-key": {"type": "string", "description": "The column key indicates which row is expandable\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/expanded-row-keys": {"type": "[KeyType[]]", "description": "An array of keys for expanded rows, can be used with `v-model`\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/default-expanded-row-keys": {"type": "[KeyType[]]", "description": "An array of keys for default expanded rows, **NON REACTIVE**\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/class": {"type": "string` | `array` | `object", "description": "Class name for the virtual table, will be applied to all three tables (left, right, main)\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/fixed": {"type": "boolean", "description": "Flag indicates the table column's width to be fixed or flexible., default: false.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/width": {"type": "number", "description": "Width of the table\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/height": {"type": "number", "description": "Height of the table\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/max-height": {"type": "number", "description": "Maximum height of the table\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/indent-size": {"type": "number", "description": "horizontal indentation of tree table, default: 12.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/h-scrollbar-size": {"type": "number", "description": "Indicates the horizontal scrollbar's size for the table, used to prevent the horizontal and vertical scrollbar to collapse, default: 6.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/v-scrollbar-size": {"type": "number", "description": "Indicates the vertical scrollbar's size for the table, used to prevent the horizontal and vertical scrollbar to collapse, default: 6.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/scrollbar-always-on": {"type": "boolean", "description": "If true, the scrollbar will always be shown instead of when mouse is placed above the table, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/sort-by": {"description": "Sort indicator, default: {}.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/sort-state": {"description": "Multiple sort indicator, default: undefined.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes)"}, "el-table-v2/column-sort": {"type": "event", "description": "Invoked when column sorted\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-events)"}, "el-table-v2/expanded-rows-change": {"type": "event", "description": "Invoked when expanded rows changed\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-events)"}, "el-table-v2/end-reached": {"type": "event", "description": "Invoked when the end of the table is reached. The callback contain the remain distance, it is the usually the scrollbar height.\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-events)"}, "el-table-v2/scroll": {"type": "event", "description": "Invoked after scrolling\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-events)"}, "el-table-v2/rows-rendered": {"type": "event", "description": "Invoked when rows are rendered\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-events)"}, "el-table-v2/row-expand": {"type": "event", "description": "Invoked when expand/collapse the tree node by clicking the arrow icon\n\n[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2-events)"}, "el-table/data": {"type": "any[]", "description": "table data, default: [].\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/height": {"type": "string | number", "description": "table's height. By default it has an `auto` height. If its value is a number, the height is measured in pixels; if its value is a string, the value will be assigned to element's style.height, the height is affected by external styles\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/max-height": {"type": "string | number", "description": "table's max-height. The legal value is a number or the height in px\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/stripe": {"type": "boolean", "description": "whether Table is striped, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/border": {"type": "boolean", "description": "whether Table has vertical border, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/size": {"type": "'' | 'large' | 'default' | 'small'", "options": ["", "large", "default", "small"], "description": "size of Table\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/fit": {"type": "boolean", "description": "whether width of column automatically fits its container, default: true.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/show-header": {"type": "boolean", "description": "whether Table header is visible, default: true.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/highlight-current-row": {"type": "boolean", "description": "whether current row is highlighted, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/current-row-key": {"type": "string | number", "description": "key of current row, a set only prop\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/row-class-name": {"type": "(data: { row: any, rowIndex: number }) => string | string", "description": "function that returns custom class names for a row, or a string assigning class names for every row\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/row-style": {"type": "(data: { row: any, rowIndex: number }) => CSSProperties | CSSProperties", "description": "function that returns custom style for a row, or an object assigning custom style for every row\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/cell-class-name": {"type": "(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => string | string", "description": "function that returns custom class names for a cell, or a string assigning class names for every cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/cell-style": {"type": "(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => CSSProperties | CSSProperties", "description": "function that returns custom style for a cell, or an object assigning custom style for every cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/header-row-class-name": {"type": "(data: { row: any, rowIndex: number }) => string | string", "description": "function that returns custom class names for a row in table header, or a string assigning class names for every row in table header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/header-row-style": {"type": "(data: { row: any, rowIndex: number }) => CSSProperties | CSSProperties", "description": "function that returns custom style for a row in table header, or an object assigning custom style for every row in table header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/header-cell-class-name": {"type": "(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => string | string", "description": "function that returns custom class names for a cell in table header, or a string assigning class names for every cell in table header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/header-cell-style": {"type": "(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => CSSProperties | CSSProperties", "description": "function that returns custom style for a cell in table header, or an object assigning custom style for every cell in table header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/row-key": {"type": "(row: any) => string | string", "description": "key of row data, used for optimizing rendering. Required if `reserve-selection` is on or display tree data. When its type is String, multi-level access is supported, e.g. `user.info.id`, but `user.info[0].id` is not supported, in which case `Function` should be used\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/empty-text": {"type": "string", "description": "displayed text when data is empty. You can customize this area with `#empty`, default: No Data.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/default-expand-all": {"type": "boolean", "description": "whether expand all rows by default, works when the table has a column type=\"expand\" or contains tree structure data, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/expand-row-keys": {"type": "string[]", "description": "set expanded rows by this prop, prop's value is the keys of expand rows, you should set row-key before using this prop\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/default-sort": {"type": "Sort", "description": "set the default sort column and order. property `prop` is used to set default sort column, property `order` is used to set default sort order, default: if `prop` is set, and `order` is not set, then `order` is default to ascending.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/tooltip-effect": {"type": "'dark' | 'light'", "options": ["dark", "light"], "description": "the `effect` of the overflow tooltip, default: dark.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/tooltip-options": {"type": "Pick<ElTooltipProps, 'effect' | 'enterable' | 'hideAfter' | 'offset' | 'placement' | 'popperClass' | 'popperOptions' | 'showAfter' | 'showArrow'>", "options": ["enterable", "hideAfter", "offset", "placement", "popperClass", "popperOptions", "showAfter"], "description": "the options for the overflow tooltip, [see the following tooltip component](tooltip.html#attributes), default: ^[object]`{ enterable: true, placement: 'top', showArrow: true, hideAfter: 200, popperOptions: { strategy: 'fixed' } }`.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/append-filter-panel-to": {"type": "string", "description": "which element the filter panels appends to\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/show-summary": {"type": "boolean", "description": "whether to display a summary row, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/sum-text": {"type": "string", "description": "displayed text for the first column of summary row, default: Sum.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/summary-method": {"type": "(data: { columns: any[], data: any[] }) => []", "description": "custom summary method\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/span-method": {"type": "(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => number[] | { rowspan: number, colspan: number } | void", "description": "method that returns rowspan and colspan\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/select-on-indeterminate": {"type": "boolean", "description": "controls the behavior of master checkbox in multi-select tables when only some rows are selected (but not all). If true, all rows will be selected, else deselected, default: true.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/indent": {"type": "number", "description": "horizontal indentation of tree data, default: 16.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/lazy": {"type": "boolean", "description": "whether to lazy loading data, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/load": {"type": "(row: any, treeNode: TreeNode, resolve: (data: any[]) => void) => void", "description": "method for loading child row data, only works when `lazy` is true\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/tree-props": {"type": "{ has<PERSON><PERSON><PERSON>n?: string, children?: string, checkStrictly?: boolean }", "description": "configuration for rendering nested data, default: ^[object]`{ hasChildren: 'hasChildren', children: 'children', checkStrictly: false }`.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/table-layout": {"type": "'fixed' | 'auto'", "options": ["fixed", "auto"], "description": "sets the algorithm used to lay out table cells, rows, and columns, default: fixed.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/scrollbar-always-on": {"type": "boolean", "description": "always show scrollbar, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/show-overflow-tooltip": {"type": "boolean | ", "description": "whether to hide extra content and show them in a tooltip when hovering on the cell.It will affect all the table columns, refer to table [tooltip-options](#table-attributes)\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/flexible": {"type": "boolean", "description": "ensure main axis minimum-size doesn't follow the content, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/scrollbar-tabindex": {"type": "string | number", "description": "body scrollbar's wrap container tabindex\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/allow-drag-last-column": {"type": "boolean", "description": "whether to allow drag the last column, default: true.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/tooltip-formatter": {"type": "(data: { row: any, column: any, cellValue: any }) => VNode | string", "description": "customize tooltip content when using `show-overflow-tooltip`\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/preserve-expanded-content": {"type": "boolean", "description": "whether to preserve expanded row content in DOM when collapsed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-attributes)"}, "el-table/select": {"type": "event", "description": "triggers when user clicks the checkbox in a row\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/select-all": {"type": "event", "description": "triggers when user clicks the checkbox in table header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/selection-change": {"type": "event", "description": "triggers when selection changes\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/cell-mouse-enter": {"type": "event", "description": "triggers when hovering into a cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/cell-mouse-leave": {"type": "event", "description": "triggers when hovering out of a cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/cell-click": {"type": "event", "description": "triggers when clicking a cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/cell-dblclick": {"type": "event", "description": "triggers when double clicking a cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/cell-contextmenu": {"type": "event", "description": "triggers when user right clicks on a cell\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/row-click": {"type": "event", "description": "triggers when clicking a row\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/row-contextmenu": {"type": "event", "description": "triggers when user right clicks on a row\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/row-dblclick": {"type": "event", "description": "triggers when double clicking a row\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/header-click": {"type": "event", "description": "triggers when clicking a column header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/header-contextmenu": {"type": "event", "description": "triggers when user right clicks on a column header\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/sort-change": {"type": "event", "description": "triggers when Table's sorting changes\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/filter-change": {"type": "event", "description": "column's key. If you need to use the filter-change event, this attribute is mandatory to identify which column is being filtered\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/current-change": {"type": "event", "description": "triggers when current row changes\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/header-dragend": {"type": "event", "description": "triggers after changing a column's width by dragging the column header's border\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/expand-change": {"type": "event", "description": "triggers when user expands or collapses a row (for expandable table, second param is expandedRows; for tree Table, second param is expanded)\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table/scroll": {"type": "event", "description": "Invoked after scrolled\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-events)"}, "el-table-column/type": {"type": "'default' | 'selection' | 'index' | 'expand'", "options": ["default", "selection", "index", "expand"], "description": "type of the column. If set to `selection`, the column will display checkbox. If set to `index`, the column will display index of the row (staring from 1). If set to `expand`, the column will display expand icon, default: default.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/index": {"type": "number | (index: number) => number", "description": "customize indices for each row, works on columns with `type=index`\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/label": {"type": "string", "description": "column label\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/column-key": {"type": "string", "description": "column's key. If you need to use the filter-change event, you need this attribute to identify which column is being filtered\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/prop": {"type": "string", "description": "field name. You can also use its alias: `property`\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/width": {"type": "string | number", "description": "column width, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/min-width": {"type": "string | number", "description": "column minimum width. Columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/fixed": {"type": "'left' | 'right' | boolean", "options": ["left", "right"], "description": "whether column is fixed at left / right. Will be fixed at left if `true`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/render-header": {"type": "(data: { column: any, $index: number }) => void", "description": "render function for table header of this column\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/sortable": {"type": "boolean | string", "description": "whether column can be sorted. Remote sorting can be done by setting this attribute to 'custom' and listening to the `sort-change` event of Table, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/sort-method": {"type": "<T = any>(a: T, b: T) => number", "description": "sorting method, works when `sortable` is `true`. Should return a number, just like Array.sort\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/sort-by": {"type": "(row: any, index: number) => string | string | string[]", "description": "specify which property to sort by, works when `sortable` is `true` and `sort-method` is `undefined`. If set to an Array, the column will sequentially sort by the next property if the previous one is equal\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/sort-orders": {"type": "[]", "description": "the order of the sorting strategies used when sorting the data, works when `sortable` is `true`. Accepts an array, as the user clicks on the header, the column is sorted in order of the elements in the array, default: ['ascending', 'descending', null].\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/resizable": {"type": "boolean", "description": "whether column width can be resized, works when `border` of `el-table` is `true`, default: true.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/formatter": {"type": "(row: any, column: any, cellValue: any, index: number) => VNode | string", "description": "function that formats cell content\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/show-overflow-tooltip": {"type": "boolean | ", "description": "whether to hide extra content and show them in a tooltip when hovering on the cell, default: undefined.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/align": {"type": "'left' | 'center' | 'right'", "options": ["left", "center", "right"], "description": "alignment, default: left.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/header-align": {"type": "'left' | 'center' | 'right'", "options": ["left", "center", "right"], "description": "alignment of the table header. If omitted, the value of the above `align` attribute will be applied, default: left.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/class-name": {"type": "string", "description": "class name of cells in the column\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/label-class-name": {"type": "string", "description": "class name of the label of this column\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/selectable": {"type": "(row: any, index: number) => boolean", "description": "function that determines if a certain row can be selected, works when `type` is 'selection'\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/reserve-selection": {"type": "boolean", "description": "whether to reserve selection after data refreshing, works when `type` is 'selection'. Note that `row-key` is required for this to work, default: false.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/filters": {"type": "Array<{text: string, value: string}>", "description": "an array of data filtering options. For each element in this array, `text` and `value` are required\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/filter-placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "placement for the filter dropdown\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/filter-class-name": {"type": "string", "description": "className for the filter dropdown\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/filter-multiple": {"type": "boolean", "description": "whether data filtering supports multiple options, default: true.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/filter-method": {"type": "(value: any, row: any, column: any) => void", "description": "data filtering method. If `filter-multiple` is on, this method will be called multiple times for each row, and a row will display if one of the calls returns `true`\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/filtered-value": {"type": "string[]", "description": "filter value for selected data, might be useful when table header is rendered with `render-header`\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-table-column/tooltip-formatter": {"type": "(data: { row: any, column: any, cellValue: any }) => VNode | string", "description": "customize tooltip content when using `show-overflow-tooltip`\n\n[Docs](https://element-plus.org/en-US/component/table.html#table-column-attributes)"}, "el-tabs/model-value": {"type": "string | number", "description": "binding value, name of the selected tab, the default value is the name of first tab\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/type": {"type": "'' | 'card' | 'border-card'", "options": ["", "card"], "description": "type of Tab, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/closable": {"type": "boolean", "description": "whether Tab is closable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/addable": {"type": "boolean", "description": "whether Tab is addable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/editable": {"type": "boolean", "description": "whether Tab is addable and closable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/tab-position": {"type": "'top' | 'right' | 'bottom' | 'left'", "options": ["top", "right", "bottom", "left"], "description": "position of tabs, default: top.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/stretch": {"type": "boolean", "description": "whether width of tab automatically fits its container, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/before-leave": {"type": "(activeName: TabPaneName, oldActiveName: TabPaneName) => Awaitable<void | boolean>", "description": "hook function before switching tab. If `false` is returned or a `Promise` is returned and then is rejected, switching will be prevented, default: () => true.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-attributes)"}, "el-tabs/tab-click": {"type": "event", "description": "triggers when a tab is clicked\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-events)"}, "el-tabs/tab-change": {"type": "event", "description": "triggers when `activeName` is changed\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-events)"}, "el-tabs/tab-remove": {"type": "event", "description": "triggers when tab-remove button is clicked\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-events)"}, "el-tabs/tab-add": {"type": "event", "description": "triggers when tab-add button is clicked\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-events)"}, "el-tabs/edit": {"type": "event", "description": "triggers when tab-add button or tab-remove is clicked\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs-events)"}, "el-tab-pane/label": {"type": "string", "description": "title of the tab, default: ''.\n\n[<PERSON><PERSON>](https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes)"}, "el-tab-pane/disabled": {"type": "boolean", "description": "whether Tab is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes)"}, "el-tab-pane/name": {"type": "string | number", "description": "identifier corresponding to the name of Tabs, representing the alias of the tab-pane, the default is ordinal number of the tab-pane in the sequence, e.g. the first tab-pane is '0'\n\n[Doc<PERSON>](https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes)"}, "el-tab-pane/closable": {"type": "boolean", "description": "whether Tab is closable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes)"}, "el-tab-pane/lazy": {"type": "boolean", "description": "whether Tab is lazily rendered, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes)"}, "el-tag/type": {"type": "'primary' | 'success' | 'info' | 'warning' | 'danger'", "options": ["primary", "success", "info", "warning", "danger"], "description": "type of Tag, default: primary.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/closable": {"type": "boolean", "description": "whether Tag can be removed, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/disable-transitions": {"type": "boolean", "description": "whether to disable animations, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/hit": {"type": "boolean", "description": "whether Tag has a highlighted border, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/color": {"type": "string", "description": "background color of the Tag\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of Tag\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/effect": {"type": "'dark' | 'light' | 'plain'", "options": ["dark", "light", "plain"], "description": "theme of Tag, default: light.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/round": {"type": "boolean", "description": "whether Tag is rounded, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-attributes)"}, "el-tag/click": {"type": "event", "description": "triggers when Tag is clicked\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-events)"}, "el-tag/close": {"type": "event", "description": "triggers when Tag is removed\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag-events)"}, "el-check-tag/checked": {"type": "boolean", "description": "is checked, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#checktag-attributes)"}, "el-check-tag/disabled": {"type": "boolean", "description": "whether the check-tag is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#checktag-attributes)"}, "el-check-tag/type": {"type": "'primary' | 'success' | 'info' | 'warning' | 'danger'", "options": ["primary", "success", "info", "warning", "danger"], "description": "type of CheckTag, default: primary.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#checktag-attributes)"}, "el-check-tag/change": {"type": "event", "description": "triggers when Check Tag is clicked\n\n[Docs](https://element-plus.org/en-US/component/tag.html#checktag-events)"}, "el-text/type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info'", "options": ["primary", "success", "warning", "danger", "info"], "description": "text type\n\n[Docs](https://element-plus.org/en-US/component/text.html#attributes)"}, "el-text/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "text size, default: default.\n\n[Docs](https://element-plus.org/en-US/component/text.html#attributes)"}, "el-text/truncated": {"type": "boolean", "description": "render ellipsis, default: false.\n\n[Docs](https://element-plus.org/en-US/component/text.html#attributes)"}, "el-text/line-clamp": {"type": "string | number", "description": "maximum lines\n\n[Docs](https://element-plus.org/en-US/component/text.html#attributes)"}, "el-text/tag": {"type": "string", "description": "custom element tag, default: span.\n\n[Docs](https://element-plus.org/en-US/component/text.html#attributes)"}, "el-time-picker/model-value": {"type": "number | string | Date | [Date, Date] | [number, number] | [string, string]", "description": "binding value, if it is an array, the length should be 2, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/readonly": {"type": "boolean", "description": "whether TimePicker is read only, default: false.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/disabled": {"type": "boolean", "description": "whether TimePicker is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/editable": {"type": "boolean", "description": "whether the input is editable, default: true.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/clearable": {"type": "boolean", "description": "whether to show clear button, default: true.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of Input\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/placeholder": {"type": "string", "description": "placeholder in non-range mode, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/start-placeholder": {"type": "string", "description": "placeholder for the start time in range mode\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/end-placeholder": {"type": "string", "description": "placeholder for the end time in range mode\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/is-range": {"type": "boolean", "description": "whether to pick a time range, default: false.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/arrow-control": {"type": "boolean", "description": "whether to pick time using arrow buttons, default: false.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/popper-class": {"type": "string", "description": "custom class name for TimePicker's dropdown, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/range-separator": {"type": "string", "description": "range separator, default: '-'.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/format": {"type": "string see ", "description": "format of the displayed value in the input box\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/default-value": {"type": "Date | [Date, Date]", "description": "optional, default date of the calendar\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/value-format": {"type": "string see ", "description": "optional, format of binding value. If not specified, the binding value will be a Date object\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/id": {"type": "string | [string, string]", "description": "same as `id` in native input\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/name": {"type": "string", "description": "same as `name` in native input, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/aria-label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/prefix-icon": {"type": "string | Component", "description": "Custom prefix icon component, default: Clock.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/clear-icon": {"type": "string | Component", "description": "Custom clear icon component, default: CircleClose.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/disabled-hours": {"type": "(role: string, comparingDate?: Dayjs) => number[]", "description": "To specify the array of hours that cannot be selected\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/disabled-minutes": {"type": "(hour: number, role: string, comparingDate?: Dayjs) => number[]", "description": "To specify the array of minutes that cannot be selected\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/disabled-seconds": {"type": "(hour: number, minute: number, role: string, comparingDate?: Dayjs) => number[]", "description": "To specify the array of seconds that cannot be selected\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/teleported": {"type": "boolean", "description": "whether time-picker dropdown is teleported to the body, default: true.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/tabindex": {"type": "string | number", "description": "input tabindex, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/empty-values": {"type": "array", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/value-on-clear": {"type": "string | number | boolean | Function", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/label": {"type": "string", "description": "same as `aria-label` in native input\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#attributes)"}, "el-time-picker/change": {"type": "event", "description": "triggers when user confirms the value\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#events)"}, "el-time-picker/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#events)"}, "el-time-picker/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#events)"}, "el-time-picker/clear": {"type": "event", "description": "triggers when the clear icon is clicked in a clearable TimePicker\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#events)"}, "el-time-picker/visible-change": {"type": "event", "description": "triggers when the TimePicker's dropdown appears/disappears\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#events)"}, "el-time-select/model-value": {"type": "string", "description": "binding value\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/disabled": {"type": "boolean", "description": "whether TimeSelect is disabled, default: false.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/editable": {"type": "boolean", "description": "whether the input is editable, default: true.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/clearable": {"type": "boolean", "description": "whether to show clear button, default: true.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/include-end-time": {"type": "boolean", "description": "whether `end` is included in options, default: false.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/size": {"type": "'large' | 'default' | 'small'", "options": ["large", "default", "small"], "description": "size of Input, default: default.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/placeholder": {"type": "string", "description": "placeholder in non-range mode\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/name": {"type": "string", "description": "same as `name` in native input\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/effect": {"type": "string | 'dark' | 'light'", "options": ["dark", "light"], "description": "Tooltip theme, built-in theme: `dark` / `light`, default: light.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/prefix-icon": {"type": "string | Component", "description": "custom prefix icon component, default: Clock.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/clear-icon": {"type": "string | Component", "description": "custom clear icon component, default: CircleClose.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/start": {"type": "string", "description": "start time, default: 09:00.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/end": {"type": "string", "description": "end time, default: 18:00.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/step": {"type": "string", "description": "time step, default: 00:30.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/min-time": {"type": "string", "description": "minimum time, any time before this time will be disabled\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/max-time": {"type": "string", "description": "maximum time, any time after this time will be disabled\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/format": {"type": "string see ", "description": "set format of time, default: HH:mm.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/empty-values": {"type": "array", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/value-on-clear": {"type": "string | number | boolean | Function", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#attributes)"}, "el-time-select/change": {"type": "event", "description": "triggers when user confirms the value\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#events)"}, "el-time-select/blur": {"type": "event", "description": "triggers when Input blurs\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#events)"}, "el-time-select/focus": {"type": "event", "description": "triggers when Input focuses\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#events)"}, "el-time-select/clear": {"type": "event", "description": "triggers when the clear icon is clicked in a clearable TimeSelect\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#events)"}, "el-timeline-item/timestamp": {"type": "string", "description": "timestamp content, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/hide-timestamp": {"type": "boolean", "description": "whether to show timestamp, default: false.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/center": {"type": "boolean", "description": "whether vertically centered, default: false.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/placement": {"type": "'top' | 'bottom'", "options": ["top", "bottom"], "description": "position of timestamp, default: bottom.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/type": {"type": "'primary' | 'success' | 'warning' | 'danger' | 'info'", "options": ["primary", "success", "warning", "danger", "info"], "description": "node type, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/color": {"type": "'hsl' | 'hsv' | 'hex' | 'rgb'", "options": ["hsl", "hsv", "hex", "rgb"], "description": "background color of node, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/size": {"type": "'normal' | 'large'", "options": ["normal", "large"], "description": "node size, default: normal.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/icon": {"type": "string | Component", "description": "icon component\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-timeline-item/hollow": {"type": "boolean", "description": "icon is hollow, default: false.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes)"}, "el-tooltip/append-to": {"type": "CSSSelector | HTMLElement", "description": "which element the tooltip CONTENT appends to\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/effect": {"type": "'dark' | 'light'", "options": ["dark", "light"], "description": "Tooltip theme, built-in theme: `dark` / `light`, default: dark.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/content": {"type": "string", "description": "display content, can be overridden by `slot#content`, default: ''.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/raw-content": {"type": "boolean", "description": "whether `content` is treated as HTML string, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/placement": {"type": "'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'", "options": ["top", "bottom", "left", "right"], "description": "position of Tooltip, default: bottom.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/fallback-placements": {"type": "Placement[]", "description": "list of possible positions for Tooltip [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/visible": {"type": "boolean", "description": "visibility of Tooltip\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/disabled": {"type": "boolean", "description": "whether <PERSON><PERSON><PERSON> is disabled\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/offset": {"type": "number", "description": "offset of the Tooltip, default: 12.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/transition": {"type": "string", "description": "animation name\n\n[Doc<PERSON>](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/popper-options": {"type": "objectrefer to  doc", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters, default: {}.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/arrow-offset": {"type": "number", "description": "Controls the offset (padding) of the tooltip’s arrow relative to the popper., default: 5.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/show-after": {"type": "number", "description": "delay of appearance, in millisecond, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/show-arrow": {"type": "boolean", "description": "whether the tooltip content has an arrow, default: true.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/hide-after": {"type": "number", "description": "delay of disappear, in millisecond, default: 200.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/auto-close": {"type": "number", "description": "timeout in milliseconds to hide tooltip, default: 0.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/popper-class": {"type": "string", "description": "custom class name for Tooltip's popper\n\n[<PERSON><PERSON>](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/enterable": {"type": "boolean", "description": "whether the mouse can enter the tooltip, default: true.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/teleported": {"type": "boolean", "description": "whether tooltip content is teleported, if `true` it will be teleported to where `append-to` sets, default: true.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/trigger": {"type": "'hover' | 'click' | 'focus' | 'contextmenu'", "options": ["hover", "click", "focus", "contextmenu"], "description": "How should the tooltip be triggered (to show), default: hover.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/virtual-triggering": {"type": "boolean", "description": "Indicates whether virtual triggering is enabled\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/virtual-ref": {"type": "HTMLElement", "description": "Indicates the reference element to which the tooltip is attached\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/trigger-keys": {"type": "Array", "description": "When you click the mouse to focus on the trigger element, you can define a set of keyboard codes to control the display of tooltip through the keyboard, default: ['Enter','Space'].\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/persistent": {"type": "boolean", "description": "when tooltip inactive and `persistent` is `false` , popconfirm will be destroyed\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-tooltip/aria-label": {"type": "string", "description": "same as `aria-label`\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#attributes)"}, "el-transfer/model-value": {"type": "Array<string | number>", "description": "binding value, default: [].\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/data": {"type": "Record<string, any>[]", "description": "data source, default: [].\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/filterable": {"type": "boolean", "description": "whether Transfer is filterable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/filter-placeholder": {"type": "string", "description": "placeholder for the filter input\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/filter-method": {"type": "(query: string, item: Record<string, any>) => boolean", "description": "custom filter method\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/target-order": {"type": "'original' | 'push' | 'unshift'", "options": ["original", "push", "unshift"], "description": "order strategy for elements in the target list. If set to `original`, the elements will keep the same order as the data source. If set to `push`, the newly added elements will be pushed to the bottom. If set to `unshift`, the newly added elements will be inserted on the top, default: original.\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/titles": {"type": "[string, string]", "description": "custom list titles, default: [].\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/button-texts": {"type": "[string, string]", "description": "custom button texts, default: [].\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/render-content": {"type": "renderContent", "description": "custom render function for data items\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/format": {"type": "TransferFormat", "description": "texts for checking status in list header, default: {}.\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/props": {"type": "TransferPropsAlias", "description": "prop aliases for data source\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/left-default-checked": {"type": "Array<string | number>", "description": "key array of initially checked data items of the left list, default: [].\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/right-default-checked": {"type": "Array<string | number>", "description": "key array of initially checked data items of the right list, default: [].\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/validate-event": {"type": "boolean", "description": "whether to trigger form validation, default: true.\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-attributes)"}, "el-transfer/change": {"type": "event", "description": "triggers when data items change in the right list\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-events)"}, "el-transfer/left-check-change": {"type": "event", "description": "triggers when end user changes the checked state of any data item in the left list\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-events)"}, "el-transfer/right-check-change": {"type": "event", "description": "triggers when end user changes the checked state of any data item in the right list\n\n[Docs](https://element-plus.org/en-US/component/transfer.html#transfer-events)"}, "el-own/cache-data": {"type": "CacheOption[]", "description": "The cached data of the lazy node, the structure is the same as the data, used to get the label of the unloaded data, default: [].\n\n[Docs](https://element-plus.org/en-US/component/tree-select.html#own-attributes)"}, "el-tree-v2/data": {"type": "array", "description": "tree data\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/empty-text": {"type": "string", "description": "text displayed when data is void\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/props": {"type": "object", "description": "configuration options, see the following table\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/highlight-current": {"type": "boolean", "description": "whether current node is highlighted, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/expand-on-click-node": {"type": "boolean", "description": "whether to expand or collapse node when clicking on the node, if false, then expand or collapse node only when clicking on the arrow icon., default: true.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/check-on-click-node": {"type": "boolean", "description": "whether to check or uncheck node when clicking on the node, if false, the node can only be checked or unchecked by clicking on the checkbox., default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/check-on-click-leaf": {"type": "boolean", "description": "whether to check or uncheck node when clicking on leaf node (last children)., default: true.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/default-expanded-keys": {"type": "array", "description": "array of keys of initially expanded nodes\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/show-checkbox": {"type": "boolean", "description": "whether node is selectable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/check-strictly": {"type": "boolean", "description": "whether checked state of a node not affects its father and child nodes when `show-checkbox` is `true`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/default-checked-keys": {"type": "array", "description": "array of keys of initially checked nodes\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/current-node-key": {"type": "string | number", "description": "key of initially selected node\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/filter-method": {"type": "Function", "description": "this function will be executed on each node when use filter method. if return `false`, tree node will be hidden.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/indent": {"type": "number", "description": "horizontal indentation of nodes in adjacent levels in pixels, default: 16.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/icon": {"type": "string | Component", "description": "custom tree node icon\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/item-size": {"type": "number", "description": "custom tree node height, default: 26.\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes)"}, "el-tree-v2/node-click": {"type": "event", "description": "triggers when a node is clicked\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/node-drop": {"type": "event", "description": "triggers when drag someting and drop on a node\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/node-contextmenu": {"type": "event", "description": "triggers when a node is clicked by right button\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/check-change": {"type": "event", "description": "triggers when the selected state of the node changes\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/check": {"type": "event", "description": "triggers after clicking the checkbox of a node\n\n[<PERSON><PERSON>](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/current-change": {"type": "event", "description": "triggers when current node changes\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/node-expand": {"type": "event", "description": "triggers when current node open\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree-v2/node-collapse": {"type": "event", "description": "triggers when current node close\n\n[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2-events)"}, "el-tree/data": {"type": "Array<{[key: string]: any}>", "description": "tree data\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/empty-text": {"type": "string", "description": "text displayed when data is void\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/node-key": {"type": "string", "description": "unique identity key name for nodes, its value should be unique across the whole tree\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/props": {"type": "[props]", "description": "configuration options, see the following table\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/render-after-expand": {"type": "boolean", "description": "whether to render child nodes only after a parent node is expanded for the first time, default: true.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/load": {"type": "(node, resolve, reject) => void", "description": "method for loading subtree data, only works when `lazy` is true\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/render-content": {"type": "(h, { node, data, store }) => void", "description": "render function for tree node\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/highlight-current": {"type": "boolean", "description": "whether current node is highlighted, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/default-expand-all": {"type": "boolean", "description": "whether to expand all nodes by default, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/expand-on-click-node": {"type": "boolean", "description": "whether to expand or collapse node when clicking on the node, if false, then expand or collapse node only when clicking on the arrow icon., default: true.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/check-on-click-node": {"type": "boolean", "description": "whether to check or uncheck node when clicking on the node, if false, the node can only be checked or unchecked by clicking on the checkbox., default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/check-on-click-leaf": {"type": "boolean", "description": "whether to check or uncheck node when clicking on leaf node (last children)., default: true.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/auto-expand-parent": {"type": "boolean", "description": "whether to expand father node when a child node is expanded, default: true.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/default-expanded-keys": {"type": "Array<string | number>", "description": "array of keys of initially expanded nodes\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/show-checkbox": {"type": "boolean", "description": "whether node is selectable, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/check-strictly": {"type": "boolean", "description": "whether checked state of a node not affects its father and child nodes when `show-checkbox` is `true`, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/default-checked-keys": {"type": "Array<string | number>", "description": "array of keys of initially checked nodes\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/current-node-key": {"type": "string | number", "description": "key of initially selected node\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/filter-node-method": {"type": "(value, data, node) => boolean", "description": "this function will be executed on each node when use filter method. if return `false`, tree node will be hidden.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/accordion": {"type": "boolean", "description": "whether only one node among the same level can be expanded at one time, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/indent": {"type": "number", "description": "horizontal indentation of nodes in adjacent levels in pixels, default: 18.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/icon": {"type": "string | Component", "description": "custom tree node icon component\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/lazy": {"type": "boolean", "description": "whether to lazy load leaf node, used with `load` attribute, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/draggable": {"type": "boolean", "description": "whether enable tree nodes drag and drop, default: false.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/allow-drag": {"type": "(node) => boolean", "description": "this function will be executed before dragging a node. If `false` is returned, the node can not be dragged\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/allow-drop": {"type": "(draggingNode, dropNode, type) => boolean", "description": "this function will be executed before the dragging node is dropped. If `false` is returned, the dragging node can not be dropped at the target node. `type` has three possible values: 'prev' (inserting the dragging node before the target node), 'inner' (inserting the dragging node to the target node) and 'next' (inserting the dragging node after the target node)\n\n[Docs](https://element-plus.org/en-US/component/tree.html#attributes)"}, "el-tree/node-click": {"type": "event", "description": "triggers when a node is clicked\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-contextmenu": {"type": "event", "description": "triggers when a node is clicked by right button\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/check-change": {"type": "event", "description": "triggers when the selected state of the node changes\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/check": {"type": "event", "description": "triggers after clicking the checkbox of a node\n\n[<PERSON><PERSON>](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/current-change": {"type": "event", "description": "triggers when current node changes\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-expand": {"type": "event", "description": "triggers when current node open\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-collapse": {"type": "event", "description": "triggers when current node close\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-drag-start": {"type": "event", "description": "triggers when dragging starts\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-drag-enter": {"type": "event", "description": "triggers when the dragging node enters another node\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-drag-leave": {"type": "event", "description": "triggers when the dragging node leaves a node\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-drag-over": {"type": "event", "description": "triggers when dragging over a node (like mouseover event)\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-drag-end": {"type": "event", "description": "triggers when dragging ends\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-tree/node-drop": {"type": "event", "description": "triggers after the dragging node is dropped\n\n[Docs](https://element-plus.org/en-US/component/tree.html#events)"}, "el-upload/action": {"type": "string", "description": "request URL., default: #.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/headers": {"type": "Headers | Record<string, any>", "description": "request headers.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/method": {"type": "string", "description": "set upload request method., default: post.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/multiple": {"type": "boolean", "description": "whether uploading multiple files is permitted., default: false.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/data": {"type": "Record<string, any> | Awaitable<Record<string, any>> | (rawFile: UploadRawFile) => Awaitable<Record<string, any>>", "description": "additions options of request. support `Awaitable` data and `Function` since v2.3.13., default: {}.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/name": {"type": "string", "description": "key name for uploaded file., default: file.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/with-credentials": {"type": "boolean", "description": "whether cookies are sent., default: false.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/show-file-list": {"type": "boolean", "description": "whether to show the uploaded file list., default: true.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/drag": {"type": "boolean", "description": "whether to activate drag and drop mode., default: false.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/accept": {"type": "string", "description": "accepted [file types](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-accept), will not work when `thumbnail-mode === true`., default: ''.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/crossorigin": {"type": "'' | 'anonymous' | 'use-credentials'", "options": ["", "anonymous"], "description": "native attribute [crossorigin](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/crossorigin).\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-preview": {"type": "(uploadFile: UploadFile) => void", "description": "hook function when clicking the uploaded files.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-remove": {"type": "(uploadFile: UploadFile, uploadFiles: UploadFiles) => void", "description": "hook function when files are removed.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-success": {"type": "(response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => void", "description": "hook function when uploaded successfully.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-error": {"type": "(error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) => void", "description": "hook function when some errors occurs.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-progress": {"type": "(evt: UploadProgressEvent, uploadFile: UploadFile, uploadFiles: UploadFiles) => void", "description": "hook function when some progress occurs.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-change": {"type": "(uploadFile: UploadFile, uploadFiles: UploadFiles) => void", "description": "hook function when select file or upload file success or upload file fail.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/on-exceed": {"type": "(files: File[], uploadFiles: UploadUserFile[]) => void", "description": "hook function when limit is exceeded.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/before-upload": {"type": "(rawFile: UploadRawFile) => Awaitable<void | undefined | null | boolean | File | Blob>", "description": "hook function before uploading with the file to be uploaded as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, uploading will be aborted.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/before-remove": {"type": "(uploadFile: UploadFile, uploadFiles: UploadFiles) => Awaitable<boolean>", "description": "hook function before removing a file with the file and file list as its parameters. If `false` is returned or a `Promise` is returned and then is rejected, removing will be aborted.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/file-list": {"type": "UploadUserFile[]", "description": "default uploaded files., default: [].\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/list-type": {"type": "'text' | 'picture' | 'picture-card'", "options": ["text", "picture"], "description": "type of file list., default: text.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/auto-upload": {"type": "boolean", "description": "whether to auto upload file., default: true.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/http-request": {"type": "(options: UploadRequestOptions) => XMLHttpRequest | Promise<unknown>", "description": "override default xhr behavior, allowing you to implement your own upload-file's request., default: ajaxUpload [see](https://github.com/element-plus/element-plus/blob/dev/packages/components/upload/src/ajax.ts#L55).\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/disabled": {"type": "boolean", "description": "whether to disable upload., default: false.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-upload/limit": {"type": "number", "description": "maximum number of uploads allowed.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#attributes)"}, "el-watermark/width": {"type": "number", "description": "The width of the watermark, the default value of `content` is its own width, default: 120.\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/height": {"type": "number", "description": "The height of the watermark, the default value of `content` is its own height, default: 64.\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/rotate": {"type": "number", "description": "When the watermark is drawn, the rotation Angle, unit `°`, default: -22.\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/z-index": {"type": "number", "description": "The z-index of the appended watermark element, default: 9.\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/image": {"type": "string", "description": "Image source, it is recommended to export 2x or 3x image, high priority\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/content": {"type": "string|string[]", "description": "Watermark text content\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/font": {"type": "[Font]", "description": "Text style, default: [Font](#font).\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/gap": {"type": "[number, number]", "description": "The spacing between watermarks, default: \\[100, 100\\].\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}, "el-watermark/offset": {"type": "[number, number]", "description": "The offset of the watermark from the upper left corner of the container. The default is `gap/2`, default: \\[gap\\[0\\]/2, gap\\[1\\]/2\\].\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#attributes)"}}