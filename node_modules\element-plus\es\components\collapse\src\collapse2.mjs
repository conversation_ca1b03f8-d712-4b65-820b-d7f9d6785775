import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';
import { mutable } from '../../../utils/typescript.mjs';
import { isNumber } from '../../../utils/types.mjs';
import { isString, isArray } from '@vue/shared';
import { UPDATE_MODEL_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';

const emitChangeFn = (value) => isNumber(value) || isString(value) || isArray(value);
const collapseProps = buildProps({
  accordion: Boolean,
  modelValue: {
    type: definePropType([Array, String, Number]),
    default: () => mutable([])
  },
  expandIconPosition: {
    type: definePropType([String]),
    default: "right"
  },
  beforeCollapse: {
    type: definePropType(Function)
  }
});
const collapseEmits = {
  [UPDATE_MODEL_EVENT]: emitChangeFn,
  [CHANGE_EVENT]: emitChangeFn
};

export { collapseEmits, collapseProps, emitChangeFn };
//# sourceMappingURL=collapse2.mjs.map
