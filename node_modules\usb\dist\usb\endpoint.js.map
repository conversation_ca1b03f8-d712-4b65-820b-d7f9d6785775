{"version": 3, "file": "endpoint.js", "sourceRoot": "", "sources": ["../../tsc/usb/endpoint.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,yCAA0F;AAE1F,+BAAiC;AAEjC,MAAM,QAAQ,GAAG,CAAC,GAAyB,EAAqB,EAAE,CAAC,GAAG,IAAI,GAAG,YAAY,UAAU,CAAC;AAEpG,kDAAkD;AAClD,MAAsB,QAAS,SAAQ,qBAAY;IAe/C,YAAsB,MAAc,EAAE,UAA8B;QAChE,KAAK,EAAE,CAAC;QADU,WAAM,GAAN,MAAM,CAAQ;QANpC,8GAA8G;QACvG,YAAO,GAAG,CAAC,CAAC;QAOf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,gBAAgB,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;IACvD,CAAC;IAED,wDAAwD;IACjD,SAAS,CAAC,QAAsD;QACnE,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CAAC,OAAe,EAAE,QAA4F;QAC7H,OAAO,IAAI,mBAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACzF,CAAC;CACJ;AAvCD,4BAuCC;AAED,iEAAiE;AACjE,MAAa,UAAW,SAAQ,QAAQ;IAYpC,YAAY,MAAc,EAAE,UAA8B;QACtD,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAX9B,0BAA0B;QACnB,cAAS,GAAiB,IAAI,CAAC;QAE5B,kBAAa,GAAe,EAAE,CAAC;QAC/B,qBAAgB,GAAG,CAAC,CAAC;QACrB,gBAAW,GAAG,CAAC,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QAMtB,IAAI,CAAC,aAAa,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;OAUG;IACI,QAAQ,CAAC,MAAc,EAAE,QAAqE;QACjG,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEpC,MAAM,EAAE,GAAG,CAAC,KAAkC,EAAE,OAAgB,EAAE,YAAqB,EAAE,EAAE;YACvF,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC;QAEF,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAoB,CAAC,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG;IACI,SAAS,CAAC,UAAmB,EAAE,YAAqB,EAAE,QAAiH;QAC1K,MAAM,YAAY,GAAG,CAAC,KAAkC,EAAE,QAAkB,EAAE,MAAc,EAAE,YAAoB,EAAE,EAAE;YAClH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;YACrD,CAAC;iBAAM,IAAI,KAAK,CAAC,KAAK,KAAK,oCAAyB,EAAE,CAAC;gBACnD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpB,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,WAAW,EAAE,CAAC;gBAEnB,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE,CAAC;oBACzB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;oBACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;oBACxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACjB,IAAI,QAAQ,EAAE,CAAC;wBACX,MAAM,SAAS,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,KAAK,MAAK,oCAAyB,CAAC;wBAC7D,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;oBAC7E,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,aAAa,GAAG,CAAC,QAAkB,EAAE,EAAE;YACzC,IAAI,CAAC;gBACD,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;oBACjF,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;gBACxD,CAAC,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpB,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,YAAY,EAAE,UAA0B,KAAK,EAAE,MAAM,EAAE,YAAY;YACxH,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAES,kBAAkB,CAAC,UAAU,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,QAA4F;QACpL,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAErB,MAAM,SAAS,GAAe,EAAE,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAChD,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QAC5B,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CAAC,QAAqB;QACjC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,IAAI,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,QAAQ;YAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC7C,CAAC;CACJ;AA5ID,gCA4IC;AAED,kEAAkE;AAClE,MAAa,WAAY,SAAQ,QAAQ;IAOrC,YAAmB,MAAc,EAAE,UAA8B;QAC7D,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAN9B,0BAA0B;QACnB,cAAS,GAAiB,KAAK,CAAC;QAMnC,IAAI,CAAC,aAAa,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;OAUG;IACI,QAAQ,CAAC,MAAc,EAAE,QAAuE;QACnG,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;QAED,MAAM,EAAE,GAAG,CAAC,KAAkC,EAAE,OAAgB,EAAE,MAAe,EAAE,EAAE;YACjF,IAAI,QAAQ,EAAE,CAAC;gBACX,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAoB,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,eAAe,CAAC,MAAc,EAAE,QAAsD;QACzF,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,KAAK,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;CACJ;AArDD,kCAqDC"}