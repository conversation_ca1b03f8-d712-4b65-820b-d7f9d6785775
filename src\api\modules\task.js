import instance from '../../utils/axios.js'

// 任务相关API
const taskApi = {
  // 获取运行中的任务列表
  getRunningTasks: (pageSize = 0, pageNum = 10) => 
    instance.get(`/task/getRunningTasks?pageSize=${pageSize}&pageNum=${pageNum}`),
  
  // 停止指定任务
  stopTask: (taskId) => instance.get(`/task/stopTask?taskId=${taskId}`),
  
  // 获取任务日志列表
  getLogsList: (id) => instance.get(`/task/logsList?id=${id}`),
  
  // 获取任务日志消息
  getLogsMsg: (id) => instance.get(`/task/logsMsg?id=${id}`),
  
  // 获取任务详细日志列表
  getLogsDetailList: (taskId, shopId) => instance.get(`/task/logsDetailList/${taskId}/${shopId}`),
  
  // 下载日志文件
  downloadLogs: (fileName) => instance.get(`/task/downloadLogs/${fileName}`, { responseType: 'blob' }),
}; 

// 导出模块
export { taskApi }; 