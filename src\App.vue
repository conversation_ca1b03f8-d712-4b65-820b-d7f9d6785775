<script setup>
	// This starter template is using Vue 3 <script setup> SFCs
	// Check out https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup
	import { computed } from 'vue'
	import { useRoute } from 'vue-router'
	import Layout from '@/layout/Index.vue'
	// 初始化 route
	const route = useRoute()
	// 根据路由元信息判断是否需要布局
	const useLayout = computed(() => !route.meta.noLayout)
</script>

<template>
	<Layout v-if="useLayout">
	    <router-view />
	</Layout>
	<router-view v-else />
</template>

<style>
	#app {
		font-family: Avenir, Helvetica, Arial, sans-serif;
		/* 抗锯齿 */
		-webkit-font-smoothing: antialiased;
		/* 抗锯齿 */
		-moz-osx-font-smoothing: grayscale;
		text-align: center;
		color: #2c3e50;
	}

	body {
		margin: 0;
	}
</style>