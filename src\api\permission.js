import axios from '@/utils/axios'

/**
 * 获取当前用户权限树
 */
export function getUserPermissionTree() {
  return axios({
    url: '/admin/permission/user/tree',
    method: 'get'
  })
}

/**
 * 获取当前用户权限编码列表
 */
export function getUserPermissionCodes() {
  return axios({
    url: '/admin/permission/user/codes',
    method: 'get'
  })
}

/**
 * 获取权限树（管理用）
 */
export function getPermissionTree() {
  return axios({
    url: '/admin/permission/tree',
    method: 'get'
  })
}

/**
 * 添加权限
 */
export function addPermission(data) {
  return axios({
    url: '/admin/permission/add',
    method: 'post',
    data
  })
}

/**
 * 更新权限
 */
export function updatePermission(data) {
  return axios({
    url: '/admin/permission/update',
    method: 'put',
    data
  })
}

/**
 * 删除权限
 */
export function deletePermission(id) {
  return axios({
    url: `/admin/permission/delete/${id}`,
    method: 'delete'
  })
} 
