/* 圆形按钮样式 */
.el-button.btn-circle {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  line-height: 1;
  transition: all 0.3s ease;
}

/* 不同尺寸的圆形按钮 */
.el-button.btn-circle.el-button--small {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.el-button.btn-circle.el-button--large {
  width: 48px;
  height: 48px;
  font-size: 18px;
}

/* 圆形刷新按钮特定样式 */
.el-button.btn-refresh {
  background-color: #f2f6fc;
  border-color: #dcdfe6;
  color: #409eff;
}

.el-button.btn-refresh:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  transform: rotate(30deg);
}

.el-button.btn-refresh:active {
  background-color: #d9ecff;
  border-color: #a0cfff;
}

/* 刷新按钮加载状态 */
.el-button.btn-refresh.is-loading {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

/* 刷新图标旋转动画 */
.el-button.btn-refresh .el-icon {
  transition: transform 0.3s ease;
}

.el-button.btn-refresh:hover .el-icon {
  transform: rotate(180deg);
}

/* 全局操作栏样式 */
.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left {
  display: flex;
  gap: 10px;
}

.action-right {
  display: flex;
  gap: 10px;
  margin-left: auto; /* 确保右侧元素靠右对齐 */
} 