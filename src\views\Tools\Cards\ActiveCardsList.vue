<template>
  <div class="active-cards-container">
    <!-- 操作按钮 -->
    <ActionBar @refresh="refreshData"/>

    <!-- 数据表格 -->
    <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%;"
        v-loading="loading"
    >
      <el-table-column label="状态" width="80" align="center">
        <template #default>
          <div class="status-indicator active"></div>
        </template>
      </el-table-column>
      <el-table-column prop="cardId" label="卡密账号" min-width="180"/>
      <el-table-column prop="cardSecret" label="卡密密码" min-width="180"/>
      <el-table-column prop="status" label="使用状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="effectiveDays" label="有效期" width="100">
        <template #default="{ row }">
          {{ row.effectiveDays }}天
        </template>
      </el-table-column>
      <el-table-column prop="activateTime" label="激活时间" min-width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.activateTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="expireTime" label="过期时间" min-width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.expireTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="note" label="备注" min-width="120"/>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted} from 'vue'
import {ElMessage} from 'element-plus'
import ActionBar from '@/components/ActionBar.vue'
import { cardsApi } from '@/api/modules/cards'

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页配置
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 初始化
onMounted(() => {
  fetchData()
})

// 获取表格数据
const fetchData = async () => {
  loading.value = true
  try {
    const res = await cardsApi.getActiveCardsPage(pagination)

    if (res.code === 200) {
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

// 页码变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 获取状态类型
const getStatusType = (status) => {
  const map = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'info'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const map = {
    0: '未激活',
    1: '未使用',
    2: '已使用',
    3: '已冻结',
    4: '已过期'
  }
  return map[status] || '未知'
}

// 格式化时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toLocaleString()
}
</script>

<style scoped>
.active-cards-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 40px);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 auto;
}

.status-indicator.active {
  background-color: #67c23a;
  box-shadow: 0 0 6px rgba(103, 194, 58, 0.5);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.el-table {
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.el-table ::v-deep(.el-table__header) th {
  background-color: #f8f8f9;
}
</style>