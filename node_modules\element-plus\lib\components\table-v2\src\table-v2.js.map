{"version": 3, "file": "table-v2.js", "sources": ["../../../../../../packages/components/table-v2/src/table-v2.tsx"], "sourcesContent": ["import { defineComponent, provide, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useTable } from './use-table'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2Props } from './table'\n// renderers\nimport MainTable from './renderers/main-table'\nimport LeftTable from './renderers/left-table'\nimport RightTable from './renderers/right-table'\nimport Row from './renderers/row'\nimport Cell from './renderers/cell'\nimport Header from './renderers/header'\nimport HeaderCell from './renderers/header-cell'\nimport Footer from './renderers/footer'\nimport Empty from './renderers/empty'\nimport Overlay from './renderers/overlay'\n\nimport type { CSSProperties } from 'vue'\nimport type { TableGridRowSlotParams } from './table-grid'\nimport type { ScrollStrategy } from './composables/use-scrollbar'\nimport type {\n  TableV2HeaderRendererParams,\n  TableV2HeaderRowCellRendererParams,\n  TableV2RowCellRenderParam,\n} from './components'\nimport type { KeyType } from './types'\n\nconst COMPONENT_NAME = 'ElTableV2'\n\nconst TableV2 = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2Props,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n\n    const {\n      columnsStyles,\n      fixedColumnsOnLeft,\n      fixedColumnsOnRight,\n      mainColumns,\n      mainTableHeight,\n      fixedTableHeight,\n      leftTableWidth,\n      rightTableWidth,\n      data,\n      depthMap,\n      expandedRowKeys,\n      hasFixedColumns,\n      mainTableRef,\n      leftTableRef,\n      rightTableRef,\n      isDynamic,\n      isResetting,\n      isScrolling,\n\n      bodyWidth,\n      emptyStyle,\n      rootStyle,\n      footerHeight,\n\n      showEmpty,\n\n      // exposes\n      scrollTo,\n      scrollToLeft,\n      scrollToTop,\n      scrollToRow,\n\n      getRowHeight,\n      onColumnSorted,\n      onRowHeightChange,\n      onRowHovered,\n      onRowExpanded,\n      onRowsRendered,\n      onScroll,\n      onVerticalScroll,\n    } = useTable(props)\n\n    expose({\n      /**\n       * @description scroll to a given position\n       * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n       */\n      scrollTo,\n      /**\n       * @description scroll to a given position horizontally\n       * @params scrollLeft {Number} where to scroll to.\n       */\n      scrollToLeft,\n      /**\n       * @description scroll to a given position vertically\n       * @params scrollTop { Number } where to scroll to.\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n    })\n\n    provide(TableV2InjectionKey, {\n      ns,\n      isResetting,\n      isScrolling,\n    })\n\n    return () => {\n      const {\n        cache,\n        cellProps,\n        estimatedRowHeight,\n        expandColumnKey,\n        fixedData,\n        headerHeight,\n        headerClass,\n        headerProps,\n        headerCellProps,\n        sortBy,\n        sortState,\n        rowHeight,\n        rowClass,\n        rowEventHandlers,\n        rowKey,\n        rowProps,\n        scrollbarAlwaysOn,\n        indentSize,\n        iconSize,\n        useIsScrolling,\n        vScrollbarSize,\n        width,\n      } = props\n\n      const _data = unref(data)\n\n      const mainTableProps = {\n        cache,\n        class: ns.e('main'),\n        columns: unref(mainColumns),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        bodyWidth: unref(bodyWidth),\n        headerHeight,\n        headerWidth: unref(bodyWidth),\n        height: unref(mainTableHeight),\n        mainTableRef,\n        rowKey,\n        rowHeight,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width,\n        getRowHeight,\n        onRowsRendered,\n        onScroll,\n      }\n\n      const leftColumnsWidth = unref(leftTableWidth)\n      const _fixedTableHeight = unref(fixedTableHeight)\n\n      const leftTableProps = {\n        cache,\n        class: ns.e('left'),\n        columns: unref(fixedColumnsOnLeft),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        leftTableRef,\n        rowHeight,\n        bodyWidth: leftColumnsWidth,\n        headerWidth: leftColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width: leftColumnsWidth,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n\n      const rightColumnsWidth = unref(rightTableWidth)\n\n      const rightTableProps = {\n        cache,\n        class: ns.e('right'),\n        columns: unref(fixedColumnsOnRight),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        rightTableRef,\n        rowHeight,\n        bodyWidth: rightColumnsWidth,\n        headerWidth: rightColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        width: rightColumnsWidth,\n        style: `--${unref(\n          ns.namespace\n        )}-table-scrollbar-size: ${vScrollbarSize}px` as unknown as CSSProperties,\n        useIsScrolling,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n      const _columnsStyles = unref(columnsStyles)\n\n      const tableRowProps = {\n        ns,\n        depthMap: unref(depthMap),\n        columnsStyles: _columnsStyles,\n        expandColumnKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        estimatedRowHeight,\n        hasFixedColumns: unref(hasFixedColumns),\n        rowProps,\n        rowClass,\n        rowKey,\n        rowEventHandlers,\n        onRowHovered,\n        onRowExpanded,\n        onRowHeightChange,\n      }\n\n      const tableCellProps = {\n        cellProps,\n        expandColumnKey,\n        indentSize,\n        iconSize,\n        rowKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        ns,\n      }\n\n      const tableHeaderProps = {\n        ns,\n        headerClass,\n        headerProps,\n        columnsStyles: _columnsStyles,\n      }\n\n      const tableHeaderCellProps = {\n        ns,\n\n        sortBy,\n        sortState,\n        headerCellProps,\n        onColumnSorted,\n      }\n\n      const tableSlots = {\n        row: (props: TableGridRowSlotParams) => (\n          <Row {...props} {...tableRowProps}>\n            {{\n              row: slots.row,\n              cell: (props: TableV2RowCellRenderParam) =>\n                slots.cell ? (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  >\n                    {slots.cell(props)}\n                  </Cell>\n                ) : (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  />\n                ),\n            }}\n          </Row>\n        ),\n        header: (props: TableV2HeaderRendererParams) => (\n          <Header {...props} {...tableHeaderProps}>\n            {{\n              header: slots.header,\n              cell: (props: TableV2HeaderRowCellRendererParams) =>\n                slots['header-cell'] ? (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  >\n                    {slots['header-cell'](props)}\n                  </HeaderCell>\n                ) : (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  />\n                ),\n            }}\n          </Header>\n        ),\n      }\n\n      const rootKls = [\n        props.class,\n        ns.b(),\n        ns.e('root'),\n        {\n          [ns.is('dynamic')]: unref(isDynamic),\n        },\n      ]\n\n      const footerProps = {\n        class: ns.e('footer'),\n        style: unref(footerHeight),\n      }\n\n      return (\n        <div class={rootKls} style={unref(rootStyle)}>\n          <MainTable {...mainTableProps}>{tableSlots}</MainTable>\n          <LeftTable {...leftTableProps}>{tableSlots}</LeftTable>\n          <RightTable {...rightTableProps}>{tableSlots}</RightTable>\n          {slots.footer && (\n            <Footer {...footerProps}>{{ default: slots.footer }}</Footer>\n          )}\n          {unref(showEmpty) && (\n            <Empty class={ns.e('empty')} style={unref(emptyStyle)}>\n              {{ default: slots.empty }}\n            </Empty>\n          )}\n          {slots.overlay && (\n            <Overlay class={ns.e('overlay')}>\n              {{ default: slots.overlay }}\n            </Overlay>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2\n\nexport type TableV2Instance = InstanceType<typeof TableV2> & {\n  /**\n   * @description scroll to a given position\n   * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n   */\n  scrollTo: (param: { scrollLeft?: number; scrollTop?: number }) => void\n  /**\n   * @description scroll to a given position horizontally\n   * @params scrollLeft {Number} where to scroll to.\n   */\n  scrollToLeft: (scrollLeft: number) => void\n  /**\n   * @description scroll to a given position vertically\n   * @params scrollTop { Number } where to scroll to.\n   */\n  scrollToTop: (scrollTop: number) => void\n  /**\n   * @description scroll to a given row\n   * @params row {Number} which row to scroll to\n   * @params strategy {ScrollStrategy} use what strategy to scroll to\n   */\n  scrollToRow(row: number, strategy?: ScrollStrategy): void\n}\n"], "names": ["Overlay", "COMPONENT_NAME", "TableV2", "defineComponent", "name", "props", "useNamespace", "slots", "expose", "columnsStyles", "fixedColumnsOnLeft", "fixedColumnsOnRight", "mainColumns", "mainTableHeight", "fixedTableHeight", "leftTableWidth", "rightTableWidth", "data", "depthMap", "expandedRowKeys", "hasFixedColumns", "mainTableRef", "leftTableRef", "rightTableRef", "isDynamic", "isResetting", "isScrolling", "bodyWidth", "emptyStyle", "rootStyle", "footerHeight", "showEmpty", "scrollTo", "scrollToLeft", "scrollToTop", "scrollToRow", "getRowHeight", "onColumnSorted", "onRowHeightChange", "useTable", "onRowsRendered", "onScroll", "onVerticalScroll", "provide", "TableV2InjectionKey", "unref", "cache", "cellProps", "estimatedRowHeight", "expandColumnKey", "fixedData", "headerHeight", "headerClass", "headerProps", "headerCellProps", "sortBy", "sortState", "rowHeight", "rowClass", "rowEventHandlers", "<PERSON><PERSON><PERSON>", "rowProps", "scrollbarAlwaysOn", "indentSize", "iconSize", "useIsScrolling", "vScrollbarSize", "width", "_data", "class", "columns", "headerWidth", "height", "scrollbarStartGap", "scrollbarEndGap", "_fixedTableHeight", "style", "tableRowProps", "ns", "_createVNode", "Row", "_mergeProps", "Cell", "Header", "tableHeaderProps", "<PERSON><PERSON><PERSON><PERSON>", "_columnsStyles", "tableSlots", "row", "cell", "column", "LeftTable", "Footer", "header"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAeA,SAAOA,WAAa;;;;;;AAYpB,EAAMC,KAAAA,EAAAA;AAEN,EAAMC,KAAAA,CAAAA,KAAO,EAAGC;AACdC,IAAAA,KAD8B;AAE9BC,IAAAA,MAF8B;;IAGzB,WAAQC,kBAAA,CAAA,UAAA,CAAA,CAAA;IAAEC,MAAF;AAASC,MAAAA,aAAAA;AAAT,MAAmB,kBAAA;AAC9B,MAAA,mBAAuB;MAEjB,WAAA;MACJC,eADI;MAEJC,gBAFI;MAGJC,cAHI;MAIJC,eAJI;MAKJC,IALI;MAMJC,QANI;MAOJC,eAPI;MAQJC,eARI;MASJC,YATI;MAUJC,YAVI;MAWJC,aAXI;MAYJC,SAZI;MAaJC,WAbI;MAcJC,WAdI;MAeJC,SAfI;MAgBJC,UAhBI;MAiBJC,SAjBI;MAkBJC,YAlBI;MAoBJC,SApBI;MAqBJC,QArBI;MAsBJC,YAtBI;MAuBJC,WAvBI;MAyBJC,WAzBI;AA2BJ,MAAA,YAAA;MACAC,cA5BI;MA6BJC,iBA7BI;MA8BJC,YA9BI;MA+BJC,aA/BI;MAiCJC,cAjCI;MAkCJC,QAlCI;MAmCJC,gBAnCI;QAAAC,iBAAA,CAAA,KAAA,CAAA,CAAA;UAAA,CAAA;MAsCJC,QAtCI;MAuCJC,YAvCI;AAwCJC,MAAAA,WAAAA;MACEH,WAASlC;AAEbG,KAAAA,CAAAA,CAAAA;AACE,IAAAmC,WAAA,CAAAC,0BAAA,EAAA;AACN,MAAA,EAAA;AACA,MAAA,WAAA;AACA,MAAA,WAAA;MACMZ,CALK;;AAML,MAAA,MAAA;AACN,QAAA,KAAA;AACA,QAAA,SAAA;AACA,QAAA,kBAAA;QATW,eAAA;;AAWL,QAAA,YAAA;AACN,QAAA,WAAA;AACA,QAAA,WAAA;AACA,QAAA,eAAA;QAdW,MAAA;;AAgBL,QAAA,SAAA;AACN,QAAA,QAAA;AACA,QAAA,gBAAA;AACA,QAAA,MAAA;AACA,QAAA,QAAA;AACMG,QAAAA,iBAAAA;AArBK,QAAP,UAAA;QAwBO;QAAsB,cAAA;QAAA,cAAA;AAG3BT,QAAAA,KAAAA;AAH2B,OAA7B,GAAA,KAAA,CAAA;AAMA,MAAA,MAAa,KAAA,GAAAmB,SAAA,CAAA,IAAA,CAAA,CAAA;MACX,MAAM,cAAA,GAAA;QACJC,KADI;QAEJC,KAFI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QAGJC,OAHI,EAAAH,SAAA,CAAA,WAAA,CAAA;QAIJI,IAJI,EAAA,KAAA;QAKJC,SALI;QAMJC,kBANI;QAOJC,SAPI,EAAAP,SAAA,CAAA,SAAA,CAAA;QAQJQ,YARI;QASJC,WATI,EAAAT,SAAA,CAAA,SAAA,CAAA;QAUJU,MAVI,EAAAV,SAAA,CAAA,eAAA,CAAA;QAWJW,YAXI;QAYJC,MAZI;QAaJC,SAbI;QAcJC,iBAdI;QAeJC,iBAfI,EAAA,CAAA;QAgBJC,eAhBI,EAAA,cAAA;QAiBJC,cAjBI;QAkBJC,KAlBI;QAmBJC,YAnBI;QAoBJC,cApBI;QAqBJC,QArBI;AAsBJC,OAAAA,CAAAA;AAtBI,MAAA,MAAN,gBAAA,GAAAtB,SAAA,CAAA,cAAA,CAAA,CAAA;;AAyBA,MAAA,MAAMuB,cAAcnD;;AAEpB,QAAA,kBAAoB,CAAG;QACrB6B,OADqB,EAAAD,SAAA,CAAA,kBAAA,CAAA;AAErBwB,QAAAA,IAAAA,OAAO;AACPC,QAAAA,SAASzB;AACT5B,QAAAA,kBAJqB;QAKrBiC,YALqB;QAMrBF,SANqB;AAOrBrB,QAAAA,SAAS,EAAEkB,gBAPU;QAQrBM,WARqB,EAAA,gBAAA;AASrBoB,QAAAA,YAAa1B;AACb2B,QAAAA,MAAM,EAAE3B,iBAAMhC;QACdQ,MAXqB;QAYrBuC,iBAZqB;QAarBH,iBAbqB,EAAA,CAAA;QAcrBK,eAdqB,EAAA,cAAA;AAerBW,QAAAA,cAAAA;AACAC,QAAAA,KAAAA,EAAAA,gBAhBqB;QAiBrBT,YAjBqB;QAkBrBE,QAlBqB,EAAA,gBAAA;QAmBrB/B;YAnBqB,iBAAA,GAAAS,SAAA,CAAA,eAAA,CAAA,CAAA;AAqBrBJ,MAAAA,MAAAA,eAAAA,GAAAA;QArBF,KAAA;AAwBA,QAAA,KAAsB,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;;AACtB,QAAA,IAAMkC,EAAiB,KAAA;;AAEvB,QAAA,kBAAoB;QAClB7B,aADqB;AAErBuB,QAAAA,SAAS;AACTC,QAAAA,SAASzB,EAAAA,iBAAMnC;AACfO,QAAAA,WAJqB,EAAA,iBAAA;QAKrBiC,YALqB;QAMrBF,MANqB,EAAA,iBAAA;QAOrB1B,MAPqB;QAQrBmC,iBARqB;AASrB9B,QAAAA,iBATqB,EAAA,CAAA;AAUrB4C,QAAAA,eAVqB,EAAA,cAAA;QAWrBpB,KAXqB,EAAA,iBAAA;AAYrBqB,QAAAA,KAAAA,GAAQG,EAZa,EAAA9B,SAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,CAAA;QAarBe,cAbqB;QAcrBE,YAdqB;AAerBW,QAAAA,QAAAA,EAAAA,gBAfqB;AAgBrBC,OAAAA,CAAAA;YAhBqB,cAAA,GAAA7B,SAAA,CAAA,aAAA,CAAA,CAAA;AAkBrBsB,MAAAA,MAAAA,aAlBqB,GAAA;QAmBrB/B,EAnBqB;AAoBrBK,QAAAA,QAAQ,EAAEC,SAAAA,CAAAA,QAAAA,CAAAA;QApBZ,aAAA,EAAA,cAAA;AAuBA,QAAA,eAAuB;AAEvB,QAAA,0BAAwB,CAAA,eAAA,CAAA;QACtBI,kBADsB;AAEtBuB,QAAAA,0BAFsB,CAAA,eAAA,CAAA;AAGtBC,QAAAA,QAASzB;AACT5B,QAAAA,QAJsB;QAKtBiC,MALsB;QAMtBF,gBANsB;QAOtBzB,YAPsB;QAQtBkC,aARsB;AAStB9B,QAAAA,iBATsB;AAUtB4C,OAAAA,CAAAA;YAVsB,cAAA,GAAA;AAYtBC,QAAAA,SAZsB;QAatBZ,eAbsB;QActBE,UAdsB;AAetBW,QAAAA,QAAAA;AACAC,QAAAA,MAAAA;AACAP,QAAAA,eAjBsB,EAAAtB,SAAA,CAAA,eAAA,CAAA;QAkBtB+B,EAAK;QAGLX;YArBsB,gBAAA,GAAA;AAuBtBxB,QAAAA,EAAAA;QAvBF,WAAA;;AAyBA,QAAA,aAAoB,EAAA;;AAEpB,MAAA,MAAMoC,oBAAgB,GAAA;QACpBC,EADoB;AAEpB5D,QAAAA,MAAAA;AACAT,QAAAA,SAAAA;QACAwC,eAJoB;AAKpB9B,QAAAA,cAAAA;QACA6B;AACA5B,MAAAA,MAAAA,UAAAA,GAAiByB;QACjBgB,GARoB,EAAA,CAAA,MAAA,KAAAkB,eAAA,CAAAC,cAAA,EAAAC,cAAA,CAAA,MAAA,EAAA,aAAA,CAAA,EAAA;UAAA,GAAA,EAAA,KAAA,CAAA,GAAA;UAAA,IAAA,EAAA,CAAA,MAAA,KAAA;YAAA,IAAA,KAAA,CAAA;YAAA,OAAA,KAAA,CAAA,IAAA,GAAAF,eAAA,CAAAG,eAAA,EAAAD,cAAA,CAAA,MAAA,EAAA,cAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AAcpB3C,aAAAA,CAAAA,EAAAA,OAAAA,CAAAA,KAAAA,GAAAA,KAAAA,CAAAA,IAAAA,CAAAA,MAAAA,CAAAA,CAAAA,GAAAA,KAAAA,GAAAA;cAdF,OAAA,EAAA,MAAA,CAAA,KAAA,CAAA;AAiBA,gCAAuB,CAAA4C,eAAA,EAAAD,cAAA,CAAA,MAAA,EAAA,cAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,IAAA,CAAA,CAAA;WAAA;SAAA,CAAA;QAKrBrB,MALqB,EAAA,CAAA,MAAA,KAAAmB,eAAA,CAAAI,iBAAA,EAAAF,cAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;AAMrB9D,UAAAA,MAAAA,EAAAA,KAAe,CAAE0B,MAAK;AACtBiC,UAAAA,IAAAA,EAAAA,CAAAA,MAAAA,KAAAA;YAPF,IAAA,MAAA,CAAA;AAUA,YAAMM,0BAAmB,CAAA,GAAAL,eAAA,CAAAM,qBAAA,EAAAJ,cAAA,CAAA,MAAA,EAAA,oBAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,OAAA,CAAA,MAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,CAAA,GAAA,MAAA,GAAA;cAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CAAA;AAIvBxE,aAAAA,CAAAA,GAAAA,eAAe6E,CAAAA,qBAAAA,EAAAA,cAAAA,CAAAA,MAAAA,EAAAA,oBAAAA,EAAAA;cAJjB,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AAOA;WAA6B;SAAA,CAAA;QAI3B9B;YAJ2B,OAAA,GAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA;AAM3BnB,QAAAA,CAAAA,EAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,GAAAA,SAAAA,CAAAA,SAAAA,CAAAA;OANF,CAAA,CAAA;AASA,MAAA,MAAMkD,WAAa,GAAA;AACjBC,QAAAA,KAAMnF,EAAAA,EAAAA,CAAD,CACMA,CAAAA,QAAAA,CAAAA;aAEF,EAAEE,SAAK,CAACiF,YAHZ,CAAA;AAICC,OAAAA,CAAAA;AAAM,MAAA,OAAAV,eAAA,CAAA,KAAA,EAAA;;AAAA,QAAA,OAAA,EAAAlC,mBAGIxC,CAAAA;AAFR,OAAA,EAAA,CAAA0E,eAAA,CAIWO,oCAAqBI,EAAN,OAAD,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;AAJzB,QAAA,OAAA,EAAA,MAAA,CAAA,UAMU,CAAA;AANV,OAAA,CAAA,EAAAX,eAAA,CAAAY,oBAAA,EAAA,cAAA,EAAA,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;eAUQtF,EAAAA,MAAAA,CAAAA,UAAAA,CAAAA;AAVR,OAAA,CAAA,EAAA0E,eAAA,CAYWO,sCAAe,EAAD,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;eAbrB,EAAA,MAAA,CAAA,UAAA,CAAA;AAAA,OAAA,CAAA,EAAA,KAAA,CAAA,MAAA,IAAAP,eAAA,CAAAa,iBAAA,EAAA,WAAA,EAAA;QAJP,OADY,EAAA,KAAA,CAAA,MAAA;AAwBjBC,OAAAA,CAAAA,EAAAA,SAASxF,CAAAA,SACKA,CAAAA,IAAAA,eAAAA,CAAAA,gBAAAA,EAAAA;eAEF,EAAA,EAAO,CAAA,CAAA,CAAA,OAHX,CAAA;AAIFoF,QAAAA,SAAM5C,SAAA,CAAA,UAAA,CAAA;AAAA,OAAA,EAAA;;AAAA,OAAA,CAAA,EAAA,KAAA,CAAA,WACEkC,eAAN,CAEQ1E,kBAAAA,EAAAA;AAFR,QAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA,CAAA,OAAA;;AAAA,KAAA,CAAA;;AADI,CAAA,CAAA,CAAA;AAJJ,gBAAA,OAAA;;;;"}