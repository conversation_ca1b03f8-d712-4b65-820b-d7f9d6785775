import instance from '../../utils/axios.js'

const settledMemberRecordApi = {
  // 获取入驻会员开通记录列表
  getSettledMemberRecordList: (params) => instance.get('/settledMember/record/list', { params }),
  
  getSettledMemberRecordlistWithDetails: (params) => instance.get('/settledMember/record/listWithDetails', { params }),


  // 删除入驻会员开通记录
  deleteSettledMemberRecord: (id) => instance.post('/settledMember/record/delete', null, { params: { id } }),
  
  // 新增入驻会员开通记录
  addSettledMemberRecord: (data) => instance.post('/settledMember/record/add', data),
  
  // 更新入驻会员开通记录
  updateSettledMemberRecord: (data) => {
    console.log('API调用 - 请求数据:', JSON.stringify(data, null, 2))
    return instance.put('/settledMember/record/update', data, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  },
  
  // 更新入驻会员开通记录状态
  updateMemberRecordStatus: (id, state) => {
    console.log('发送状态更新请求:', { id, state });
    // 使用查询参数形式发送，确保参数名与后端一致
    return instance.post('/settledMember/record/updateStatus', null, { 
      params: { id, state }
    });
  },
  
  // 搜索入驻会员开通记录（分页+条件）
  searchSettledMemberRecord: (params) => instance.get('/settledMember/record/search', { params }),
}

export { settledMemberRecordApi } 