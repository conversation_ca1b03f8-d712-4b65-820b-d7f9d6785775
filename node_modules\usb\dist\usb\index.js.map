{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../tsc/usb/index.ts"], "names": [], "mappings": ";AAAA,mCAAsC;AACtC,qCAA0C;AAC1C,kCAAkC;AAElC,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;IACjB,+BAA+B;IAC/B,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;AACjD,CAAC;AAED,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,qBAAY,CAAC,SAAS,CAAC,CAAC;AACnD,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,aAAa,EAAE;IACtC,KAAK,EAAE,KAAK;IACZ,QAAQ,EAAE,IAAI;CACjB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,EAAE;IAC3C,KAAK,EAAE,GAAG;IACV,QAAQ,EAAE,IAAI;CACjB,CAAC,CAAC;AAEH,4DAA4D;AAC5D,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;IACb,MAAM,CAAC,mBAAmB,CAAC,uBAAc,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAChE,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,wBAAwB,CAAC,uBAAc,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9I,CAAC,CAAC,CAAC;AACP,CAAC;AAoCD,sDAAsD;AACtD,IAAI,cAAc,GAAG,IAAI,GAAG,EAAc,CAAC;AAE3C,oIAAoI;AACpI,MAAM,iBAAiB,GAAG,GAAG,EAAE;IAC3B,0BAA0B;IAC1B,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;IAE7C,wBAAwB;IACxB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC/B,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACvB,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC/B,CAAC;IACL,CAAC;IAED,cAAc,GAAG,OAAO,CAAC;AAC7B,CAAC,CAAC;AAEF,yFAAyF;AACzF,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,MAAM,WAAW,GAAG,CAAC,KAAK,GAAG,KAAK,EAAE,EAAE;IAClC,IAAI,KAAK,EAAE,CAAC;QACR,cAAc,GAAG,IAAI,CAAC;IAC1B,CAAC;SAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QACzB,OAAO;IACX,CAAC;SAAM,CAAC;QACJ,iBAAiB,EAAE,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAC1D,CAAC,CAAC;AAEF,gCAAgC;AAChC,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,iBAAiB,EAAE,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAEzF,kBAAkB;AAClB,IAAI,gBAAgB,GAAG,CAAC,CAAC;AACzB,MAAM,YAAY,GAAG,GAAG,EAAE;IACtB,gBAAgB,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,uBAAuB,EAAE,CAAC;IAEvE,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;QACzB,gDAAgD;QAChD,cAAc,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,gBAAgB,EAAE,CAAC;QACnB,6BAA6B;QAC7B,GAAG,CAAC,oBAAoB,EAAE,CAAC;QAE3B,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YACzB,kDAAkD;YAClD,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACpC,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,iDAAiD;QACjD,WAAW,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,GAAG,EAAE;IACrB,IAAI,gBAAgB,EAAE,CAAC;QACnB,yBAAyB;QACzB,GAAG,CAAC,qBAAqB,EAAE,CAAC;QAE5B,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YACzB,oCAAoC;YACpC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACrC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,eAAe;QACf,cAAc,GAAG,KAAK,CAAC;IAC3B,CAAC;AACL,CAAC,CAAC;AAEF,GAAG,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;IAC1B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC3C,OAAO;IACX,CAAC;IACD,MAAM,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChF,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;QACtB,YAAY,EAAE,CAAC;IACnB,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,GAAG,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;IAC7B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC3C,OAAO;IACX,CAAC;IACD,MAAM,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChF,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;QACtB,WAAW,EAAE,CAAC;IAClB,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,iBAAS,GAAG,CAAC"}