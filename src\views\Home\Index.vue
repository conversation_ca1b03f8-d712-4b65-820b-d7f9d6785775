<template>
  <div class="monitoring-dashboard">
    <!-- 页面标题 -->
    <div class="dashboard-header">
      <h1>实时服务器监控大屏</h1>
      <div class="last-update">
        <span id="lastUpdate">最后更新: --:--:--</span>
        <el-button @click="refreshData" size="small" type="primary">手动刷新</el-button>
      </div>
    </div>

    <!-- 汇总卡片区域 -->
    <div class="summary-section">
      <div class="summary-card">
        <div class="card-icon server-icon">
          <i class="el-icon-monitor"></i>
        </div>
        <div class="card-content">
          <div class="card-title">服务器状态</div>
          <div class="card-value" id="serverCount">0/0</div>
          <div class="card-desc">在线/总数</div>
        </div>
      </div>

      <div class="summary-card">
        <div class="card-icon cpu-icon">
          <i class="el-icon-cpu"></i>
        </div>
        <div class="card-content">
          <div class="card-title">平均CPU</div>
          <div class="card-value" id="avgCpu">0%</div>
          <div class="card-desc">处理器使用率</div>
        </div>
      </div>

      <div class="summary-card">
        <div class="card-icon memory-icon">
          <i class="el-icon-memory-card"></i>
        </div>
        <div class="card-content">
          <div class="card-title">平均内存</div>
          <div class="card-value" id="avgMemory">0%</div>
          <div class="card-desc">内存使用率</div>
        </div>
      </div>

      <div class="summary-card">
        <div class="card-icon alert-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="card-content">
          <div class="card-title">活跃告警</div>
          <div class="card-value" id="alertCount">0</div>
          <div class="card-desc">需要关注</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <div class="chart-title">服务器状态分布</div>
        <div id="serverStatusChart" class="chart"></div>
      </div>

      <div class="chart-container">
        <div class="chart-title">平均资源使用率</div>
        <div id="resourceChart" class="chart"></div>
      </div>
    </div>

    <!-- 服务器列表区域 -->
    <div class="server-list-section">
      <div class="section-title">
        <h3>服务器详细信息</h3>
        <div class="loading-indicator" id="serversLoading" v-show="loading">
          <i class="el-icon-loading"></i> 加载中...
        </div>
      </div>

      <div class="error-message" id="serversError" v-show="errorMessage">
        {{ errorMessage }}
      </div>

      <div class="server-table-container">
        <table class="server-table" id="serversTable">
          <thead>
            <tr>
              <th>服务器名称</th>
              <th>状态</th>
              <th>CPU使用率</th>
              <th>内存使用率</th>
              <th>磁盘使用率</th>
              <th>服务状态</th>
              <th>最后更新</th>
            </tr>
          </thead>
          <tbody id="serversTableBody">
            <!-- 动态生成的服务器行 -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'MonitoringDashboard',
  data() {
    return {
      loading: false,
      errorMessage: '',
      serverStatusChart: null,
      resourceChart: null,
      refreshTimer: null,
      API_BASE_URL: '/api/monitor'
    }
  },

  mounted() {
    this.initCharts()
    this.loadDashboardData()
    this.startAutoRefresh()

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },

  beforeUnmount() {
    this.stopAutoRefresh()
    window.removeEventListener('resize', this.handleResize)

    // 销毁图表实例
    if (this.serverStatusChart) {
      this.serverStatusChart.dispose()
    }
    if (this.resourceChart) {
      this.resourceChart.dispose()
    }
  },

  methods: {
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        const serverStatusEl = document.getElementById('serverStatusChart')
        const resourceEl = document.getElementById('resourceChart')

        if (serverStatusEl) {
          this.serverStatusChart = echarts.init(serverStatusEl)
        }
        if (resourceEl) {
          this.resourceChart = echarts.init(resourceEl)
        }
      })
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadDashboardData()
      }, 30000) // 30秒刷新一次
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 加载仪表板数据
    async loadDashboardData() {
      this.loading = true
      this.errorMessage = ''

      try {
        const [summaryResponse, serversResponse, serverStatsResponse, resourceStatsResponse] = await Promise.all([
          fetch(`${this.API_BASE_URL}/dashboard/summary`),
          fetch(`${this.API_BASE_URL}/servers`),
          fetch(`${this.API_BASE_URL}/stats/servers`),
          fetch(`${this.API_BASE_URL}/stats/resources`)
        ])

        if (summaryResponse.ok && serversResponse.ok && serverStatsResponse.ok && resourceStatsResponse.ok) {
          const summaryData = await summaryResponse.json()
          const serversData = await serversResponse.json()
          const serverStatsData = await serverStatsResponse.json()
          const resourceStatsData = await resourceStatsResponse.json()

          // 检查后端返回的JsonResult结构
          if (summaryData.code === 200 && serversData.code === 200 &&
            serverStatsData.code === 200 && resourceStatsData.code === 200) {
            this.updateSummaryCards(summaryData.data, serverStatsData.data)
            this.updateCharts(serverStatsData.data, resourceStatsData.data)
            this.updateServersTable(serversData.data)
            this.updateLastUpdateTime()
          } else {
            this.errorMessage = '数据加载失败'
          }
        } else {
          this.errorMessage = 'API请求失败'
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.errorMessage = '网络连接失败'
      } finally {
        this.loading = false
      }
    },

    // 更新汇总卡片
    updateSummaryCards(summaryData, serverStatsData) {
      const serverCountEl = document.getElementById('serverCount')
      const avgCpuEl = document.getElementById('avgCpu')
      const avgMemoryEl = document.getElementById('avgMemory')
      const alertCountEl = document.getElementById('alertCount')

      if (serverCountEl) {
        serverCountEl.textContent = `${serverStatsData.online}/${serverStatsData.total}`
      }
      if (avgCpuEl) {
        avgCpuEl.textContent = `${summaryData.metrics.avgCpu}%`
      }
      if (avgMemoryEl) {
        avgMemoryEl.textContent = `${summaryData.metrics.avgMemory}%`
      }
      if (alertCountEl) {
        alertCountEl.textContent = summaryData.alerts.active
      }
    },

    // 更新图表
    updateCharts(serverStatsData, resourceStatsData) {
      // 更新服务器状态饼图
      if (this.serverStatusChart) {
        const serverStatusOption = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: {
              color: '#333'
            }
          },
          series: [{
            name: '服务器状态',
            type: 'pie',
            radius: '60%',
            center: ['60%', '50%'],
            data: [
              { value: serverStatsData.online, name: '在线', itemStyle: { color: '#27ae60' } },
              { value: serverStatsData.warning, name: '警告', itemStyle: { color: '#f39c12' } },
              { value: serverStatsData.offline, name: '离线', itemStyle: { color: '#e74c3c' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        }
        this.serverStatusChart.setOption(serverStatusOption)
      }

      // 更新资源使用率柱状图
      if (this.resourceChart) {
        const resourceOption = {
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}%'
          },
          xAxis: {
            type: 'category',
            data: ['CPU', '内存', '磁盘'],
            axisLabel: {
              color: '#333'
            }
          },
          yAxis: {
            type: 'value',
            max: 100,
            axisLabel: {
              formatter: '{value}%',
              color: '#333'
            }
          },
          series: [{
            type: 'bar',
            data: [
              { value: resourceStatsData.cpu, itemStyle: { color: '#e74c3c' } },
              { value: resourceStatsData.memory, itemStyle: { color: '#f39c12' } },
              { value: resourceStatsData.disk, itemStyle: { color: '#3498db' } }
            ],
            barWidth: '60%'
          }]
        }
        this.resourceChart.setOption(resourceOption)
      }
    },

    // 更新服务器列表
    updateServersTable(servers) {
      const tbody = document.getElementById('serversTableBody')
      if (!tbody) return

      tbody.innerHTML = ''

      servers.forEach(server => {
        const row = document.createElement('tr')
        row.innerHTML = `
          <td class="server-name">${this.getFriendlyServerName(server.hostname)}</td>
          <td><span class="status-badge status-${server.status}">${this.getStatusText(server.status)}</span></td>
          <td>${this.createProgressBar(server.cpuPercent || 0)}</td>
          <td>${this.createProgressBar(server.memPercent || 0)}</td>
          <td>${this.createProgressBar(server.diskUsage || 0)}</td>
          <td class="service-status">
            <span class="service-indicator ${server.mysqlAlive ? 'online' : 'offline'}" title="MySQL">M</span>
            <span class="service-indicator ${server.redisAlive ? 'online' : 'offline'}" title="Redis">R</span>
            <span class="service-indicator ${server.kafkaAlive ? 'online' : 'offline'}" title="Kafka">K</span>
          </td>
          <td>${this.formatTimestamp(server.lastUpdate)}</td>
        `
        tbody.appendChild(row)
      })
    },

    // 获取友好的服务器名称
    getFriendlyServerName(hostname) {
      const nameMap = {
        'VM-0-3-opencloudos': '服务层服务器',
        'VM-0-16-opencloudos': '入口层服务器',
        'VM-0-15-opencloudos': '负载A服务器',
        'VM-0-9-opencloudos': '负载B层服务器',
        'VM-0-7-opencloudos': '核价专用服务器',
        'VM-0-6-opencloudos': '临-主服务服务器',
        'VM-28-17-opencloudos': '临-测试服务器'
      }
      return nameMap[hostname] || hostname
    },

    // 创建进度条
    createProgressBar(value) {
      const percentage = Math.round(value || 0)
      let colorClass = 'progress-low'
      if (percentage > 80) colorClass = 'progress-high'
      else if (percentage > 60) colorClass = 'progress-medium'

      return `
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill ${colorClass}" style="width: ${percentage}%"></div>
          </div>
          <span class="progress-text">${percentage}%</span>
        </div>
      `
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'online': '在线',
        'warning': '警告',
        'offline': '离线'
      }
      return statusMap[status] || status
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    },

    // 更新最后更新时间
    updateLastUpdateTime() {
      const now = new Date()
      const lastUpdateEl = document.getElementById('lastUpdate')
      if (lastUpdateEl) {
        lastUpdateEl.textContent = `最后更新: ${now.toLocaleTimeString()}`
      }
    },

    // 手动刷新数据
    refreshData() {
      this.loadDashboardData()
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.serverStatusChart) {
        this.serverStatusChart.resize()
      }
      if (this.resourceChart) {
        this.resourceChart.resize()
      }
    }
  }
}
</script>

<style scoped>
.monitoring-dashboard {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #fff;
}

/* 页面标题区域 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.dashboard-header h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.last-update {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.1rem;
}

/* 汇总卡片区域 */
.summary-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 25px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
}

.server-icon {
  background: linear-gradient(45deg, #4CAF50, #45a049);
}

.cpu-icon {
  background: linear-gradient(45deg, #2196F3, #1976D2);
}

.memory-icon {
  background: linear-gradient(45deg, #FF9800, #F57C00);
}

.alert-icon {
  background: linear-gradient(45deg, #f44336, #d32f2f);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 5px;
}

.card-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-desc {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.chart {
  height: 300px;
  width: 100%;
}

/* 服务器列表区域 */
.server-list-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title h3 {
  font-size: 1.5rem;
  margin: 0;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  opacity: 0.8;
}

.error-message {
  background: rgba(244, 67, 54, 0.2);
  color: #ffcdd2;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #f44336;
}

.server-table-container {
  overflow-x: auto;
}

.server-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  overflow: hidden;
}

.server-table th {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  text-align: left;
  font-weight: bold;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.server-table td {
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.server-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

.server-name {
  font-weight: bold;
  color: #fff;
}

.status-badge {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
}

.status-online {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.status-warning {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
  border: 1px solid #FF9800;
}

.status-offline {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid #f44336;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-low {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.progress-medium {
  background: linear-gradient(90deg, #FF9800, #FFC107);
}

.progress-high {
  background: linear-gradient(90deg, #f44336, #FF5722);
}

.progress-text {
  font-size: 0.9rem;
  font-weight: bold;
  min-width: 40px;
}

.service-status {
  display: flex;
  gap: 8px;
}

.service-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
}

.service-indicator.online {
  background: #4CAF50;
  color: white;
}

.service-indicator.offline {
  background: #f44336;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .summary-section {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .chart {
    height: 250px;
  }

  .server-table-container {
    font-size: 0.9rem;
  }

  .server-table th,
  .server-table td {
    padding: 10px 8px;
  }
}

@media (max-width: 480px) {
  .monitoring-dashboard {
    padding: 10px;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .summary-card {
    padding: 15px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 15px;
  }

  .card-value {
    font-size: 1.5rem;
  }

  .chart {
    height: 200px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.summary-card,
.chart-container,
.server-list-section {
  animation: fadeIn 0.6s ease-out;
}

/* 滚动条样式 */
.server-table-container::-webkit-scrollbar {
  height: 8px;
}

.server-table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>