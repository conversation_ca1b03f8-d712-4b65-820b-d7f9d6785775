import axios from '@/utils/axios'

/**
 * 获取角色列表
 */
export function getRoleList() {
  return axios({
    url: '/admin/role/list',
    method: 'get'
  })
}

/**
 * 添加角色
 */
export function addRole(data) {
  return axios({
    url: '/admin/role/add',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 */
export function updateRole(data) {
  return axios({
    url: '/admin/role/update',
    method: 'put',
    data
  })
}

/**
 * 删除角色
 */
export function deleteRole(id) {
  return axios({
    url: `/admin/role/delete/${id}`,
    method: 'delete'
  })
}

/**
 * 获取角色权限
 */
export function getRolePermissions(roleId) {
  return axios({
    url: `/admin/role/permissions/selectRoleAndPermissions/${roleId}`,
    method: 'get'
  })
}

/**
 * 更新角色权限
 */
export function updateRolePermissions(roleId, permissionIds) {
  return axios({
    url: `/admin/role/permissions/${roleId}`,
    method: 'put',
    data: permissionIds
  })
} 