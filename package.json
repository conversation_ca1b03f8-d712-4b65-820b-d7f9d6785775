{"name": "vue3_cli_default", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "escpos": "^3.0.0-alpha.6", "escpos-usb": "^3.0.0-alpha.4", "vue": "^3.4.21", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.4.21", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^2.5.2"}}