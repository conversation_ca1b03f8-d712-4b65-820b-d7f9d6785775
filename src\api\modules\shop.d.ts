// Shop API类型定义
export interface ShopParams {
  pageNum?: number;
  pageSize?: number;
  shopType?: string;
  shopGroup?: string;
  shopName?: string;
  shopAliasName?: string;
  shopAuthorize?: string;
  status?: string;
}

export interface ShopData {
  id: number;
  mallId?: string;
  shopNike?: string;
  shopType: string;
  shopGroup?: string;
  shopName: string;
  shopAliasName: string;
  shopAuthorize: string;
  status: string;
  account?: string;
  password?: string;
  isSynOrder: number;
  addTime?: string;
  expirationTime?: string;
  createdBy?: number;
  createdTime?: number;
  updatedBy?: number;
  updatedTime?: number;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface PageResponse<T> {
  endRow: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  list: T[];
  navigateFirstPage: number;
  navigateLastPage: number;
  navigatePages: number;
  navigatepageNums: number[];
  nextPage: number;
  pageNum: number;
  pageSize: number;
  pages: number;
  prePage: number;
  size: number;
  startRow: number;
  total: number;
}

declare function getShopList(params: ShopParams): Promise<{data: ApiResponse<PageResponse<ShopData>>}>;
declare function getShopDetail(id: number): Promise<{data: ApiResponse<ShopData>}>;
declare function addShop(data: ShopData): Promise<{data: ApiResponse<any>}>;
declare function updateShop(data: ShopData): Promise<{data: ApiResponse<any>}>;
declare function deleteShop(id: number): Promise<{data: ApiResponse<any>}>;
declare function batchDeleteShop(ids: string): Promise<{data: ApiResponse<any>}>;
declare function updateSyncOrderStatus(id: number, isSynOrder: number): Promise<{data: ApiResponse<any>}>;

export {
  getShopList,
  getShopDetail,
  addShop,
  updateShop,
  deleteShop,
  batchDeleteShop,
  updateSyncOrderStatus
}; 