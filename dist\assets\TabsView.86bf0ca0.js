import{d as a,T as e,u as l,Z as t,_ as u,$ as s,a0 as o,a1 as n,Q as v,o as c,k as i,m as d,w as p,H as m,G as r,l as h,F as f,N as b,a2 as w}from"./vendor.9a6f3141.js";const _={class:"tabs-container"},P={class:"tab-content"};var V=a({__name:"TabsView",setup(a){const V=e(),k=l(),y=t(V.path),T=t([]),x=t([]);u((()=>{T.value.some((a=>"/welcome"===a.path))||T.value.push({title:"欢迎页",path:"/welcome"})})),s(V,(a=>{var e;const l=a.fullPath,t=(null==(e=a.meta.title)?void 0:e.toString())||"异常页";if("/welcome"===l)return;T.value.find((a=>a.path===l))||(T.value.push({title:t,path:l}),x.value.includes(l)||x.value.push(l)),y.value=l}),{immediate:!0}),s(y,(a=>{a&&a!==V.fullPath&&k.push(a)}));const I=a=>{if("/welcome"===a)return;const e=T.value.findIndex((e=>e.path===a));if(-1!==e&&(x.value=x.value.filter((e=>e!==a)),T.value.splice(e,1),a===V.fullPath)){const a=T.value[e]||T.value[e-1];y.value=(null==a?void 0:a.path)||"/welcome"}};return(a,e)=>{const l=o,t=n,u=v("router-view");return c(),i("div",_,[d(t,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>y.value=a),type:"card",closable:"",onTabRemove:I},{default:p((()=>[(c(!0),i(m,null,r(T.value,(a=>(c(),f(l,{key:a.path,label:a.title,name:a.path,closable:"/welcome"!==a.path},null,8,["label","name","closable"])))),128))])),_:1},8,["modelValue"]),h("div",P,[d(u,null,{default:p((({Component:e})=>[(c(),f(w,{include:x.value},[(c(),f(b(e),{key:a.$route.fullPath}))],1032,["include"]))])),_:1})])])}}});V.__scopeId="data-v-c370b4d0";export{V as default};
