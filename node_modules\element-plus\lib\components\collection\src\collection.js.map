{"version": 3, "file": "collection.js", "sources": ["../../../../../../packages/components/collection/src/collection.ts"], "sourcesContent": ["import { inject, onBeforeUnmount, onMounted, provide, ref, unref } from 'vue'\nimport Collection from './collection.vue'\nimport CollectionItem from './collection-item.vue'\n\nimport type { InjectionKey, SetupContext } from 'vue'\nimport type {\n  ElCollectionInjectionContext,\n  ElCollectionItemInjectionContext,\n} from './tokens'\n\nexport const COLLECTION_ITEM_SIGN = `data-el-collection-item`\n\n// Make sure the first letter of name is capitalized\nexport const createCollectionWithScope = (name: string) => {\n  const COLLECTION_NAME = `El${name}Collection`\n  const COLLECTION_ITEM_NAME = `${COLLECTION_NAME}Item`\n  const COLLECTION_INJECTION_KEY: InjectionKey<ElCollectionInjectionContext> =\n    Symbol(COLLECTION_NAME)\n  const COLLECTION_ITEM_INJECTION_KEY: InjectionKey<ElCollectionItemInjectionContext> =\n    Symbol(COLLECTION_ITEM_NAME)\n\n  const ElCollection = {\n    ...Collection,\n    name: COLLECTION_NAME,\n    setup() {\n      const collectionRef = ref<HTMLElement>()\n      const itemMap: ElCollectionInjectionContext['itemMap'] = new Map()\n      const getItems = () => {\n        const collectionEl = unref(collectionRef)\n\n        if (!collectionEl) return []\n        const orderedNodes = Array.from(\n          collectionEl.querySelectorAll(`[${COLLECTION_ITEM_SIGN}]`)\n        )\n\n        const items = [...itemMap.values()]\n\n        return items.sort(\n          (a, b) => orderedNodes.indexOf(a.ref!) - orderedNodes.indexOf(b.ref!)\n        )\n      }\n\n      provide(COLLECTION_INJECTION_KEY, {\n        itemMap,\n        getItems,\n        collectionRef,\n      })\n    },\n  }\n\n  const ElCollectionItem = {\n    ...CollectionItem,\n    name: COLLECTION_ITEM_NAME,\n    setup(_: unknown, { attrs }: SetupContext) {\n      const collectionItemRef = ref<HTMLElement>()\n      const collectionInjection = inject(COLLECTION_INJECTION_KEY, undefined)!\n\n      provide(COLLECTION_ITEM_INJECTION_KEY, {\n        collectionItemRef,\n      })\n\n      onMounted(() => {\n        const collectionItemEl = unref(collectionItemRef)\n        if (collectionItemEl) {\n          collectionInjection.itemMap.set(collectionItemEl, {\n            ref: collectionItemEl,\n            ...attrs,\n          })\n        }\n      })\n\n      onBeforeUnmount(() => {\n        const collectionItemEl = unref(collectionItemRef)!\n        collectionInjection.itemMap.delete(collectionItemEl)\n      })\n    },\n  }\n\n  return {\n    COLLECTION_INJECTION_KEY,\n    COLLECTION_ITEM_INJECTION_KEY,\n    ElCollection,\n    ElCollectionItem,\n  }\n}\n"], "names": ["Collection", "ref", "unref", "provide", "CollectionItem", "inject", "onMounted", "onBeforeUnmount"], "mappings": ";;;;;;;;AAGY,MAAC,oBAAoB,GAAG,CAAC,uBAAuB,EAAE;AAClD,MAAC,yBAAyB,GAAG,CAAC,IAAI,KAAK;AACnD,EAAE,MAAM,eAAe,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAChD,EAAE,MAAM,oBAAoB,GAAG,CAAC,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;AACxD,EAAE,MAAM,wBAAwB,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;AAC3D,EAAE,MAAM,6BAA6B,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACrE,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI,GAAGA,qBAAU;AACjB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,KAAK,GAAG;AACZ,MAAM,MAAM,aAAa,GAAGC,OAAG,EAAE,CAAC;AAClC,MAAM,MAAM,OAAO,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAChD,MAAM,MAAM,QAAQ,GAAG,MAAM;AAC7B,QAAQ,MAAM,YAAY,GAAGC,SAAK,CAAC,aAAa,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,YAAY;AACzB,UAAU,OAAO,EAAE,CAAC;AACpB,QAAQ,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpG,QAAQ,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AAC5C,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/F,OAAO,CAAC;AACR,MAAMC,WAAO,CAAC,wBAAwB,EAAE;AACxC,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,aAAa;AACrB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG;AAC3B,IAAI,GAAGC,yBAAc;AACrB,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AACxB,MAAM,MAAM,iBAAiB,GAAGH,OAAG,EAAE,CAAC;AACtC,MAAM,MAAM,mBAAmB,GAAGI,UAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3E,MAAMF,WAAO,CAAC,6BAA6B,EAAE;AAC7C,QAAQ,iBAAiB;AACzB,OAAO,CAAC,CAAC;AACT,MAAMG,aAAS,CAAC,MAAM;AACtB,QAAQ,MAAM,gBAAgB,GAAGJ,SAAK,CAAC,iBAAiB,CAAC,CAAC;AAC1D,QAAQ,IAAI,gBAAgB,EAAE;AAC9B,UAAU,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;AAC5D,YAAY,GAAG,EAAE,gBAAgB;AACjC,YAAY,GAAG,KAAK;AACpB,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAMK,mBAAe,CAAC,MAAM;AAC5B,QAAQ,MAAM,gBAAgB,GAAGL,SAAK,CAAC,iBAAiB,CAAC,CAAC;AAC1D,QAAQ,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC7D,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,wBAAwB;AAC5B,IAAI,6BAA6B;AACjC,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,GAAG,CAAC;AACJ;;;;;"}