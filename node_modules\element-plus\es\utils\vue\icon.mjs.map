{"version": 3, "file": "icon.mjs", "sources": ["../../../../../packages/utils/vue/icon.ts"], "sourcesContent": ["import {\n  <PERSON><PERSON>heck,\n  CircleClose,\n  <PERSON>CloseFilled,\n  Close,\n  InfoFilled,\n  Loading,\n  SuccessFilled,\n  WarningFilled,\n} from '@element-plus/icons-vue'\nimport { definePropType } from './props'\n\nimport type { Component } from 'vue'\n\nexport const iconPropType = definePropType<string | Component>([\n  String,\n  Object,\n  Function,\n])\n\nexport const CloseComponents = {\n  Close,\n}\n\nexport const TypeComponents = {\n  Close,\n  SuccessFilled,\n  InfoFilled,\n  WarningFilled,\n  CircleCloseFilled,\n}\n\nexport const TypeComponentsMap = {\n  primary: InfoFilled,\n  success: SuccessFilled,\n  warning: WarningFilled,\n  error: CircleCloseFilled,\n  info: InfoFilled,\n}\n\nexport const ValidateComponentsMap = {\n  validating: Loading,\n  success: CircleCheck,\n  error: CircleClose,\n}\n"], "names": [], "mappings": ";;;AAWY,MAAC,YAAY,GAAG,cAAc,CAAC;AAC3C,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,CAAC,EAAE;AACS,MAAC,eAAe,GAAG;AAC/B,EAAE,KAAK;AACP,EAAE;AACU,MAAC,cAAc,GAAG;AAC9B,EAAE,KAAK;AACP,EAAE,aAAa;AACf,EAAE,UAAU;AACZ,EAAE,aAAa;AACf,EAAE,iBAAiB;AACnB,EAAE;AACU,MAAC,iBAAiB,GAAG;AACjC,EAAE,OAAO,EAAE,UAAU;AACrB,EAAE,OAAO,EAAE,aAAa;AACxB,EAAE,OAAO,EAAE,aAAa;AACxB,EAAE,KAAK,EAAE,iBAAiB;AAC1B,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE;AACU,MAAC,qBAAqB,GAAG;AACrC,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,OAAO,EAAE,WAAW;AACtB,EAAE,KAAK,EAAE,WAAW;AACpB;;;;"}