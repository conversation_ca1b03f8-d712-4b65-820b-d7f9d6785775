<template>
	<div class="list-container">
		<!-- 搜索区域 -->
		<div class="search-area">
			<el-form :inline="true" :model="searchForm">
				<el-form-item label="会员ID">
					<el-input v-model="searchForm.userId" placeholder="请输入会员ID" clearable />
				</el-form-item>
				<el-form-item label="会员名称">
					<el-input v-model="searchForm.memberName" placeholder="请输入会员名称" clearable />
				</el-form-item>
				<el-form-item label="手机号">
					<el-input v-model="searchForm.phoneNumber" placeholder="请输入手机号" clearable />
				</el-form-item>
				<el-form-item label="开通时间">
					<el-date-picker
						v-model="searchForm.timeRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD"
						:shortcuts="dateShortcuts"
					/>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button @click="resetSearch">重置</el-button>
				</el-form-item>
			</el-form>
		</div>

		<!-- 操作按钮 -->
		<div class="action-bar">
			<!-- 注释掉新增按钮 -->
			<!-- <el-button type="primary" @click="handleAdd">新增</el-button> -->
			<div class="action-left">
				<!-- 左侧按钮区域 -->
			</div>
			<div class="action-right">
				<RefreshButton @refresh="refreshData" />
			</div>
		</div>

		<!-- 数据表格 -->
		<el-table
			ref="tableRef"
			:data="tableData"
			border
			stripe
			style="width: 100%"
			@selection-change="handleSelectionChange"
			row-key="id"
			v-loading="loading"
		>
			<el-table-column type="expand">
				<template #default="{ row }">
					<div class="expand-content">
						<div class="expand-item">
							<span class="expand-label">会员限制:</span>
							<span v-if="row.constraintJson">
								最小本数: {{ getConstraintValue(row.constraintJson, 'books_count_min', 0) }}，
								最大本数: {{ getConstraintValue(row.constraintJson, 'books_count_max', 3000) }}，
								当前本数: {{ row.booksCount || 0 }}
							</span>
							<span v-else>-</span>
						</div>
						<div class="expand-item" v-if="row.note">
							<span class="expand-label">备注:</span>
							<span>{{ row.note }}</span>
						</div>
					</div>
				</template>
			</el-table-column>
			<el-table-column type="selection" align="center" width="55" />
			<el-table-column prop="id" align="center" label="记录ID" width="80" />
			<el-table-column prop="userId" align="center" label="用户ID" width="100" />
			<el-table-column prop="title" align="center" label="会员名称" width="120" />
			<el-table-column prop="phoneNumber" align="center" label="手机号" width="120" />
			<el-table-column prop="settledCostKey" align="center" label="入驻标识" width="150" />
			<el-table-column align="center" label="创建时间" width="180">
				<template #default="{ row }">
					{{ row.createdTime ? formatDateTime(row.createdTime) : '-' }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="更新时间" width="180">
				<template #default="{ row }">
					{{ row.updatedTime ? formatDateTime(row.updatedTime) : '-' }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="到期时间" width="180">
				<template #default="{ row }">
					{{ formatDateTime(row.expirationDate) }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="佣金设置" width="200">
				<template #default="{ row }">
					{{ getKickbackTypeText(row.kickbackType) }}:
					{{ formatValue(row.kickbackValue, row.kickbackType) }}
				</template>
			</el-table-column>
			<el-table-column align="center" label="资源占用费" width="200">
				<template #default="{ row }">
					{{ getResourceCostTypeText(row.resourceCostType) }}:
					{{ formatValue(row.resourceCostValue, row.resourceCostType) }}
				</template>
			</el-table-column>
			<el-table-column prop="serviceRate" align="center" label="服务费比例" width="120">
				<template #default="{ row }">
					{{ (row.serviceRate / 10000).toFixed(2) }}%
				</template>
			</el-table-column>
			<el-table-column prop="price" align="center" label="购买价格(元)" width="120">
				<template #default="{ row }">
					{{ (row.price / 100).toFixed(2) }}
				</template>
			</el-table-column>
			<el-table-column prop="state" align="center" label="状态" width="100">
				<template #default="{ row }">
					<el-tag :type="getStatusType(row.state)">{{ getStatusText(row.state) }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column align="center" label="操作" fixed="right" width="220">
				<template #default="{ row }">
					<el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
					<el-button 
						size="small" 
						type="danger" 
						@click="handleDelete(row)"
						:disabled="row.state === 1"
					>删除</el-button>
					<el-button 
						v-if="row.state === 0" 
						size="small" 
						type="warning" 
						@click="handleChangeStatus(row, 1)"
						:loading="row.loading"
					>停用</el-button>
					<el-button 
						v-if="row.state === 1" 
						size="small" 
						type="success" 
						@click="handleChangeStatus(row, 0)"
						:loading="row.loading"
					>启用</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-container">
			<el-pagination
				:current-page="pagination.current"
				:page-size="pagination.size"
				:page-sizes="[10, 20, 50, 100]"
				:layout="'total, sizes, prev, pager, next, jumper'"
				:total="pagination.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 新增/编辑对话框 -->
		<el-dialog
			:title="dialogType === 'add' ? '新增入驻会员开通记录' : '编辑入驻会员开通记录'"
			v-model="dialogVisible"
			width="600px"
			:close-on-click-modal="false"
		>
			<el-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				label-width="120px"
				label-position="right"
			>
				<el-form-item label="会员ID" prop="userId">
					<el-input v-model="formData.userId" placeholder="请输入会员ID" />
				</el-form-item>
				<el-form-item label="会员名称" prop="title">
					<el-input v-model="formData.title" placeholder="请输入会员名称" maxlength="20" show-word-limit />
				</el-form-item>
				<el-form-item label="配置选择" prop="settledCostConfigId">
					<el-select v-model="formData.settledCostConfigId" placeholder="请选择配置" @change="handleConfigChange">
						<el-option
							v-for="item in configOptions"
							:key="item.id"
							:label="item.title"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="开通时间" prop="openTime">
					<el-date-picker
						v-model="formData.openTime"
						type="datetime"
						placeholder="选择开通时间"
						value-format="YYYY-MM-DD HH:mm:ss"
					/>
				</el-form-item> -->
				<el-form-item label="到期时间" prop="expirationDate">
					<el-date-picker
						v-model="formData.expirationDate"
						type="datetime"
						placeholder="选择到期时间"
						value-format="YYYY-MM-DD HH:mm:ss"
					/>
				</el-form-item>
				<el-form-item label="支付金额(元)" prop="price">
					<el-input-number 
						v-model="formData.price" 
						:min="0" 
						:precision="2" 
						:step="10"
						placeholder="请输入支付金额"
					/>
				</el-form-item>
				<el-form-item label="佣金类型" prop="kickbackType">
					<el-select v-model="formData.kickbackType" placeholder="请选择佣金类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
						<el-option label="提点/固费" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item 
					v-if="[1, 3].includes(formData.kickbackType)" 
					label="提点比例" 
					prop="kickbackPoint"
				>
					<el-input-number 
						v-model="formData.kickbackPoint" 
						:min="0"
						:precision="2"
						:step="0.01"
						placeholder="请输入提点比例"
						@change="updateKickbackValue"
					/>
					<span class="form-tip">%</span>
				</el-form-item>
				<el-form-item 
					v-if="[2, 3].includes(formData.kickbackType)" 
					label="固定费用" 
					prop="kickbackFixed"
				>
					<el-input-number 
						v-model="formData.kickbackFixed" 
						:min="0"
						:precision="2"
						:step="1"
						placeholder="请输入固定费用"
						@change="updateKickbackValue"
					/>
					<span class="form-tip">分</span>
				</el-form-item>
				<el-form-item label="资源占用费类型" prop="resourceCostType">
					<el-select v-model="formData.resourceCostType" placeholder="请选择资源占用费类型">
						<el-option label="预留" :value="0" />
						<el-option label="提点" :value="1" />
						<el-option label="固定费用" :value="2" />
					</el-select>
				</el-form-item>
				<el-form-item label="资源占用费值" prop="resourceCostValue">
					<el-input-number 
						v-model="formData.resourceCostValue" 
						:min="0"
						:precision="formData.resourceCostType === 1 ? 2 : 0"
						:step="formData.resourceCostType === 1 ? 0.01 : 1"
						placeholder="请输入资源占用费值"
					/>
					<span class="form-tip">{{ formData.resourceCostType === 1 ? '万分比' : '分' }}</span>
				</el-form-item>
				<el-form-item label="服务费比例" prop="serviceRate">
					<el-input-number 
						v-model="formData.serviceRate" 
						:min="0"
						:precision="2"
						:step="0.01"
						placeholder="请输入服务费比例"
					/>
					<span class="form-tip">万分比</span>
				</el-form-item>
				<el-form-item label="会员限制" prop="constraintJson">
					<div class="constraint-inputs">
						<div class="constraint-input-item">
							<span class="constraint-label">最小本数:</span>
							<el-input-number 
								v-model="constraintMin" 
								:min="0"
								:precision="0"
								@change="updateConstraintJson"
							/>
						</div>
						<div class="constraint-input-item">
							<span class="constraint-label">最大本数:</span>
							<el-input-number 
								v-model="constraintMax" 
								:min="0"
								:precision="0"
								@change="updateConstraintJson"
							/>
						</div>
					</div>
				</el-form-item>
				<el-form-item label="备注" prop="note">
					<el-input
						v-model="formData.note"
						type="textarea"
						:rows="3"
						placeholder="请输入备注信息"
					/>
				</el-form-item>
				<el-form-item label="状态" prop="state">
					<el-select v-model="formData.state" placeholder="请选择状态">
						<el-option label="已启用" :value="0" />
						<el-option label="已停用" :value="1" />
					</el-select>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { settledMemberRecordApi } from '@/api/modules/settledMemberRecord'
import { settledCostConfigApi } from '@/api/modules/settledCostConfig'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import RefreshButton from '@/components/RefreshButton.vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableRef = ref(null)

// 多选数据
const multipleSelection = ref([])

// 搜索表单
const searchForm = reactive({
	userId: '',
	memberName: '',
	phoneNumber: '',
	timeRange: []
})

// 日期快捷选项
const dateShortcuts = [
	{
		text: '最近一周',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
			return [start, end]
		},
	},
	{
		text: '最近一个月',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
			return [start, end]
		},
	},
	{
		text: '最近三个月',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
			return [start, end]
		},
	},
]

// 分页配置
const pagination = reactive({
	current: 1,
	size: 20, // 默认每页20条
	total: 0
})

// 入驻配置选项
const configOptions = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
	id: null,
	userId: '',
	settledCostConfigId: null,
	title: '',
	settledCostKey: '',
	constraintJson: '{"books_count_min":0,"books_count_max":3000}',
	expirationDate: '',
	openTime: '',
	kickbackType: 1,
	kickbackValue: 0,
	kickbackPoint: 0,
	kickbackFixed: 0,
	resourceCostType: 1,
	resourceCostValue: 0,
	serviceRate: 0,
	price: 0,
	state: 0,
	note: '',
	createdBy: 0,
	updatedBy: 0
})

// 表单验证规则
const formRules = {
	userId: [
		{ required: true, message: '请输入用户ID', trigger: 'blur' },
	],
	settledCostConfigId: [
		{ required: true, message: '请选择配置', trigger: 'change' },
	],
	title: [
		{ required: true, message: '请输入标题', trigger: 'blur' },
		{ max: 20, message: '标题长度不能超过20个字符', trigger: 'blur' }
	],
	expirationDate: [
		{ required: true, message: '请选择到期时间', trigger: 'change' },
	],
	kickbackType: [
		{ required: true, message: '请选择佣金类型', trigger: 'change' },
	],
	kickbackValue: [
		{ required: true, message: '请输入佣金值', trigger: 'blur' },
	],
	resourceCostType: [
		{ required: true, message: '请选择资源占用费类型', trigger: 'change' },
	],
	resourceCostValue: [
		{ required: true, message: '请输入资源占用费值', trigger: 'blur' },
	],
	serviceRate: [
		{ required: true, message: '请输入服务费比例', trigger: 'blur' },
	],
	price: [
		{ required: true, message: '请输入购买价格', trigger: 'blur' },
	],
	state: [
		{ required: true, message: '请选择状态', trigger: 'change' },
	]
}

// 约束条件编辑字段
const constraintMin = ref(0)
const constraintMax = ref(3000)

// 监听约束值变化
const updateConstraintJson = () => {
	formData.constraintJson = JSON.stringify({
		books_count_min: constraintMin.value,
		books_count_max: constraintMax.value
	})
}

// 监听constraintJson变化，更新约束值
watch(() => formData.constraintJson, (newVal) => {
	if (newVal) {
		try {
			const constraint = JSON.parse(newVal)
			constraintMin.value = constraint.books_count_min !== undefined ? constraint.books_count_min : 0
			constraintMax.value = constraint.books_count_max !== undefined ? constraint.books_count_max : 3000
		} catch (e) {
			console.error('解析约束条件失败:', e)
		}
	}
}, { immediate: true })

// 初始化
onMounted(() => {
	fetchData()
	// fetchConfigOptions()
})

// 获取入驻配置选项
const fetchConfigOptions = async () => {
	try {
		const res = await settledCostConfigApi.getSettledCostConfigList()
		console.log("res",res)
		// API返回的数据已经是驼峰命名，直接使用
		configOptions.value = res.data || []
	} catch (error) {
		console.error('获取配置选项失败:', error)
		ElMessage.error('获取配置选项失败')
	}
}

// 获取表格数据
const fetchData = async () => {
	loading.value = true
	try {
		// 在请求前再次确认页码
		console.log('fetchData中的当前页码:', pagination.current);
		
		// 构建请求参数，符合PageQueryRequest格式
		const params = {
			pageNum: pagination.current,     // 使用pageNum作为页码参数
			pageSize: pagination.size,       // 使用pageSize作为每页数量参数
			// 查询条件参数
			userId: searchForm.userId || undefined,
			memberName: searchForm.memberName || undefined,
			phoneNumber: searchForm.phoneNumber || undefined
		}
		
		// 添加时间范围参数
		if (searchForm.timeRange && searchForm.timeRange.length === 2) {
			params.startTime = searchForm.timeRange[0]
			params.endTime = searchForm.timeRange[1]
		}
		
		console.log('最终发送的分页请求参数:', JSON.stringify(params));  // 使用JSON.stringify确保完整输出
		const res = await settledMemberRecordApi.getSettledMemberRecordlistWithDetails(params)
		console.log('获取分页数据响应:', res);
		
		if (res.code === 200 && res.data) {
			tableData.value = res.data.list || []
			pagination.total = res.data.total || 0
		}
	} catch (error) {
		console.error('获取数据失败:', error)
		ElMessage.error('获取数据失败')
	} finally {
		loading.value = false
	}
}

// 刷新数据
const refreshData = () => {
	fetchData()
}

// 处理搜索
const handleSearch = () => {
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 重置搜索
const resetSearch = () => {
	searchForm.userId = ''
	searchForm.memberName = ''
	searchForm.phoneNumber = ''
	searchForm.timeRange = []
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 分页大小变化
const handleSizeChange = (size) => {
	console.log('改变每页显示数量:', size);
	pagination.size = size
	pagination.current = 1 // 切换每页条数时，重置到第一页
	fetchData()
}

// 页码变化
const handleCurrentChange = (current) => {
	console.log('改变页码，值为:', current);
	// 确保更新当前页码
	pagination.current = current;
	// 使用setTimeout确保状态更新后再发送请求
	setTimeout(() => {
		console.log('即将请求的页码:', pagination.current);
		fetchData();
	}, 0);
}

// 多选处理
const handleSelectionChange = (val) => {
	multipleSelection.value = val
}

// 编辑
const handleEdit = (row) => {
	resetForm()
	dialogType.value = 'edit'
	// 直接使用行数据
	Object.assign(formData, row)
	// 转换为元
	formData.price = row.price / 100
	
	// 解析佣金值
	try {
		if (typeof row.kickbackValue === 'string' && row.kickbackValue.includes('{')) {
			const kickbackData = JSON.parse(row.kickbackValue)
			formData.kickbackPoint = kickbackData.point / 10000 || 0 // 万分比转为百分比
			formData.kickbackFixed = kickbackData.fixed || 0 // 保持分作为单位
		} else {
			// 兼容旧数据
			if (row.kickbackType === 1) {
				formData.kickbackPoint = parseFloat(row.kickbackValue) / 10000 || 0 // 万分比转为百分比
				formData.kickbackFixed = 0
			} else if (row.kickbackType === 2) {
				formData.kickbackPoint = 0
				formData.kickbackFixed = parseFloat(row.kickbackValue) || 0 // 保持分作为单位
			} else if (row.kickbackType === 3) {
				formData.kickbackPoint = 0
				formData.kickbackFixed = 0
			}
		}
	} catch (e) {
		console.error('解析佣金值失败:', e)
		formData.kickbackPoint = 0
		formData.kickbackFixed = 0
	}
	
	// 解析约束条件
	try {
		if (row.constraintJson) {
			const constraint = JSON.parse(row.constraintJson)
			constraintMin.value = constraint.books_count_min !== undefined ? constraint.books_count_min : 0
			constraintMax.value = constraint.books_count_max !== undefined ? constraint.books_count_max : 3000
		}
	} catch (e) {
		console.error('解析约束条件失败:', e)
	}
	
	dialogVisible.value = true
}

// 删除
const handleDelete = (row) => {
	ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			await settledMemberRecordApi.deleteSettledMemberRecord(row.id)
			ElMessage.success('删除成功')
			fetchData()
		} catch (error) {
			console.error('删除失败:', error)
			ElMessage.error('删除失败')
		}
	}).catch(() => {})
}

// 改变状态
const handleChangeStatus = async (row, newState) => {
	// 设置当前行的loading状态
	row.loading = true
	
	const stateTip = newState === 0 ? '启用' : '停用'
	ElMessageBox.confirm(`确定要${stateTip}该记录吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			await settledMemberRecordApi.updateMemberRecordStatus(row.id, newState)
			ElMessage.success(`${stateTip}成功`)
			fetchData() // 刷新数据
		} catch (error) {
			console.error(`${stateTip}失败:`, error)
			ElMessage.error(`${stateTip}失败: ${error.message || '未知错误'}`)
		} finally {
			row.loading = false
		}
	}).catch(() => {
		// 取消操作时也需要重置loading状态
		row.loading = false
	})
}

// 处理配置改变
const handleConfigChange = (configId) => {
	const selectedConfig = configOptions.value.find(item => item.id === configId)
	if (selectedConfig) {
		formData.title = selectedConfig.title
		formData.settledCostKey = selectedConfig.settledCostKey
		formData.price = selectedConfig.price / 100 // 转换为元
		// 同步限制条件
		if (selectedConfig.constraintJson) {
			formData.constraintJson = selectedConfig.constraintJson
		}
	}
}

// 处理提交表单
const handleSubmit = async () => {
	if (!formRef.value) return
	
	await formRef.value.validate(async (valid) => {
		if (valid) {
			submitLoading.value = true
			
			try {
				// 确保佣金值已更新
				updateKickbackValue()
				
				// 创建提交数据对象 - 严格按照MemberRecordDTO的字段类型
				const submitData = {
					id: Number(formData.id), // Long -> Number
					userId: Number(formData.userId), // Long -> Number（不是String）
					settledCostConfigId: Number(formData.settledCostConfigId), // Long -> Number
					title: formData.title?.trim() || '',
					settledCostKey: formData.settledCostKey?.trim() || '',
					constraintJson: formData.constraintJson,
					expirationDate: new Date(formData.expirationDate).getTime(), // 转换为时间戳
					kickbackType: Number(formData.kickbackType),
					kickbackValue: formData.kickbackValue, // 已经是JSON字符串
					resourceCostType: Number(formData.resourceCostType),
					resourceCostValue: Number(Math.round(formData.resourceCostValue)),
					serviceRate: Number(Math.round(formData.serviceRate)),
					price: Number(Math.round(formData.price * 100)),
					state: Number(formData.state),
					note: formData.note?.trim() || ''
				}
				
				// 调试信息
				console.log('提交数据(完整):', JSON.stringify(submitData, null, 2))
				console.log('kickbackValue类型:', typeof submitData.kickbackValue)
				
				const response = await settledMemberRecordApi.updateSettledMemberRecord(submitData)
				console.log('更新响应:', response)
				ElMessage.success('更新成功')
				
				dialogVisible.value = false
				fetchData()
			} catch (apiError) {
				console.error('API错误:', apiError)
				
				// 详细打印错误信息
				if (apiError.response?.data) {
					console.error('错误响应数据:', apiError.response.data)
					const errorData = apiError.response.data
					let errorMsg = '提交失败'
					
					if (typeof errorData === 'string') {
						errorMsg = errorData
					} else if (errorData.message) {
						errorMsg = errorData.message
					} else if (errorData.error) {
						errorMsg = errorData.error
					}
					
					ElMessage.error(errorMsg)
				} else {
					ElMessage.error('提交失败: ' + (apiError.message || '未知错误'))
				}
			} finally {
				submitLoading.value = false
			}
		}
	})
}

// 重置表单
const resetForm = () => {
	if (formRef.value) {
		formRef.value.resetFields()
	}
	Object.assign(formData, {
		id: null,
		userId: '',
		settledCostConfigId: null,
		title: '',
		settledCostKey: '',
		constraintJson: '{"books_count_min":0,"books_count_max":3000}',
		expirationDate: '',
		openTime: '',
		kickbackType: 1,
		kickbackValue: 0,
		kickbackPoint: 0,
		kickbackFixed: 0,
		resourceCostType: 1,
		resourceCostValue: 0,
		serviceRate: 0,
		price: 0,
		state: 0,
		note: '',
		createdBy: 0,
		updatedBy: 0
	})
	// 重置约束值
	constraintMin.value = 0
	constraintMax.value = 3000
}

// 格式化时间
const formatDateTime = (timestamp) => {
	if (!timestamp) return '-'
	const date = new Date(timestamp)
	return date.toLocaleString()
}

// 获取状态类型
const getStatusType = (state) => {
	const map = {
		0: 'success',
		1: 'danger'
	}
	return map[state] || 'info'
}

// 获取状态文本
const getStatusText = (state) => {
	const map = {
		0: '已启用',
		1: '已停用'
	}
	return map[state] || '未知'
}

// 获取佣金类型文本
const getKickbackTypeText = (type) => {
	const map = {
		0: '预留',
		1: '提点',
		2: '固定费用',
		3: '提点/固费'
	}
	return map[type] || '未知'
}

// 获取资源占用费类型文本
const getResourceCostTypeText = (type) => {
	const map = {
		0: '预留',
		1: '提点',
		2: '固定费用'
	}
	return map[type] || '未知'
}

// 格式化值（根据类型）
const formatValue = (value, type) => {
	if (type === 1) {
		// 提点 - 显示为百分比
		try {
			if (typeof value === 'string' && value.includes('{')) {
				const data = JSON.parse(value);
				return (data.point / 10000).toFixed(2) + '%';
			}
		} catch (e) {
			console.error('解析佣金值失败:', e);
		}
		return (value / 10000).toFixed(2) + '%';
	} else if (type === 2) {
		// 固定费用 - 显示为元
		try {
			if (typeof value === 'string' && value.includes('{')) {
				const data = JSON.parse(value);
				return (data.fixed / 100).toFixed(2) + '元'; // 从分转为元显示
			}
		} catch (e) {
			console.error('解析佣金值失败:', e);
		}
		return (value / 100).toFixed(2) + '元'; // 从分转为元显示
	} else if (type === 3) {
		// 提点/固费模式
		try {
			if (typeof value === 'string' && value.includes('{')) {
				const data = JSON.parse(value);
				return `提点: ${(data.point / 10000).toFixed(2)}%, 固定: ${(data.fixed / 100).toFixed(2)}元`;
			}
		} catch (e) {
			console.error('解析佣金值失败:', e);
		}
	}
	return value;
}

// 获取约束值
const getConstraintValue = (jsonStr, key, defaultValue) => {
	try {
		const constraint = JSON.parse(jsonStr)
		return constraint[key] !== undefined ? constraint[key] : defaultValue
	} catch (e) {
		console.error('解析约束条件失败:', e)
		return defaultValue
	}
}

// 更新佣金值
const updateKickbackValue = () => {
	try {
		// 对所有类型统一使用JSON格式
		let kickbackObj = {}
		
		if (formData.kickbackType === 1) {
			// 提点模式
			kickbackObj = {
				point: Math.round(formData.kickbackPoint * 10000),
				fixed: 0
			}
		} else if (formData.kickbackType === 2) {
			// 固定费用模式 - 直接使用formData.kickbackFixed，已经是分单位
			kickbackObj = {
				point: 0,
				fixed: Math.round(formData.kickbackFixed)
			}
		} else if (formData.kickbackType === 3) {
			// 提点/固费混合模式 - fixed直接使用分单位
			kickbackObj = {
				point: Math.round(formData.kickbackPoint * 10000),
				fixed: Math.round(formData.kickbackFixed)
			}
		} else {
			// 预留模式或其他情况
			kickbackObj = {
				point: 0,
				fixed: 0
			}
		}
		
		// 转换为JSON字符串
		formData.kickbackValue = JSON.stringify(kickbackObj)
		console.log('更新后的佣金值:', formData.kickbackValue, '类型:', typeof formData.kickbackValue)
	} catch (e) {
		console.error('更新佣金值失败:', e)
		formData.kickbackValue = JSON.stringify({point: 0, fixed: 0})
	}
}

// 监听佣金类型变化
watch(() => formData.kickbackType, () => {
	updateKickbackValue()
})
</script>

<style scoped>
.list-container {
	padding: 20px;
}

.search-area {
	margin-bottom: 20px;
	padding: 18px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	text-align: left;
}

.search-area :deep(.el-form-item) {
	margin-right: 18px;
	margin-bottom: 10px;
}

.action-bar {
	margin-bottom: 20px;
	display: flex;
	justify-content: flex-start;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.expand-content {
	padding: 12px 20px;
}

.expand-item {
	margin: 8px 0;
	display: flex;
	align-items: flex-start;
}

.expand-label {
	font-weight: bold;
	margin-right: 10px;
	min-width: 80px;
}

.form-tip {
	margin-left: 8px;
	color: #909399;
	font-size: 14px;
}

.constraint-inputs {
	display: flex;
	gap: 20px;
	align-items: center;
}

.constraint-input-item {
	display: flex;
	align-items: center;
}

.constraint-label {
	margin-right: 8px;
}
</style> 