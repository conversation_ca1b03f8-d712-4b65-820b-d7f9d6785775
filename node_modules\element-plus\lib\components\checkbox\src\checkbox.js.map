{"version": 3, "file": "checkbox.js", "sources": ["../../../../../../packages/components/checkbox/src/checkbox.vue"], "sourcesContent": ["<template>\n  <component\n    :is=\"!hasOwnLabel && isLabeledByFormItem ? 'span' : 'label'\"\n    :class=\"compKls\"\n    :aria-controls=\"indeterminate ? ariaControls : null\"\n    @click=\"onClickRoot\"\n  >\n    <span :class=\"spanKls\">\n      <input\n        v-if=\"trueValue || falseValue || trueLabel || falseLabel\"\n        :id=\"inputId\"\n        v-model=\"model\"\n        :class=\"ns.e('original')\"\n        type=\"checkbox\"\n        :indeterminate=\"indeterminate\"\n        :name=\"name\"\n        :tabindex=\"tabindex\"\n        :disabled=\"isDisabled\"\n        :true-value=\"trueValue ?? trueLabel ?? true\"\n        :false-value=\"falseValue ?? falseLabel ?? false\"\n        @change=\"handleChange\"\n        @focus=\"isFocused = true\"\n        @blur=\"isFocused = false\"\n        @click.stop\n      />\n      <input\n        v-else\n        :id=\"inputId\"\n        v-model=\"model\"\n        :class=\"ns.e('original')\"\n        type=\"checkbox\"\n        :indeterminate=\"indeterminate\"\n        :disabled=\"isDisabled\"\n        :value=\"actualValue\"\n        :name=\"name\"\n        :tabindex=\"tabindex\"\n        @change=\"handleChange\"\n        @focus=\"isFocused = true\"\n        @blur=\"isFocused = false\"\n        @click.stop\n      />\n      <span :class=\"ns.e('inner')\" />\n    </span>\n    <span v-if=\"hasOwnLabel\" :class=\"ns.e('label')\">\n      <slot />\n      <template v-if=\"!$slots.default\">{{ label }}</template>\n    </span>\n  </component>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, useSlots } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { checkboxEmits, checkboxProps } from './checkbox'\nimport { useCheckbox } from './composables'\n\ndefineOptions({\n  name: 'ElCheckbox',\n})\n\nconst props = defineProps(checkboxProps)\ndefineEmits(checkboxEmits)\nconst slots = useSlots()\n\nconst {\n  inputId,\n  isLabeledByFormItem,\n  isChecked,\n  isDisabled,\n  isFocused,\n  checkboxSize,\n  hasOwnLabel,\n  model,\n  actualValue,\n  handleChange,\n  onClickRoot,\n} = useCheckbox(props, slots)\n\nconst ns = useNamespace('checkbox')\n\nconst compKls = computed(() => {\n  return [\n    ns.b(),\n    ns.m(checkboxSize.value),\n    ns.is('disabled', isDisabled.value),\n    ns.is('bordered', props.border),\n    ns.is('checked', isChecked.value),\n  ]\n})\n\nconst spanKls = computed(() => {\n  return [\n    ns.e('input'),\n    ns.is('disabled', isDisabled.value),\n    ns.is('checked', isChecked.value),\n    ns.is('indeterminate', props.indeterminate),\n    ns.is('focus', isFocused.value),\n  ]\n})\n</script>\n"], "names": ["useSlots", "useCheckbox", "useNamespace", "computed"], "mappings": ";;;;;;;;;;uCAwDc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA;AAAA,MACJ,OAAA;AAAA,MACA,mBAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,SAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,KAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,KACF,GAAIC,uBAAY,CAAA,KAAA,EAAO,KAAK,CAAA,CAAA;AAE5B,IAAM,MAAA,EAAA,GAAKC,mBAAa,UAAU,CAAA,CAAA;AAElC,IAAM,MAAA,OAAA,GAAUC,aAAS,MAAM;AAC7B,MAAO,OAAA;AAAA,QACL,GAAG,CAAE,EAAA;AAAA,QACL,EAAA,CAAG,CAAE,CAAA,YAAA,CAAa,KAAK,CAAA;AAAA,QACvB,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,UAAA,CAAW,KAAK,CAAA;AAAA,QAClC,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA,QAC9B,EAAG,CAAA,EAAA,CAAG,SAAW,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,OAClC,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAUA,aAAS,MAAM;AAC7B,MAAO,OAAA;AAAA,QACL,EAAA,CAAG,EAAE,OAAO,CAAA;AAAA,QACZ,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,UAAA,CAAW,KAAK,CAAA;AAAA,QAClC,EAAG,CAAA,EAAA,CAAG,SAAW,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,QAChC,EAAG,CAAA,EAAA,CAAG,eAAiB,EAAA,KAAA,CAAM,aAAa,CAAA;AAAA,QAC1C,EAAG,CAAA,EAAA,CAAG,OAAS,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,OAChC,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}