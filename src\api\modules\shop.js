import instance from '../../utils/axios.js'

// 获取店铺列表
export const getShopList = async (params = {}) => {
  // 构造查询参数
  const queryParams = new URLSearchParams();
  
  // 添加分页参数
  if (params.pageNum) queryParams.append('pageNum', params.pageNum);
  if (params.pageSize) queryParams.append('pageSize', params.pageSize);
  
  // 添加搜索参数
  if (params.shopType) queryParams.append('shopType', params.shopType);
  if (params.shopGroup) queryParams.append('shopGroup', params.shopGroup);
  if (params.shopName) queryParams.append('shopName', params.shopName);
  if (params.shopAliasName) queryParams.append('shopAliasName', params.shopAliasName);
  if (params.shopAuthorize) queryParams.append('shopAuthorize', params.shopAuthorize);
  if (params.status) queryParams.append('status', params.status);
  
  const url = `/shop/list?${queryParams.toString()}`;
  return instance.get(url);
};

// 获取店铺详情
export const getShopDetail = (id) => {
  return instance.get(`/shop/${id}`);
};

// 新增店铺
export const addShop = (data) => {
  return instance.post('/shop', data);
};

// 修改店铺
export const updateShop = (data) => {
  return instance.put('/shop', data);
};

// 删除店铺
export const deleteShop = (id) => {
  return instance.delete(`/shop/${id}`);
};

// 批量删除店铺
export const batchDeleteShop = (ids) => {
  return instance.delete(`/shop/batch/${ids}`);
};

// 更新同步订单状态
export const updateSyncOrderStatus = (id, isSynOrder) => {
  return instance.put('/shop/syncOrder', {
    id,
    isSynOrder
  });
};

// 为了向后兼容，也导出整个对象
export const shopApi = {
  getShopList,
  getShopDetail,
  addShop,
  updateShop,
  deleteShop,
  batchDeleteShop,
  updateSyncOrderStatus
};
