<template>
	<el-container class="layout-container">
		<!-- 顶部导航 -->
		<el-header height="60px"><navbar /></el-header>
		<el-container>
			<!-- 侧边栏 -->
			<el-aside width="220px"><sidebar /></el-aside>
			<!-- 标签页 -->
			<el-main style="padding: 5px;">
				<router-view />
			</el-main>
		</el-container>
	</el-container>
</template>

<script setup lang="ts">
	import Navbar from './Navbar.vue'
	import Sidebar from './Sidebar.vue'
</script>

<style scoped>
	.layout-container {
		height: 100vh;
	}

	.el-header {
		border-bottom: 1px solid #eee;
		display: flex;
		align-items: center;
	}
</style>