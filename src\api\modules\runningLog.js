import instance from '../../utils/axios.js'

const runningApi = {
  // 获取查询参数与分页参数
  pageQuery: (params) => {
    // 转换分页参数名称
    const convertedParams = {
      ...params,
      pageNum: params.page,
      pageSize: params.size
    }
    // 删除旧的参数
    delete convertedParams.page
    delete convertedParams.size
    
    return instance.get('/runningLog/pageQuery', { 
      params: convertedParams
    })
  },
  viewLog: (fileName) => {
    return instance.get('/runningLog/viewLog?fileName='+fileName);
  }
}

export { runningApi } 