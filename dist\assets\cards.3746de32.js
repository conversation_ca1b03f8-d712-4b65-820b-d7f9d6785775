var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,d=(r,a,t)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t;import{i as c}from"./index.1c8cd61b.js";const i={pageQueryCard:e=>{const i=(p=((e,r)=>{for(var a in r||(r={}))o.call(r,a)&&d(e,a,r[a]);if(t)for(var a of t(r))s.call(r,a)&&d(e,a,r[a]);return e})({},e),n={pageNum:e.page,pageSize:e.size},r(p,a(n)));var p,n;return delete i.page,delete i.size,console.log("卡密查询参数:",i),c.get("/cards/pageQueryCard",{params:i,paramsSerializer:e=>Object.entries(e).filter((([e,r])=>void 0!==r)).map((([e,r])=>`${e}=${encodeURIComponent(r)}`)).join("&")})},deleteCard:e=>c.post("/cards/deleteCard",{id:e}),disableCard:e=>c.post("/cards/disableCard",{id:e}),enableCard:e=>c.post("/cards/enableCard",{id:e}),createCardSecret:e=>c.post("/cards/createCardSecret",e),batchCreateCards:e=>c.post("/cards/batchCreate",e),updateCardStatus:e=>c.put("/cards/updateStatus",e),getActiveCardsPage:e=>{const r={pageNum:e.current,pageSize:e.size};return delete r.current,delete r.size,console.log("活跃卡密查询参数:",r),c.get("/verifyPrice/getActiveCardsPage",{params:r,paramsSerializer:e=>Object.entries(e).filter((([e,r])=>void 0!==r)).map((([e,r])=>`${e}=${encodeURIComponent(r)}`)).join("&")})}};export{i as c};
