<template>
  <div class="invitation-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>邀请码管理</span>
        </div>
      </template>
      
      <el-form :inline="true" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="searchParams.userId" placeholder="请输入用户ID" clearable />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input v-model="searchParams.phonenumber" placeholder="请输入手机号码" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table :data="inviteCodes" stripe style="width: 100%" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="70" align="center" />
        <el-table-column prop="userId" label="用户ID" min-width="180" align="center" />
        <el-table-column prop="phonenumber" label="手机号码" min-width="120" align="center" />
        <el-table-column prop="code" label="邀请码" width="120" align="center" />
        <el-table-column label="邀请链接" min-width="300" align="center">
          <template #default="scope">
            <div class="invite-url-container">
              <el-input 
                v-model="scope.row.inviteUrl" 
                readonly 
                size="small" 
                class="invite-url-input"
              />
              <el-button 
                type="primary" 
                size="small" 
                @click="copyInviteUrl(scope.row.inviteUrl)"
                class="copy-btn"
              >
                复制
              </el-button>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="过期时间" width="160" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.expireTime) }}
          </template>
        </el-table-column>
        <el-table-column label="是否已使用" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.used ? 'success' : 'info'" size="small">
              {{ scope.row.used ? '已使用' : '未使用' }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="创建时间" width="160" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small"
              @click="viewInvitees(scope.row.userId)"
            >
              查看被邀请人
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 被邀请人弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="`邀请总数: ${invitees.length}人`" 
      width="50%"
    >
      <el-table :data="invitees" stripe style="width: 100%" v-loading="inviteesLoading" border>
        <el-table-column prop="phonenumber" label="手机号码" min-width="150" align="center" />
        <el-table-column label="邀请时间" min-width="150" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.inviteTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { invitationApi } from '@/api'
import { Document } from '@element-plus/icons-vue'

interface InviteCode {
  id: number
  userId: string
  code: string
  inviteUrl: string
  expireTime: string
  used: boolean
  createdAt: string
  tenantId: number | null
  phonenumber?: string
}

interface InviteRelation {
  id: number
  inviterId: string
  inviteeId: string
  phonenumber: string
  inviteCode: string
  inviteTime: string
}

const inviteCodes = ref<InviteCode[]>([])
const invitees = ref<InviteRelation[]>([])
const loading = ref(false)
const inviteesLoading = ref(false)
const dialogVisible = ref(false)
const currentInviterId = ref<string | null>(null)
const total = ref(0)
const pageSize = ref(10)
const currentPage = ref(1)

const searchParams = reactive({
  userId: '',
  phonenumber: ''
})

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
}

// 获取邀请码列表
const getInviteCodes = async () => {
  loading.value = true
  try {
    // 检查API是否需要参数
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
    
    // 只有当有搜索条件时才添加
    if (searchParams.userId) {
      params.userId = searchParams.userId
    }
    if (searchParams.phonenumber) {
      params.phonenumber = searchParams.phonenumber
    }
    
    const response = await invitationApi.getInviteCodes(params)
    
    if (response && response.data && response.code === 200) {
      // 直接使用标准返回格式
      inviteCodes.value = response.data.list || []
      total.value = response.data.total || 0
      pageSize.value = response.data.pageSize || 10
      currentPage.value = response.data.pageNum || 1
      
      console.log('设置到inviteCodes的数据:', inviteCodes.value)
    } else {
      console.error('无法解析API返回数据:', response)
      ElMessage.error('获取邀请码列表失败')
      inviteCodes.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取邀请码列表出错:', error)
    ElMessage.error('获取邀请码列表出错')
    inviteCodes.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取被邀请人
const viewInvitees = async (inviterId: string) => {
  console.log('inviterId:', inviterId)
  // userId已经是字符串类型，不需要转换
  currentInviterId.value = inviterId
  dialogVisible.value = true
  inviteesLoading.value = true
  
  try {
    // 调用API - 直接传递字符串参数，不要包装在对象中
    const response = await invitationApi.getInviteesByInviter(inviterId)
    
    if (response && response.data && response.code === 200) {
      invitees.value = response.data || []
      console.log('处理后的被邀请人数据:', invitees.value)
      
      if (invitees.value.length === 0) {
        ElMessage.info('该用户暂无邀请记录')
      }
    } else {
      console.warn('获取被邀请人列表返回异常结构:', response)
      invitees.value = []
    }
  } catch (error) {
    console.error('获取被邀请人列表出错:', error)
    invitees.value = []
  } finally {
    inviteesLoading.value = false
  }
}

// 复制邀请链接
const copyInviteUrl = (url: string) => {
  navigator.clipboard.writeText(url)
    .then(() => {
      ElMessage.success('邀请链接已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

const handleSearch = () => {
  currentPage.value = 1
  getInviteCodes()
}

const resetSearch = () => {
  searchParams.userId = ''
  searchParams.phonenumber = ''
  currentPage.value = 1
  getInviteCodes()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getInviteCodes()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getInviteCodes()
}

onMounted(() => {
  getInviteCodes()
})
</script>

<style scoped>
.invitation-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-form {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.ellipsis-text {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.invite-url-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.invite-url-input {
  flex: 1;
}
.copy-btn {
  margin-left: 8px;
  white-space: nowrap;
}
</style> 