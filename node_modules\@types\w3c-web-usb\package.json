{"name": "@types/w3c-web-usb", "version": "1.0.10", "description": "TypeScript definitions for w3c-web-usb", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/w3c-web-usb", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "larsgk", "url": "https://github.com/larsgk"}, {"name": "<PERSON>", "githubUsername": "thegecko", "url": "https://github.com/thegecko"}, {"name": "<PERSON>", "githubUsername": "yume-chan", "url": "https://github.com/yume-chan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/w3c-web-usb"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "02b64f2407b53cf7e130a1ac682ed7a131d9d3f130ae2765ef20e3a6c5df03c5", "typeScriptVersion": "4.5", "nonNpm": true}