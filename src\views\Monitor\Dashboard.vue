<template>
    <div class="monitor-dashboard-wrapper">
        <!-- 嵌入Go监控系统的原生页面 -->
        <iframe ref="monitorFrame" :src="monitorUrl" class="monitor-iframe" frameborder="0"
            @load="onFrameLoad"></iframe>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
            <div class="loading-spinner">
                <i class="el-icon-loading"></i>
            </div>
            <p>正在加载监控大屏...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-overlay">
            <el-icon>
                <Warning />
            </el-icon>
            <p>{{ error }}</p>
            <el-button @click="reload" type="primary">重新加载</el-button>
        </div>
    </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { Warning } from '@element-plus/icons-vue'

export default {
    name: 'MonitorDashboard',
    components: {
        Warning
    },
    setup() {
        const monitorFrame = ref(null)
        const loading = ref(true)
        const error = ref('')
        const monitorUrl = ref('http://**************') // Go监控服务地址

        // 检查监控服务是否可用
        const checkMonitorService = async () => {
            try {
                const response = await fetch(`${monitorUrl.value}/api/v1/health`)
                if (!response.ok) {
                    throw new Error('监控服务不可用')
                }
                return true
            } catch (err) {
                error.value = '无法连接到监控服务，请确保Go监控服务已启动'
                loading.value = false
                return false
            }
        }

        const onFrameLoad = () => {
            loading.value = false
            error.value = ''
        }

        const reload = () => {
            loading.value = true
            error.value = ''
            if (monitorFrame.value) {
                monitorFrame.value.src = monitorUrl.value
            }
        }

        onMounted(async () => {
            // 检查监控服务
            const isAvailable = await checkMonitorService()
            if (!isAvailable) {
                return
            }

            // 设置iframe源
            if (monitorFrame.value) {
                monitorFrame.value.src = monitorUrl.value
            }
        })

        return {
            monitorFrame,
            monitorUrl,
            loading,
            error,
            onFrameLoad,
            reload
        }
    }
}
</script>

<style scoped>
.monitor-dashboard-wrapper {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.monitor-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.loading-overlay,
.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    z-index: 1000;
}

.loading-overlay p,
.error-overlay p {
    margin-top: 20px;
    font-size: 16px;
}

.error-overlay .el-icon {
    font-size: 48px;
    color: #f56c6c;
}

.error-overlay .el-button {
    margin-top: 20px;
}

.loading-spinner {
    font-size: 32px;
    animation: rotate 2s linear infinite;
}

.loading-spinner i {
    color: #409EFF;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>