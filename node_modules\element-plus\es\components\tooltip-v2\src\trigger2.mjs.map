{"version": 3, "file": "trigger2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/trigger.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\nconst EventHandler = {\n  type: definePropType<(e: Event) => boolean | void>(Function),\n} as const\n\nexport const tooltipV2TriggerProps = buildProps({\n  onBlur: EventHandler,\n  onClick: EventHandler,\n  onFocus: EventHandler,\n  onMouseDown: EventHandler,\n  onMouseEnter: EventHandler,\n  onMouseLeave: EventHandler,\n} as const)\n\nexport type TooltipV2TriggerProps = ExtractPropTypes<\n  typeof tooltipV2TriggerProps\n>\n"], "names": [], "mappings": ";;AACA,MAAM,YAAY,GAAG;AACrB,EAAE,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAChC,CAAC,CAAC;AACU,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,MAAM,EAAE,YAAY;AACtB,EAAE,OAAO,EAAE,YAAY;AACvB,EAAE,OAAO,EAAE,YAAY;AACvB,EAAE,WAAW,EAAE,YAAY;AAC3B,EAAE,YAAY,EAAE,YAAY;AAC5B,EAAE,YAAY,EAAE,YAAY;AAC5B,CAAC;;;;"}