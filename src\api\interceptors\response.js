import axios from 'axios'; // 引入axios用于创建新请求
import { ElMessage, ElMessageBox } from 'element-plus'; // 引入Element Plus的消息组件
import store from '../../store/Index'; // 引入store用于统一处理登出
import axiosInstance from '../../utils/axios'; // 引入已配置的axios实例

export function setupResponseInterceptors(instance) {
  let isRefreshing = false;
  let requests = [];

  const handleTokenExpired = (message = '登录已过期，请重新登录') => {
    ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '重新登录',
      type: 'warning',
      showCancelButton: false,
      center: true
    }).then(() => {
      store.dispatch('logout');
      window.location.href = '/login';
    });
  };

  instance.interceptors.response.use(
    response => {
      const { data } = response;
      if (data.code != 200) {
        const error = new Error(data.message || '业务逻辑错误');
        error.name = 'BusinessError';
        error.data = data;
        error.code = data.code;
        return Promise.reject(error);
      }
      return data;
    },
    async error => {
      const originalRequest = error.config;

      if (!error.response) {
        return Promise.reject(error);
      }

      const statusCode = error.response.status;
      console.log("statusCode", statusCode)
      // 401表示accessToken过期，尝试用refreshToken刷新
      if ((statusCode === 401 || statusCode === 500) && !originalRequest._retry) {
        originalRequest._retry = true;
        console.log("isRefreshing", isRefreshing)
        if (!isRefreshing) {
          isRefreshing = true;

          try {
            const refreshToken = localStorage.getItem('refreshToken');
            if (!refreshToken) {
              handleTokenExpired();
              return Promise.reject(error);
            }

            // 调用刷新接口
            const formData = new FormData();
            formData.append('refreshToken', refreshToken);

            const response = await axiosInstance.post('/admin/refreshToken', formData);
            console.log('刷新token响应:', response);

            // 后端返回格式: { code: 200, data: { accessToken: "xxx", refreshToken: "xxx" } }
            // 由于axios拦截器已经处理了response.data，所以这里直接使用response.data
            const responseData = response.data || response;
            console.log('响应数据:', responseData);

            const accessToken = responseData.accessToken;
            const newRefreshToken = responseData.refreshToken;

            if (!accessToken) {
              throw new Error('刷新token失败：未获取到新的accessToken');
            }

            // 更新本地存储
            localStorage.setItem('accessToken', accessToken);
            if (newRefreshToken) {
              localStorage.setItem('refreshToken', newRefreshToken);
            }

            // 更新请求头
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;

            // 处理队列中的请求
            requests.forEach(cb => cb(accessToken));
            requests = [];

            return instance(originalRequest);
          } catch (refreshError) {
            handleTokenExpired();
            return Promise.reject(refreshError);
          } finally {
            isRefreshing = false;
          }
        } else {
          // 正在刷新中，将请求加入队列
          return new Promise(resolve => {
            requests.push(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              resolve(instance(originalRequest));
            });
          });
        }
      }

      return Promise.reject(error);
    }
  );
}