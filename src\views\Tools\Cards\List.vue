<template>
	<div class="list-container">
		<!-- 搜索区域 -->
		<div class="search-area">
			<el-form :inline="true" :model="searchForm">
				<el-form-item label="卡密账号">
					<el-input v-model="searchForm.cardId" placeholder="请输入卡密账号" clearable />
				</el-form-item>
				<el-form-item label="卡密密码">
					<el-input v-model="searchForm.cardSecret" placeholder="请输入卡密密码" clearable />
				</el-form-item>

				<el-form-item label="有效期">
					<el-input-number v-model="searchForm.effectiveDays" placeholder="请输入有效期天数" :min="0" clearable />
				</el-form-item>
				<el-form-item label="激活时间">
					<el-date-picker
						v-model="searchForm.activateTimeRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始日期时间"
						end-placeholder="结束日期时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						:shortcuts="dateShortcuts"
					/>
				</el-form-item>
				<el-form-item label="过期时间">
					<el-date-picker
						v-model="searchForm.expireTimeRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始日期时间"
						end-placeholder="结束日期时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						:shortcuts="dateShortcuts"
					/>
				</el-form-item>
				<el-form-item label="状态">
					<el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="min-width: 100px;">
						<el-option label="未激活" :value="0" />
						<el-option label="未使用" :value="1" />
						<el-option label="已使用" :value="2" />
						<el-option label="已冻结" :value="3" />
						<el-option label="已过期" :value="4" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button @click="resetSearch">重置</el-button>
				</el-form-item>
			</el-form>
		</div>

		<!-- 操作按钮 -->
		<ActionBar @refresh="refreshData">
			<template #left>
				<el-button type="primary" @click="handleGetCardSecret">获取卡密</el-button>
			</template>
		</ActionBar>

		<!-- 数据表格 -->
		<el-table
			ref="tableRef"
			:data="tableData"
			border
			stripe
			style="width: 100%;"
			v-loading="loading"
		>
			<el-table-column type="selection" align="center" width="55" />
			<el-table-column prop="cardId" label="卡密账号" min-width="180" />
			<el-table-column prop="cardSecret" label="卡密密码" min-width="180" />
			<el-table-column prop="cardType" label="卡密类型" min-width="120">
				<template #default="{ row }">
					{{ getCardTypeText(row.cardType) }}
				</template>
			</el-table-column>
			<el-table-column prop="status" label="状态" width="100">
				<template #default="{ row }">
					<el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="faceValue" label="面值" width="100">
				<template #default="{ row }">
					{{ row.faceValue }}元
				</template>
			</el-table-column>
			<el-table-column prop="balance" label="当前余额" width="100">
				<template #default="{ row }">
					{{ row.balance }}元
				</template>
			</el-table-column>
			<el-table-column prop="effectiveDays" label="有效期" width="100">
				<template #default="{ row }">
					{{ row.effectiveDays }}天
				</template>
			</el-table-column>
			<el-table-column prop="activateTime" label="激活时间" min-width="160">
				<template #default="{ row }">
					{{ formatDateTime(row.activateTime) }}
				</template>
			</el-table-column>
			<el-table-column prop="expireTime" label="过期时间" min-width="160">
				<template #default="{ row }">
					{{ formatDateTime(row.expireTime) }}
				</template>
			</el-table-column>
			<el-table-column prop="useTime" label="使用时间" min-width="160">
				<template #default="{ row }">
					{{ formatDateTime(row.useTime) }}
				</template>
			</el-table-column>
			<el-table-column prop="memo" label="备注" min-width="120" />
			<el-table-column label="操作" fixed="right" width="180">
				<template #default="{ row }">
					<el-button 
						size="small" 
						type="danger" 
						@click="handleDelete(row.id)"
						:disabled="row.status === 3 || row.status === 4"
					>删除</el-button>
					<el-button 
						v-if="row.status !== 3" 
						size="small" 
						type="warning" 
						@click="handleDisable(row.id)"
						:disabled="row.status === 4 || row.status === 0"
					>停用</el-button>
					<el-button 
						v-else
						size="small" 
						type="success" 
						@click="handleEnable(row.id)"
					>启用</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-container">
			<el-pagination
				v-model:current-page="pagination.current"
				v-model:page-size="pagination.size"
				:page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 获取卡密对话框 -->
		<el-dialog
			title="卡密信息"
			v-model="cardSecretDialogVisible"
			width="500px"
			:close-on-click-modal="false"
		>
			<div class="card-secret-content">
				<p>您的卡密为：</p>
				<el-input
					v-model="cardSecretValue"
					type="textarea"
					:rows="3"
					readonly
				/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cardSecretDialogVisible = false">关闭</el-button>
					<el-button type="primary" @click="handleCopyCardSecret">复制</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 添加卡密参数对话框 -->
		<el-dialog
			title="添加卡密参数"
			v-model="cardParamsDialogVisible"
			width="500px"
			:close-on-click-modal="false"
		>
			<div class="card-params-content">
				<el-form :model="cardParams" label-width="80px">
					<el-form-item label="有效期">
						<el-input-number v-model="cardParams.effectiveDays" :min="1" :max="365" />
						<span class="unit-label">天</span>
					</el-form-item>
					<el-form-item label="备注">
						<el-input v-model="cardParams.memo" type="textarea" :rows="3" placeholder="请输入备注信息" />
					</el-form-item>
				</el-form>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cardParamsDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="submitCardSecretRequest">提交</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cardsApi } from '@/api/modules/cards'
import RefreshButton from '@/components/RefreshButton.vue'
import ActionBar from '@/components/ActionBar.vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableRef = ref(null)

// 搜索表单
const searchForm = reactive({
	cardId: '',
	cardSecret: '',
	status: '',
	effectiveDays: 0,
	activateTimeRange: [],
	expireTimeRange: []
})

// 分页配置
const pagination = reactive({
	current: 1,
	size: 10,
	total: 0
})

// 卡密对话框
const cardSecretDialogVisible = ref(false)
const cardSecretValue = ref('')

// 添加卡密参数对话框
const cardParamsDialogVisible = ref(false)
const cardParams = reactive({
	effectiveDays: 30,
	memo: ''
})

// 日期快捷选项
const dateShortcuts = [
	{
		text: '最近一周',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000)
			return [start, end]
		}
	},
	{
		text: '最近一个月',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000)
			return [start, end]
		}
	},
	{
		text: '最近三个月',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 90 * 24 * 60 * 60 * 1000)
			return [start, end]
		}
	},
	{
		text: '最近半年',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 180 * 24 * 60 * 60 * 1000)
			return [start, end]
		}
	},
	{
		text: '最近一年',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setTime(start.getTime() - 365 * 24 * 60 * 60 * 1000)
			return [start, end]
		}
	}
]

// 初始化
onMounted(() => {
	fetchData()
})

// 获取表格数据
const fetchData = async () => {
	loading.value = true
	try {
		// 构建查询参数
		const params = {
			page: pagination.current,
			size: pagination.size,
			cardId: searchForm.cardId || undefined,
			cardSecret: searchForm.cardSecret || undefined,
			status: searchForm.status !== null ? searchForm.status : undefined,
			effectiveDays: searchForm.effectiveDays || undefined
		}
		
		// 处理激活时间范围
		if (searchForm.activateTimeRange && searchForm.activateTimeRange.length === 2) {
			params.activateTimeStart = searchForm.activateTimeRange[0]
			params.activateTimeEnd = searchForm.activateTimeRange[1]
		}
		
		// 处理过期时间范围
		if (searchForm.expireTimeRange && searchForm.expireTimeRange.length === 2) {
			params.expireTimeStart = searchForm.expireTimeRange[0]
			params.expireTimeEnd = searchForm.expireTimeRange[1]
		}
		
		// 使用API模块获取数据
		const res = await cardsApi.pageQueryCard(params)
		
		// 由于响应拦截器已经提取了response.data，所以直接使用res
		if (res.code === 200) {
			tableData.value = res.data.list || []
			pagination.total = res.data.total || 0
		} else {
			ElMessage.error(res.message || '获取数据失败')
		}
	} catch (error) {
		console.error('获取数据失败:', error)
		// 显示更具体的错误信息
		if (error.code === 'ECONNABORTED') {
			ElMessage.error('请求超时，请检查网络连接或联系管理员')
		} else if (error.response) {
			ElMessage.error(`请求失败: ${error.response.status} ${error.response.statusText}`)
		} else if (error.request) {
			ElMessage.error('服务器未响应，请稍后再试')
		} else {
			ElMessage.error(`请求错误: ${error.message}`)
		}
		
		// 设置空数据
		tableData.value = []
		pagination.total = 0
	} finally {
		loading.value = false
	}
}

// 获取卡密
const handleGetCardSecret = () => {
	// 显示参数输入对话框
	cardParamsDialogVisible.value = true
}

// 提交卡密获取请求
const submitCardSecretRequest = async () => {
	try {
		const cardData = {
			cardType: "verifyPriceCredential",
			effectiveDays: cardParams.effectiveDays,
			memo: cardParams.memo
		};
		const res = await cardsApi.createCardSecret(cardData)
		// 由于createCardSecret方法已经处理了响应，所以直接使用res
    	console.log("res",res)
		if (res.code === 200) {
			cardSecretValue.value = res.data

			// 关闭参数对话框，显示卡密结果对话框
			cardParamsDialogVisible.value = false
			cardSecretDialogVisible.value = true
		} else {
			ElMessage.error(res.message || '获取卡密失败')
		}
	} catch (error) {
		console.error('获取卡密失败:', error)
		// if(error.status === 400){
		// 	ElMessage.error('获取卡密失败: ' + (error.message || '未知错误'))
		// }
		ElMessage.error('获取卡密失败: ' + (error.response.data.message || '未知错误'))
	}
}

// 复制卡密
const handleCopyCardSecret = () => {
	try {
		// 创建临时文本区域元素
		const textarea = document.createElement('textarea')
		textarea.value = cardSecretValue.value
		textarea.style.position = 'fixed'  // 避免滚动到底部
		textarea.style.opacity = '0'
		document.body.appendChild(textarea)
		textarea.select()
    console.log("textarea",textarea)
		
		// 执行复制命令
		const successful = document.execCommand('copy')
		document.body.removeChild(textarea)
		
		if (successful) {
			ElMessage.success('复制成功')
		} else {
			ElMessage.warning('复制失败，请手动复制')
		}
		cardSecretDialogVisible.value = false
	} catch (error) {
		console.error('复制失败:', error)
		ElMessage.error('复制失败，请手动复制')
	}
}

// 刷新数据
const refreshData = () => {
	fetchData()
}

// 处理搜索
const handleSearch = () => {
	pagination.current = 1
	fetchData()
}

// 重置搜索
const resetSearch = () => {
	searchForm.cardId = ''
	searchForm.cardSecret = ''
	searchForm.status = null
	searchForm.effectiveDays = 0
	searchForm.activateTimeRange = []
	searchForm.expireTimeRange = []
	pagination.current = 1
	fetchData()
}

// 分页大小变化
const handleSizeChange = (size) => {
	pagination.size = size
	pagination.current = 1
	fetchData()
}

// 页码变化
const handleCurrentChange = (current) => {
	pagination.current = current
	fetchData()
}

// 删除
const handleDelete = (id) => {
	ElMessageBox.confirm('确定要删除该卡密吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			const res = await cardsApi.deleteCard(id)
			if (res.code === 200) {
				ElMessage.success('删除成功')
				fetchData()
			} else {
				ElMessage.error(res.message || '删除失败')
			}
		} catch (error) {
			console.error('删除失败:', error)
			// 显示更具体的错误信息
			if (error.code === 'ECONNABORTED') {
				ElMessage.error('请求超时，请稍后再试')
			} else if (error.response) {
				ElMessage.error(`删除失败: ${error.response.status} ${error.response.statusText}`)
			} else {
				ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
			}
		}
	}).catch(() => {
		// 用户取消操作，不做处理
	})
}

// 停用卡密
const handleDisable = (id) => {
	ElMessageBox.confirm('确定要停用该卡密吗？停用后将无法使用', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			const res = await cardsApi.disableCard(id)
			if (res.code === 200) {
				ElMessage.success('停用成功')
				fetchData()
			} else {
				ElMessage.error(res.message || '停用失败')
			}
		} catch (error) {
			console.error('停用失败:', error)
			// 显示更具体的错误信息
			if (error.code === 'ECONNABORTED') {
				ElMessage.error('请求超时，请稍后再试')
			} else if (error.response) {
				ElMessage.error(`停用失败: ${error.response.status} ${error.response.statusText}`)
			} else {
				ElMessage.error(`停用失败: ${error.message || '未知错误'}`)
			}
		}
	}).catch(() => {
		// 用户取消操作，不做处理
	})
}

// 启用卡密
const handleEnable = (id) => {
	ElMessageBox.confirm('确定要启用该卡密吗？启用后将可以使用', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		try {
			const res = await cardsApi.enableCard(id)
			if (res.code === 200) {
				ElMessage.success('启用成功')
				fetchData()
			} else {
				ElMessage.error(res.message || '启用失败')
			}
		} catch (error) {
			console.error('启用失败:', error)
			// 显示更具体的错误信息
			if (error.code === 'ECONNABORTED') {
				ElMessage.error('请求超时，请稍后再试')
			} else if (error.response) {
				ElMessage.error(`启用失败: ${error.response.status} ${error.response.statusText}`)
			} else {
				ElMessage.error(`启用失败: ${error.message || '未知错误'}`)
			}
		}
	}).catch(() => {
		// 用户取消操作，不做处理
	})
}

// 获取状态类型
const getStatusType = (status) => {
	const map = {
		0: 'info',
		1: 'success',
		2: 'warning',
		3: 'danger',
		4: 'info'
	}
	return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
	const map = {
		0: '未激活',
		1: '未使用',
		2: '已使用',
		3: '已冻结',
		4: '已过期'
	}
	return map[status] || '未知'
}

// 获取卡密类型文本
const getCardTypeText = (type) => {
	const map = {
		'verifyPriceCredential': '使用核价凭证'
	}
	return map[type] || type
}

// 格式化时间
const formatDateTime = (timestamp) => {
	if (!timestamp) return '-'
	const date = new Date(timestamp)
	return date.toLocaleString()
}
</script>

<style scoped>
.list-container {
	padding: 20px;
}

.search-area {
	margin-bottom: 20px;
	padding: 18px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	text-align: left;
}

.search-area .el-form-item {
	margin-right: 18px;
	margin-bottom: 10px;
}

.action-bar {
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 10px;
	text-align: left;
}

.action-left {
	display: flex;
	gap: 10px;
}

.action-right {
	display: flex;
	gap: 10px;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-start;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.card-secret-content {
	text-align: left;
	
	p {
		margin-bottom: 15px;
		font-size: 16px;
	}
}

.card-params-content {
	text-align: left;
	
	p {
		margin-bottom: 15px;
		font-size: 16px;
	}
}

.unit-label {
	margin-left: 5px;
}
</style> 