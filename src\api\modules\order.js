import request from '@/utils/axios'

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/wechatOrder/list',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: `/wechatOrder/${id}`,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/wechatOrder',
    method: 'post',
    data
  })
}

// 更新订单
export function updateOrder(data) {
  return request({
    url: '/wechatOrder',
    method: 'put',
    data
  })
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/wechatOrder/${id}`,
    method: 'delete'
  })
} 