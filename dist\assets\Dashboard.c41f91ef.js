import{aW as a,Z as r,_ as e,Q as o,o as l,k as n,l as s,an as t,m as i,w as c,t as d,v as u,z as v,a3 as m,a6 as p,a7 as f}from"./vendor.9a6f3141.js";const y={name:"MonitorDashboard",components:{Warning:a},setup(){const a=r(null),o=r(!0),l=r(""),n=r("http://118.195.145.61");return e((async()=>{await(async()=>{try{if(!(await fetch(`${n.value}/api/v1/health`)).ok)throw new Error("监控服务不可用");return!0}catch(a){return l.value="无法连接到监控服务，请确保Go监控服务已启动",o.value=!1,!1}})()&&a.value&&(a.value.src=n.value)})),{monitorFrame:a,monitorUrl:n,loading:o,error:l,onFrameLoad:()=>{o.value=!1,l.value=""},reload:()=>{o.value=!0,l.value="",a.value&&(a.value.src=n.value)}}}},h=a=>(p("data-v-ccc0a0ea"),a=a(),f(),a),g={class:"monitor-dashboard-wrapper"},k=["src"],w={key:0,class:"loading-overlay"},F=[h((()=>s("div",{class:"loading-spinner"},[s("i",{class:"el-icon-loading"})],-1))),h((()=>s("p",null,"正在加载监控大屏...",-1)))],_={key:1,class:"error-overlay"};y.render=function(a,r,e,p,f,y){const h=o("Warning"),L=v,b=m;return l(),n("div",g,[s("iframe",{ref:"monitorFrame",src:p.monitorUrl,class:"monitor-iframe",frameborder:"0",onLoad:r[0]||(r[0]=(...a)=>p.onFrameLoad&&p.onFrameLoad(...a))},null,40,k),p.loading?(l(),n("div",w,F)):t("",!0),p.error?(l(),n("div",_,[i(L,null,{default:c((()=>[i(h)])),_:1}),s("p",null,d(p.error),1),i(b,{onClick:p.reload,type:"primary"},{default:c((()=>[u("重新加载")])),_:1},8,["onClick"])])):t("",!0)])},y.__scopeId="data-v-ccc0a0ea";export{y as default};
