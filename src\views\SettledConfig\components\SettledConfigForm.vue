<template>
  <el-dialog
    :title="formData.id ? '编辑入驻配置' : '新增入驻配置'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="150px"
      label-position="right"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入标题" />
      </el-form-item>
      
      <el-form-item label="入驻标识" prop="settledCostKey">
        <el-input v-model="formData.settledCostKey" placeholder="请输入入驻标识" />
      </el-form-item>

      <el-form-item label="最小书本数量">
        <el-input-number v-model="constraint.books_count_min" :min="0" :precision="0" @change="updateConstraintJson" />
        <span class="unit-label">本</span>
      </el-form-item>

      <el-form-item label="最大书本数量">
        <el-input-number v-model="constraint.books_count_max" :min="0" :precision="0" @change="updateConstraintJson" />
        <span class="unit-label">本</span>
      </el-form-item>

      <el-divider content-position="center">佣金设置</el-divider>

      <el-form-item label="佣金类型" prop="kickbackType">
        <el-select v-model="formData.kickbackType" placeholder="请选择佣金类型">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item 
        v-if="[1, 3].includes(formData.kickbackType)" 
        label="提点比例" 
        prop="kickbackPointValue"
      >
        <el-input-number 
          v-model="kickbackPoint" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateKickbackJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item 
        v-if="[2, 3].includes(formData.kickbackType)" 
        label="固定费用" 
        prop="kickbackFixedValue"
      >
        <el-input-number 
          v-model="kickbackFixed" 
          :min="0" 
          :precision="2"
          @change="updateKickbackJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">小程序收费</el-divider>

      <el-form-item label="小程序月收费">
        <el-input-number v-model="constraint.miniapp.month_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="小程序年收费">
        <el-input-number v-model="constraint.miniapp.year_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/年</span>
      </el-form-item>

      <el-form-item label="小程序佣金类型">
        <el-select v-model="constraint.miniapp.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.miniapp.kickback_type)" label="小程序提点比例">
        <el-input-number 
          v-model="constraint.miniapp.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.miniapp.kickback_type)" label="小程序固定费用">
        <el-input-number 
          v-model="constraint.miniapp.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">新增账号收费</el-divider>

      <el-form-item label="新增账号月收费">
        <el-input-number v-model="constraint.new_account.month_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="新增账号年收费">
        <el-input-number v-model="constraint.new_account.year_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/年</span>
      </el-form-item>

      <el-form-item label="新增账号佣金类型">
        <el-select v-model="constraint.new_account.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.new_account.kickback_type)" label="新增账号提点比例">
        <el-input-number 
          v-model="constraint.new_account.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.new_account.kickback_type)" label="新增账号固定费用">
        <el-input-number 
          v-model="constraint.new_account.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">孔夫子店铺收费</el-divider>

      <el-form-item label="孔夫子店铺月收费">
        <el-input-number v-model="constraint.kongfz_store.month_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="孔夫子店铺年收费">
        <el-input-number v-model="constraint.kongfz_store.year_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/年</span>
      </el-form-item>

      <el-form-item label="孔夫子店铺佣金类型">
        <el-select v-model="constraint.kongfz_store.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.kongfz_store.kickback_type)" label="孔夫子店铺提点比例">
        <el-input-number 
          v-model="constraint.kongfz_store.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.kongfz_store.kickback_type)" label="孔夫子店铺固定费用">
        <el-input-number 
          v-model="constraint.kongfz_store.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">拼多多专营店收费</el-divider>

      <el-form-item label="拼多多专营店月收费">
        <el-input-number v-model="constraint.pdd_store.month_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="拼多多专营店年收费">
        <el-input-number v-model="constraint.pdd_store.year_fee" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/年</span>
      </el-form-item>

      <el-form-item label="拼多多专营店佣金类型">
        <el-select v-model="constraint.pdd_store.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.pdd_store.kickback_type)" label="拼多多专营店提点比例">
        <el-input-number 
          v-model="constraint.pdd_store.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.pdd_store.kickback_type)" label="拼多多专营店固定费用">
        <el-input-number 
          v-model="constraint.pdd_store.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">资源费阶梯设置</el-divider>

      <el-form-item label="图书数量范围">
        <div class="range-fields">
          <span>0 - 3000条:</span>
          <el-input-number v-model="constraint.resource_fee.range1" :min="0" :precision="2" @change="updateConstraintJson" />
          <span class="unit-label">元/月</span>
        </div>
      </el-form-item>

      <el-form-item label="3000 - 20000条">
        <el-input-number v-model="constraint.resource_fee.range2" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="20000 - 50000条">
        <el-input-number v-model="constraint.resource_fee.range3" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="50000 - 100000条">
        <el-input-number v-model="constraint.resource_fee.range4" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="100000条以上">
        <el-input-number v-model="constraint.resource_fee.range5" :min="0" :precision="2" @change="updateConstraintJson" />
        <span class="unit-label">元/月</span>
      </el-form-item>

      <el-form-item label="资源费佣金类型">
        <el-select v-model="constraint.resource_fee.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.resource_fee.kickback_type)" label="资源费提点比例">
        <el-input-number 
          v-model="constraint.resource_fee.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.resource_fee.kickback_type)" label="资源费固定费用">
        <el-input-number 
          v-model="constraint.resource_fee.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">交易手续费设置</el-divider>

      <el-form-item label="同库房同店铺佣金类型">
        <el-select v-model="constraint.transaction_fee.same_store.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.transaction_fee.same_store.kickback_type)" label="同库房同店铺提点比例">
        <el-input-number 
          v-model="constraint.transaction_fee.same_store.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.transaction_fee.same_store.kickback_type)" label="同库房同店铺固定费用">
        <el-input-number 
          v-model="constraint.transaction_fee.same_store.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider content-position="center">同库房不同店铺设置</el-divider>

      <el-form-item label="扣库房金额">
        <el-input-number 
          v-model="constraint.transaction_fee.diff_store.warehouse_fee" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-form-item label="扣店主金额">
        <el-input-number 
          v-model="constraint.transaction_fee.diff_store.shop_owner_fee" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-form-item label="不同店铺佣金类型">
        <el-select v-model="constraint.transaction_fee.diff_store.kickback_type" placeholder="请选择佣金类型" @change="updateConstraintJson">
          <el-option label="预留" :value="0" />
          <el-option label="提点" :value="1" />
          <el-option label="固定费用" :value="2" />
          <el-option label="提点/固费" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="[1, 3].includes(constraint.transaction_fee.diff_store.kickback_type)" label="不同店铺提点比例">
        <el-input-number 
          v-model="constraint.transaction_fee.diff_store.kickback_point" 
          :min="0" 
          :max="100"
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">%</span>
      </el-form-item>

      <el-form-item v-if="[2, 3].includes(constraint.transaction_fee.diff_store.kickback_type)" label="不同店铺固定费用">
        <el-input-number 
          v-model="constraint.transaction_fee.diff_store.kickback_fixed" 
          :min="0" 
          :precision="2"
          @change="updateConstraintJson"
        />
        <span class="unit-label">元</span>
      </el-form-item>

      <el-divider></el-divider>

      <el-form-item label="资源费类型" prop="resourceCostType">
        <el-select v-model="formData.resourceCostType" placeholder="请选择资源费类型">
          <el-option label="类型1" :value="1" />
          <el-option label="类型2" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="资源费值" prop="resourceCostValue">
        <el-input-number v-model="formData.resourceCostValue" :min="0" :precision="2" />
      </el-form-item>

      <el-form-item label="服务费率" prop="serviceRate">
        <el-input-number v-model="formData.serviceRate" :min="0" :max="100" :precision="2" />
      </el-form-item>

      <el-form-item label="价格(分)" prop="price">
        <el-input-number v-model="formData.price" :min="0" :precision="0" />
      </el-form-item>

      <el-form-item label="状态" prop="state">
        <el-switch
          v-model="formData.state"
          :active-value="1"
          :inactive-value="0"
          active-text="正常"
          inactive-text="失效"
        />
      </el-form-item>

      <el-form-item label="备注" prop="note">
        <el-input
          v-model="formData.note"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { settledCostConfigApi } from '@/api/modules/settledCostConfig'

const props = defineProps<{
  visible: boolean
  editData?: any
}>()

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(props.visible)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 约束条件数据结构
const constraint = reactive({
  books_count_min: 0,
  books_count_max: 3000,
  miniapp: {
    month_fee: 0,
    year_fee: 0,
    kickback_type: 0,
    kickback_point: 0,
    kickback_fixed: 0
  },
  new_account: {
    month_fee: 0,
    year_fee: 0,
    kickback_type: 0,
    kickback_point: 0,
    kickback_fixed: 0
  },
  kongfz_store: {
    month_fee: 0,
    year_fee: 0,
    kickback_type: 0,
    kickback_point: 0,
    kickback_fixed: 0
  },
  pdd_store: {
    month_fee: 0,
    year_fee: 0,
    kickback_type: 0,
    kickback_point: 0,
    kickback_fixed: 0
  },
  resource_fee: {
    range1: 0,  // 0-3000条
    range2: 50, // 3000-20000条
    range3: 100, // 20000-50000条
    range4: 200, // 50000-100000条
    range5: 300, // 100000以上
    kickback_type: 0,
    kickback_point: 0,
    kickback_fixed: 0
  },
  transaction_fee: {
    same_store: {
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    },
    diff_store: {
      warehouse_fee: 0,
      shop_owner_fee: 0,
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    }
  }
})

// 更新constraintJson
const updateConstraintJson = () => {
  formData.constraintJson = JSON.stringify(constraint)
}

// 解析constraintJson
const parseConstraintJson = (jsonStr: string) => {
  try {
    const data = JSON.parse(jsonStr)
    
    // 保留原有的books_count字段
    constraint.books_count_min = data.books_count_min ?? 0
    constraint.books_count_max = data.books_count_max ?? 3000
    
    // 小程序收费
    if (data.miniapp) {
      Object.assign(constraint.miniapp, data.miniapp)
    }
    
    // 新增账号收费
    if (data.new_account) {
      Object.assign(constraint.new_account, data.new_account)
    }
    
    // 孔夫子店铺收费
    if (data.kongfz_store) {
      Object.assign(constraint.kongfz_store, data.kongfz_store)
    }
    
    // 拼多多专营店收费
    if (data.pdd_store) {
      Object.assign(constraint.pdd_store, data.pdd_store)
    }
    
    // 资源费阶梯设置
    if (data.resource_fee) {
      Object.assign(constraint.resource_fee, data.resource_fee)
    }
    
    // 交易手续费设置
    if (data.transaction_fee) {
      if (data.transaction_fee.same_store) {
        Object.assign(constraint.transaction_fee.same_store, data.transaction_fee.same_store)
      }
      if (data.transaction_fee.diff_store) {
        Object.assign(constraint.transaction_fee.diff_store, data.transaction_fee.diff_store)
      }
    }
  } catch (e) {
    console.error('解析约束条件失败:', e)
  }
}

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  settledCostKey: '',
  kickbackType: 0,
  kickbackValue: JSON.stringify({ point: 0, fixed: 0 }),
  resourceCostType: 1,
  resourceCostValue: 0,
  serviceRate: 0,
  price: 0,
  state: 1,
  note: '',
  constraintJson: JSON.stringify(constraint)
})

// 佣金值编辑字段
const kickbackPoint = ref(0)
const kickbackFixed = ref(0)

// 更新佣金JSON
const updateKickbackJson = () => {
  const kickbackData = {
    point: kickbackPoint.value,
    fixed: kickbackFixed.value
  }
  formData.kickbackValue = JSON.stringify(kickbackData)
}

// 解析佣金JSON
const parseKickbackJson = (jsonStr: string) => {
  try {
    const data = JSON.parse(jsonStr)
    kickbackPoint.value = data.point || 0
    kickbackFixed.value = data.fixed || 0
  } catch (e) {
    console.error('解析佣金值失败:', e)
    kickbackPoint.value = 0
    kickbackFixed.value = 0
  }
}

// 监听佣金类型变化
watch(() => formData.kickbackType, (newType) => {
  if (![1, 2, 3].includes(newType)) {
    // 如果是预留类型，重置佣金值
    kickbackPoint.value = 0
    kickbackFixed.value = 0
    updateKickbackJson()
  }
})

// 监听kickbackValue变化
watch(() => formData.kickbackValue, (newVal) => {
  if (newVal && typeof newVal === 'string') {
    parseKickbackJson(newVal)
  }
}, { immediate: true })

// 监听constraintJson变化
watch(() => formData.constraintJson, (newVal) => {
  if (newVal && typeof newVal === 'string') {
    parseConstraintJson(newVal)
  }
}, { immediate: true })

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val && props.editData) {
    // 编辑模式，填充表单数据
    Object.assign(formData, {
      id: props.editData.id,
      title: props.editData.title,
      settledCostKey: props.editData.settled_cost_key,
      kickbackType: props.editData.kickback_type,
      kickbackValue: props.editData.kickback_value,
      resourceCostType: props.editData.resource_cost_type,
      resourceCostValue: props.editData.resource_cost_value,
      serviceRate: props.editData.service_rate,
      price: props.editData.price,
      state: props.editData.state,
      note: props.editData.note,
      constraintJson: props.editData.constraint_json
    })
    // 解析佣金值
    if (props.editData.kickback_value) {
      parseKickbackJson(props.editData.kickback_value)
    }
    // 解析约束条件
    if (props.editData.constraint_json) {
      parseConstraintJson(props.editData.constraint_json)
    }
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:visible', val)
  if (!val) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置约束条件
  Object.assign(constraint, {
    books_count_min: 0,
    books_count_max: 3000,
    miniapp: {
      month_fee: 0,
      year_fee: 0,
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    },
    new_account: {
      month_fee: 0,
      year_fee: 0,
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    },
    kongfz_store: {
      month_fee: 0,
      year_fee: 0,
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    },
    pdd_store: {
      month_fee: 0,
      year_fee: 0,
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    },
    resource_fee: {
      range1: 0,
      range2: 50,
      range3: 100,
      range4: 200,
      range5: 300,
      kickback_type: 0,
      kickback_point: 0,
      kickback_fixed: 0
    },
    transaction_fee: {
      same_store: {
        kickback_type: 0,
        kickback_point: 0,
        kickback_fixed: 0
      },
      diff_store: {
        warehouse_fee: 0,
        shop_owner_fee: 0,
        kickback_type: 0,
        kickback_point: 0,
        kickback_fixed: 0
      }
    }
  })
  
  // 更新constraintJson
  updateConstraintJson()
  
  // 重置表单数据
  Object.assign(formData, {
    id: '',
    title: '',
    settledCostKey: '',
    kickbackType: 0,
    kickbackValue: JSON.stringify({ point: 0, fixed: 0 }),
    resourceCostType: 1,
    resourceCostValue: 0,
    serviceRate: 0,
    price: 0,
    state: 1,
    note: '',
    constraintJson: JSON.stringify(constraint)
  })
  
  kickbackPoint.value = 0
  kickbackFixed.value = 0
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  // 更新 constraintJson
  updateConstraintJson()
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (formData.id) {
          // 编辑
          await settledCostConfigApi.updateSettledCostConfig(formData)
          ElMessage.success('更新成功')
        } else {
          // 新增
          await settledCostConfigApi.createSettledCostConfig(formData)
          ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        emit('success')
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  resetForm()
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.unit-label {
  margin-left: 8px;
  color: #909399;
}

.el-divider {
  margin: 16px 0;
}

.range-fields {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 