var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,r=(a,l,o)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[l]=o;import{d as p,a8 as i,Z as d,_ as n,ao as u,ah as c,ai as h,aa as m,a9 as f,a3 as y,ab as g,ac as b,ae as v,al as w,af as _,ag as V,aA as N,aB as k,am as z,as as S,o as A,k as O,l as C,m as U,w as j,v as T,at as x,F as G,t as $,an as I,E as P}from"./vendor.9a6f3141.js";/* empty css                   *//* empty css                   *//* empty css                 *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                     */import{i as R}from"./index.1c8cd61b.js";import{R as D}from"./RefreshButton.c2d64dc7.js";const B=e=>R.get(`/shop/${e}`),E=e=>R.put("/shop",e),L=async e=>{try{const l=await fetch(e,{method:"GET",headers:{Accept:"*/*"},cache:"no-cache"});if(!l.ok)throw new Error(`HTTP error! Status: ${l.status}`);const o=await l.text();if((e=>{try{return new URL(e),!0}catch(a){return!1}})(o))return console.log("直接返回URL:",o),{url:o.trim()};try{return JSON.parse(o)}catch(a){return{text:o}}}catch(l){throw console.error("外部API请求失败:",l),l}},q={class:"list-container"},F={class:"search-area"},H={class:"action-bar"},J={class:"action-left"},Z={class:"action-right"},K={class:"pagination-container"},M={class:"dialog-footer"};var Q=p({__name:"index",setup(e){const p=i({shopType:"",shopGroup:"",shopName:"",shopAliasName:"",shopAuthorize:"",status:"",pageNum:1,pageSize:20}),Q=d(!1),W=d([]),X=d(0),Y=d([]),ee=i({visible:!1,title:""}),ae=d(),le=i({id:void 0,mallId:"",shopNike:"",shopType:"1",shopGroup:"",shopName:"",shopAliasName:"",shopAuthorize:"0",status:"0",account:"",password:"",isSynOrder:0}),oe={shopType:[{required:!0,message:"请选择店铺类型",trigger:"change"}],shopName:[{required:!0,message:"请输入店铺名称",trigger:"blur"}],shopAliasName:[{required:!0,message:"请输入三方名称",trigger:"blur"}]};n((()=>{re()}));const te=e=>{switch(e){case"1":return"success";case"0":return"info";case"2":return"warning";default:return"info"}},se=e=>{switch(e){case"1":return"已授权";case"0":return"未授权";case"2":return"已过期";default:return"未知"}},re=async()=>{Q.value=!0;try{const e=await(async(e={})=>{const a=new URLSearchParams;e.pageNum&&a.append("pageNum",e.pageNum),e.pageSize&&a.append("pageSize",e.pageSize),e.shopType&&a.append("shopType",e.shopType),e.shopGroup&&a.append("shopGroup",e.shopGroup),e.shopName&&a.append("shopName",e.shopName),e.shopAliasName&&a.append("shopAliasName",e.shopAliasName),e.shopAuthorize&&a.append("shopAuthorize",e.shopAuthorize),e.status&&a.append("status",e.status);const l=`/shop/list?${a.toString()}`;return R.get(l)})(p);console.log("res",e),200===e.code?(W.value=e.data.list,X.value=e.data.total):u.error(e.message||"获取数据失败")}catch(e){u.error("获取店铺列表失败")}finally{Q.value=!1}},pe=()=>{p.pageNum=1,re()},ie=()=>{p.shopType="",p.shopGroup="",p.shopName="",p.shopAliasName="",p.shopAuthorize="",p.status="",p.pageNum=1,re()},de=e=>{p.pageSize=e,re()},ne=e=>{p.pageNum=e,re()},ue=e=>{Y.value=e},ce=()=>{ee.title="添加店铺",ee.visible=!0,le.id=void 0,le.mallId="",le.shopNike="",le.shopType="1",le.shopGroup="",le.shopName="",le.shopAliasName="",le.shopAuthorize="0",le.status="0",le.account="",le.password="",le.isSynOrder=0},he=e=>{if(ee.title="编辑店铺",ee.visible=!0,e){const a=e.id;a&&B(a).then((e=>{200===e.data.code&&Object.assign(le,e.data.data)}))}else{if(1!==Y.value.length)return u.warning("请选择一条记录进行修改"),void(ee.visible=!1);const e=Y.value[0].id;e&&B(e).then((e=>{200===e.data.code&&Object.assign(le,e.data.data)}))}},me=e=>{if(e)P.confirm("确认删除该店铺记录吗？","警告",{type:"warning"}).then((()=>{var a;e.id&&(a=e.id,R.delete(`/shop/${a}`)).then((e=>{200===e.data.code&&(u.success("删除成功"),re())}))})).catch((()=>{}));else{if(0===Y.value.length)return void u.warning("请至少选择一条记录");P.confirm(`确认删除选中的${Y.value.length}条店铺记录吗？`,"警告",{type:"warning"}).then((()=>{const e=Y.value.map((e=>e.id)).filter((e=>void 0!==e));e.length>0&&(e=>R.delete(`/shop/batch/${e}`))(e.join(",")).then((e=>{200===e.data.code&&(u.success("批量删除成功"),re())}))})).catch((()=>{}))}},fe=e=>{const a=1===e.isSynOrder?"开启":"关闭";var l,o;e.id&&(l=e.id,o=e.isSynOrder,R.put("/shop/syncOrder",{id:l,isSynOrder:o})).then((l=>{200===l.data.code?u.success(`${a}同步订单成功`):(u.error(`${a}同步订单失败`),e.isSynOrder=1===e.isSynOrder?0:1)})).catch((()=>{e.isSynOrder=1===e.isSynOrder?0:1,u.error(`${a}同步订单失败`)}))},ye=async(e,p)=>{const i="0"===p?"启用":"停用";P.confirm(`确认要${i}该店铺吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const n=(d=((e,a)=>{for(var l in a||(a={}))t.call(a,l)&&r(e,l,a[l]);if(o)for(var l of o(a))s.call(a,l)&&r(e,l,a[l]);return e})({},e),a(d,l({status:p})));await E(n),u.success(`${i}成功`),re()}catch(n){console.error(`${i}失败:`,n),u.error(`${i}失败`)}var d})).catch((()=>{}))},ge=()=>{ae.value.validate((e=>{var a;e&&(le.id?E(le).then((e=>{200===e.data.code&&(u.success("修改成功"),ee.visible=!1,re())})):(a=le,R.post("/shop",a)).then((e=>{200===e.data.code&&(u.success("新增成功"),ee.visible=!1,re())})))}))};return(e,a)=>{const l=c,o=h,t=m,s=f,r=y,i=g,d=b,n=v,P=w,R=_,B=V,E=N,Y=k,be=z,ve=S;return A(),O("div",q,[C("div",F,[U(i,{model:p,inline:""},{default:j((()=>[U(t,{label:"店铺类型"},{default:j((()=>[U(o,{modelValue:p.shopType,"onUpdate:modelValue":a[0]||(a[0]=e=>p.shopType=e),placeholder:"请选择店铺类型",clearable:""},{default:j((()=>[U(l,{label:"拼多多",value:"1"})])),_:1},8,["modelValue"])])),_:1}),U(t,{label:"分组"},{default:j((()=>[U(s,{modelValue:p.shopGroup,"onUpdate:modelValue":a[1]||(a[1]=e=>p.shopGroup=e),placeholder:"请输入分组"},null,8,["modelValue"])])),_:1}),U(t,{label:"店铺名称"},{default:j((()=>[U(s,{modelValue:p.shopName,"onUpdate:modelValue":a[2]||(a[2]=e=>p.shopName=e),placeholder:"请输入店铺名称"},null,8,["modelValue"])])),_:1}),U(t,{label:"三方名称"},{default:j((()=>[U(s,{modelValue:p.shopAliasName,"onUpdate:modelValue":a[3]||(a[3]=e=>p.shopAliasName=e),placeholder:"请输入三方名称"},null,8,["modelValue"])])),_:1}),U(t,{label:"是否授权"},{default:j((()=>[U(o,{modelValue:p.shopAuthorize,"onUpdate:modelValue":a[4]||(a[4]=e=>p.shopAuthorize=e),placeholder:"请选择是否授权",clearable:""},{default:j((()=>[U(l,{label:"已授权",value:"1"}),U(l,{label:"未授权",value:"0"}),U(l,{label:"已过期",value:"2"})])),_:1},8,["modelValue"])])),_:1}),U(t,{label:"店铺状态"},{default:j((()=>[U(o,{modelValue:p.status,"onUpdate:modelValue":a[5]||(a[5]=e=>p.status=e),placeholder:"请选择店铺状态",clearable:""},{default:j((()=>[U(l,{label:"正常",value:"0"}),U(l,{label:"停用",value:"1"})])),_:1},8,["modelValue"])])),_:1}),U(t,null,{default:j((()=>[U(r,{type:"primary",icon:"Search",onClick:pe},{default:j((()=>[T("搜索")])),_:1}),U(r,{icon:"Refresh",onClick:ie},{default:j((()=>[T("重置")])),_:1})])),_:1})])),_:1},8,["model"])]),C("div",H,[C("div",J,[U(r,{type:"primary",onClick:ce},{default:j((()=>[T("新增")])),_:1}),U(r,{type:"primary",onClick:he},{default:j((()=>[T("编辑")])),_:1}),U(r,{type:"danger",onClick:me},{default:j((()=>[T("删除")])),_:1})]),C("div",Z,[U(D,{onRefresh:re})])]),x((A(),G(R,{data:W.value,onSelectionChange:ue,border:"",stripe:"",style:{width:"100%"},"row-key":"id"},{default:j((()=>[U(d,{type:"selection",width:"55",align:"center"}),U(d,{label:"店铺编码",prop:"id",align:"center"}),U(d,{label:"店铺类型",align:"center"},{default:j((e=>[T($("1"===e.row.shopType?"拼多多":"-"),1)])),_:1}),U(d,{label:"分组",prop:"shopGroup",align:"center"}),U(d,{label:"店铺名称",prop:"shopName",align:"center"}),U(d,{label:"三方名称",prop:"shopAliasName",align:"center"}),U(d,{label:"是否授权",align:"center"},{default:j((e=>["1"===e.row.shopAuthorize?(A(),G(n,{key:0,type:te(e.row.shopAuthorize)},{default:j((()=>[T($(se(e.row.shopAuthorize)),1)])),_:2},1032,["type"])):(A(),G(r,{key:1,size:"small",type:"primary",onClick:a=>(async e=>{try{u.info("正在获取授权链接，请稍候...");const a=`https://api.buzhiyushu.cn/huidiao/pdd/toPddGetCode?id=${e.id}&type=4`,l=await L(a);let o=null;if(l&&(l.url?o=l.url:"string"==typeof l&&l.includes("http")&&(o=l)),o){console.log("授权URL:",o);const e=window.open(o,"_blank");e&&!e.closed&&void 0!==e.closed||(window.location.href=o)}else console.error("无法解析授权链接:",l),u.error("获取授权链接失败")}catch(a){console.error("授权请求失败:",a),u.error("授权请求失败")}})(e.row)},{default:j((()=>[T(" 授权 ")])),_:2},1032,["onClick"]))])),_:1}),U(d,{label:"到期时间",align:"center"},{default:j((e=>[T($(e.row.expirationTime),1)])),_:1}),U(d,{label:"添加时间",prop:"addTime",align:"center"}),U(d,{label:"店铺状态",align:"center"},{default:j((e=>[U(n,{type:"0"===e.row.status?"success":"danger"},{default:j((()=>[T($("0"===e.row.status?"正常":"停用"),1)])),_:2},1032,["type"])])),_:1}),U(d,{label:"同步订单",align:"center"},{default:j((e=>[U(P,{modelValue:e.row.isSynOrder,"onUpdate:modelValue":a=>e.row.isSynOrder=a,"active-value":1,"inactive-value":0,class:"ml-2","inline-prompt":"","active-text":"开","inactive-text":"关",onChange:a=>fe(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),U(d,{label:"设置",align:"center",width:"60"},{default:j((e=>[U(r,{type:"primary",icon:"Setting",circle:"",plain:"",size:"small",onClick:a=>{return l=e.row,void u.info(`设置店铺: ${l.shopName}`);var l}},null,8,["onClick"])])),_:1}),U(d,{label:"操作",align:"center",width:"220",fixed:"right"},{default:j((e=>[U(r,{size:"small",type:"primary",onClick:a=>he(e.row)},{default:j((()=>[T("编辑")])),_:2},1032,["onClick"]),U(r,{size:"small",type:"danger",onClick:a=>me(e.row)},{default:j((()=>[T("删除")])),_:2},1032,["onClick"]),"0"===e.row.status?(A(),G(r,{key:0,size:"small",type:"warning",onClick:a=>ye(e.row,"1")},{default:j((()=>[T("停用")])),_:2},1032,["onClick"])):I("",!0),"1"===e.row.status?(A(),G(r,{key:1,size:"small",type:"success",onClick:a=>ye(e.row,"0")},{default:j((()=>[T("启用")])),_:2},1032,["onClick"])):I("",!0)])),_:1})])),_:1},8,["data"])),[[ve,Q.value]]),C("div",K,[U(B,{"current-page":p.pageNum,"onUpdate:currentPage":a[6]||(a[6]=e=>p.pageNum=e),"page-size":p.pageSize,"onUpdate:pageSize":a[7]||(a[7]=e=>p.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:X.value,onSizeChange:de,onCurrentChange:ne},null,8,["current-page","page-size","total"])]),U(be,{title:ee.title,modelValue:ee.visible,"onUpdate:modelValue":a[19]||(a[19]=e=>ee.visible=e),width:"600px","close-on-click-modal":!1,"append-to-body":""},{footer:j((()=>[C("div",M,[U(r,{onClick:a[18]||(a[18]=e=>ee.visible=!1)},{default:j((()=>[T("取消")])),_:1}),U(r,{type:"primary",onClick:ge},{default:j((()=>[T("确定")])),_:1})])])),default:j((()=>[U(i,{model:le,rules:oe,ref_key:"formRef",ref:ae,"label-width":"100px"},{default:j((()=>[U(t,{label:"店铺类型",prop:"shopType"},{default:j((()=>[U(o,{modelValue:le.shopType,"onUpdate:modelValue":a[8]||(a[8]=e=>le.shopType=e),placeholder:"请选择店铺类型",style:{width:"100%"}},{default:j((()=>[U(l,{label:"拼多多",value:"1"})])),_:1},8,["modelValue"])])),_:1}),U(t,{label:"分组",prop:"shopGroup"},{default:j((()=>[U(s,{modelValue:le.shopGroup,"onUpdate:modelValue":a[9]||(a[9]=e=>le.shopGroup=e),placeholder:"请输入分组"},null,8,["modelValue"])])),_:1}),U(t,{label:"店铺名称",prop:"shopName"},{default:j((()=>[U(s,{modelValue:le.shopName,"onUpdate:modelValue":a[10]||(a[10]=e=>le.shopName=e),placeholder:"请输入店铺名称"},null,8,["modelValue"])])),_:1}),U(t,{label:"三方名称",prop:"shopAliasName"},{default:j((()=>[U(s,{modelValue:le.shopAliasName,"onUpdate:modelValue":a[11]||(a[11]=e=>le.shopAliasName=e),placeholder:"请输入三方名称"},null,8,["modelValue"])])),_:1}),U(t,{label:"店铺ID",prop:"mallId"},{default:j((()=>[U(s,{modelValue:le.mallId,"onUpdate:modelValue":a[12]||(a[12]=e=>le.mallId=e),placeholder:"请输入三方店铺ID"},null,8,["modelValue"])])),_:1}),U(t,{label:"万里牛ID",prop:"shopNike"},{default:j((()=>[U(s,{modelValue:le.shopNike,"onUpdate:modelValue":a[13]||(a[13]=e=>le.shopNike=e),placeholder:"请输入万里牛系统ID"},null,8,["modelValue"])])),_:1}),U(t,{label:"第三方账号",prop:"account"},{default:j((()=>[U(s,{modelValue:le.account,"onUpdate:modelValue":a[14]||(a[14]=e=>le.account=e),placeholder:"请输入第三方平台账号"},null,8,["modelValue"])])),_:1}),U(t,{label:"第三方密码",prop:"password"},{default:j((()=>[U(s,{modelValue:le.password,"onUpdate:modelValue":a[15]||(a[15]=e=>le.password=e),type:"password",placeholder:"请输入第三方平台密码","show-password":""},null,8,["modelValue"])])),_:1}),U(t,{label:"同步订单",prop:"isSynOrder"},{default:j((()=>[U(P,{modelValue:le.isSynOrder,"onUpdate:modelValue":a[16]||(a[16]=e=>le.isSynOrder=e),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])),_:1}),U(t,{label:"店铺状态",prop:"status"},{default:j((()=>[U(Y,{modelValue:le.status,"onUpdate:modelValue":a[17]||(a[17]=e=>le.status=e)},{default:j((()=>[U(E,{label:"0"},{default:j((()=>[T("正常")])),_:1}),U(E,{label:"1"},{default:j((()=>[T("停用")])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"])])}}});Q.__scopeId="data-v-727ef5ec";export{Q as default};
