import instance from '../../utils/axios.js'

const bookBaseInfoApi = {
  // 获取图书基本信息列表
  getBookBaseInfoList: (params) => instance.get('/baseInfo/list', { params }),
  
  // 获取图书基本信息详情
  getBookBaseInfoDetail: (id) => instance.get(`/book/baseInfo/detail/${id}`),
  
  // 添加图书基本信息
  addBookBaseInfo: (data) => instance.post('/book/baseInfo/add', data),
  
  // 更新图书基本信息
  updateBookBaseInfo: (data) => instance.put('/book/baseInfo/update', data),
  
  // 删除图书基本信息
  deleteBookBaseInfo: (id) => instance.delete(`/book/baseInfo/delete/${id}`),
  
  // 批量删除图书基本信息
  batchDeleteBookBaseInfo: (ids) => instance.post('/book/baseInfo/batchDelete', { ids }),
  
  // 更新图书违规状态
  updateBookViolationStatus: (data) => instance.put('/book/baseInfo/updateViolation', data),
  
  // 批量设置违规信息
  updateViolationConfig: (data) => instance.put('/baseInfo/batchUpdateIll', data),
  
  // 图书价格调整
  adjustBookPrice: (data) => instance.put('/book/baseInfo/adjustPrice', data),
  
  // 统计图书数据
  getBookStatistics: () => instance.get('/book/baseInfo/statistics')
}

export { bookBaseInfoApi } 