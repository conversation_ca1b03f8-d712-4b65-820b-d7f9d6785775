{"version": 3, "file": "interface.js", "sourceRoot": "", "sources": ["../../tsc/usb/interface.ts"], "names": [], "mappings": ";;;AAAA,yCAAyE;AAEzE,yCAA+D;AAC/D,+BAAiC;AAEjC,MAAa,SAAS;IAgBlB,YAAsB,MAAc,EAAY,EAAU;QAApC,WAAM,GAAN,MAAM,CAAQ;QAAY,OAAE,GAAF,EAAE,CAAQ;QAZ1D,wCAAwC;QACjC,eAAU,GAAG,CAAC,CAAC;QAYlB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,YAAY,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IAES,OAAO;QACb,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,6BAAkB,CAAC,CAAC,CAAC,CAAC,qBAAU,CAAC,CAAC,CAAC,sBAAW,CAAC;YAClF,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,KAAK;QACR,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IAyBM,OAAO,CAAC,wBAAwE,EAAE,QAAuD;QAE5I,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,OAAO,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAChD,cAAc,GAAG,wBAAwB,CAAC;QAC9C,CAAC;aAAM,CAAC;YACJ,QAAQ,GAAG,wBAAwB,CAAC;QACxC,CAAC;QAED,MAAM,IAAI,GAAG,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;gBAC5C,IAAI,CAAC,KAAK,EAAE,CAAC;oBACT,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;oBACpB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnB,CAAC;gBACD,IAAI,QAAQ,EAAE,CAAC;oBACX,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC/B,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,IAAI,EAAE,CAAC;QACX,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACxB,IAAI,EAAE,CAAC,SAAS,KAAK,IAAI,IAAK,EAAiB,CAAC,UAAU,EAAE,CAAC;oBACzD,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;wBAChB,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;4BACZ,IAAI,EAAE,CAAC;wBACX,CAAC;oBACL,CAAC,CAAC,CAAC;oBACF,EAAiB,CAAC,QAAQ,EAAE,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACJ,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;wBACZ,IAAI,EAAE,CAAC;oBACX,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,UAAkB,EAAE,QAAuD;QAC5F,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,EAAE;YACpD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;gBAC7B,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACX,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,IAAY;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC;IAC9D,CAAC;CACJ;AAvKD,8BAuKC"}