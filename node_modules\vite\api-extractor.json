{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "projectFolder": "./src/node", "mainEntryPointFilePath": "./temp/node/index.d.ts", "dtsRollup": {"enabled": true, "untrimmedFilePath": "", "publicTrimmedFilePath": "./dist/node/index.d.ts"}, "apiReport": {"enabled": false}, "docModel": {"enabled": false}, "tsdocMetadata": {"enabled": false}, "messages": {"compilerMessageReporting": {"default": {"logLevel": "warning"}}, "extractorMessageReporting": {"default": {"logLevel": "warning", "addToApiReportFile": true}, "ae-missing-release-tag": {"logLevel": "none"}}, "tsdocMessageReporting": {"default": {"logLevel": "warning"}, "tsdoc-undefined-tag": {"logLevel": "none"}}}}