<template>
  <div class="action-bar">
    <div class="action-left">
      <slot name="left"></slot>
    </div>
    <div class="action-right">
      <slot name="right">
        <RefreshButton v-if="showRefresh" @refresh="onRefresh" />
      </slot>
    </div>
  </div>
</template>

<script setup>
import RefreshButton from './RefreshButton.vue'

// 定义组件名称
defineOptions({
  name: 'ActionBar'
})

const props = defineProps({
  showRefresh: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['refresh'])

const onRefresh = () => {
  emit('refresh')
}
</script>

<style scoped>
/* 组件级样式可以在这里添加 */
</style> 