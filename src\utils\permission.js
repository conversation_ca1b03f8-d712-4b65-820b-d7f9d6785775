import { getUserPermissionCodes } from '@/api/permission'

let userPermissions = []

/**
 * 初始化用户权限
 */
export async function initUserPermissions() {
  try {
    const res = await getUserPermissionCodes()
    if (res.code === 200) {
      userPermissions = res.data.filter(code => code && code.trim() !== '')
    }
  } catch (error) {
    console.error('获取用户权限失败:', error)
  }
}

/**
 * 检查用户是否有指定权限
 */
export function hasPermission(permissionCode) {
  if (!permissionCode) return true
  return userPermissions.includes(permissionCode)
}

/**
 * 检查用户是否有任意一个权限
 */
export function hasAnyPermission(permissionCodes) {
  if (!permissionCodes || permissionCodes.length === 0) return true
  return permissionCodes.some(code => hasPermission(code))
}

/**
 * 检查用户是否有所有权限
 */
export function hasAllPermissions(permissionCodes) {
  if (!permissionCodes || permissionCodes.length === 0) return true
  return permissionCodes.every(code => hasPermission(code))
}

/**
 * 获取用户所有权限
 */
export function getUserPermissions() {
  return userPermissions
}