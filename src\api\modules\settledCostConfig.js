import instance from '../../utils/axios.js'

const settledCostConfigApi = {
  // 获取入驻费用配置列表
  getSettledCostConfigList: () => instance.get('/settledCostConfig/list'),
  
  // 删除入驻费用配置
  deleteSettledCostConfig: (id) => instance.post('/settledCostConfig/delete', null, { params: { id } }),
  
  // 新增入驻费用配置
  addSettledCostConfig: (data) => {
    console.log('API调用 - 新增入驻配置:', JSON.stringify(data, null, 2))
    
    // 尝试使用不同的请求头
    return instance.post('/settledCostConfig/add', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      },
      validateStatus: function (status) {
        // 允许任何状态码，以便我们可以看到完整的响应
        return true
      }
    })
    .then(res => {
      console.log('API调用 - 新增响应状态:', res.status)
      console.log('API调用 - 新增响应数据:', res.data)
      
      // 如果状态码不是2xx，抛出错误
      if (res.status < 200 || res.status >= 300) {
        const error = new Error(`请求失败，状态码: ${res.status}`)
        error.response = res
        throw error
      }
      
      return res
    })
    .catch(err => {
      console.error('API调用 - 新增失败:')
      console.error('- 错误消息:', err.message)
      if (err.response) {
        console.error('- 响应状态:', err.response.status)
        console.error('- 响应数据:', err.response.data)
        console.error('- 响应头:', err.response.headers)
      }
      throw err
    })
  },
  
  // 更新入驻费用配置
  updateSettledCostConfig: (data) => {
    console.log('API调用 - 更新入驻配置:', JSON.stringify(data, null, 2))
    return instance.put('/settledCostConfig/update', data, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      },
      validateStatus: function (status) {
        return true
      }
    })
    .then(res => {
      console.log('API调用 - 更新响应状态:', res.status)
      console.log('API调用 - 更新响应数据:', res.data)
      
      if (res.status < 200 || res.status >= 300) {
        const error = new Error(`请求失败，状态码: ${res.status}`)
        error.response = res
        throw error
      }
      
      return res
    })
    .catch(err => {
      console.error('API调用 - 更新失败:')
      console.error('- 错误消息:', err.message)
      if (err.response) {
        console.error('- 响应状态:', err.response.status)
        console.error('- 响应数据:', err.response.data)
        console.error('- 响应头:', err.response.headers)
      }
      throw err
    })
  },
  
  // 搜索入驻费用配置（分页+条件）
  searchSettledCostConfig: (params) => instance.get('/settledCostConfig/search', { params }),
}

export { settledCostConfigApi }