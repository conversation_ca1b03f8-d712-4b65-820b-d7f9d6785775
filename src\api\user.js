import axios from '@/utils/axios'

/**
 * 获取管理员角色
 */
export function getAdminRoles(adminId) {
  return axios({
    url: `/admin/user/roles/${adminId}`,
    method: 'get'
  })
}

/**
 * 更新管理员角色
 */
export function updateAdminRoles(adminId, roleIds) {
  return axios({
    url: `/admin/user/roles/${adminId}`,
    method: 'put',
    data: roleIds
  })
}

/**
 * 获取用户列表
 */
export function getUserList(params) {
  return axios({
    url: '/admin/user/list',
    method: 'get',
    params
  })
} 