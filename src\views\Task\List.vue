<template>
  <div class="task-list-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>任务列表</span>
          <el-button type="primary" size="small" @click="refreshTasks">刷新</el-button>
        </div>
      </template>
      <div v-if="loading" class="loading-content">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="taskList.length === 0" class="empty-content">
        暂无运行中的任务
      </div>
      <el-table v-else :data="taskList" border style="width: 100%" height="500" max-height="500"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', textAlign: 'center' }">
        <el-table-column prop="id" label="任务ID" width="180" align="center" />
        <el-table-column prop="fileName" label="文件名称" align="center" />
        <el-table-column prop="shopNames" label="店铺名称" align="center" />
        <el-table-column prop="dataNum" label="数据总数" width="100" align="center" />
        <el-table-column prop="taskStatus" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getTaskStatusType(scope.row.taskStatus)">
              {{ getTaskStatusText(scope.row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="查看日志" placement="top" :hide-after="1500">
                <el-button type="primary" size="small" @click="handleViewLogs(scope.row.id)" circle>
                  <el-icon><Document /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="停止任务" placement="top" :hide-after="1500">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="handleStopTask(scope.row.id)"
                  :loading="stoppingTasks.includes(scope.row.id)" 
                  :disabled="scope.row.taskStatus !== '1'"
                  circle
                >
                  <el-icon><VideoPause /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页控件独立于卡片外部 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <el-dialog v-model="detailVisible" title="任务详情" width="500px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="任务ID">{{ currentTask.id }}</el-descriptions-item>
        <el-descriptions-item label="文件名称">{{ currentTask.fileName }}</el-descriptions-item>
        <el-descriptions-item label="店铺名称">{{ currentTask.shopNames }}</el-descriptions-item>
        <el-descriptions-item label="数据总数">{{ currentTask.dataNum }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentTask.createTime }}</el-descriptions-item>
        <el-descriptions-item label="执行进度">
          <el-progress :percentage="calculateProgress(currentTask)"
            :status="currentTask.taskStatus === '1' ? 'success' : 'exception'" />
        </el-descriptions-item>
        <el-descriptions-item label="执行消息">
          <div class="task-msg">{{ currentTask.msg }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 任务日志弹窗 -->
    <el-dialog v-model="logsVisible" title="任务日志" width="900px" :close-on-click-modal="false">
      <div class="logs-container">
        <!-- 日志消息区域 -->
        <div class="logs-message-section">
          <div class="logs-message-content">
            <div v-if="logsLoading" class="loading-text">加载中...</div>
            <div v-else class="logs-text">
              <div v-for="(line, index) in formatLogMessage(logsMessage)" :key="index" class="log-line">
                {{ line }}
              </div>
            </div>
          </div>
          <div class="refresh-btn-container">
            <el-button type="primary" size="small" @click="refreshLogs" :loading="logsLoading" icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>

        <!-- 店铺日志列表 -->
        <div class="logs-table-section">
          <el-table :data="logsList" border style="width: 100%"
            :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', textAlign: 'center' }">
            <el-table-column prop="shopName" label="店铺名称" align="center" />
            <el-table-column prop="progress" label="进度" width="100" align="center">
              <template #default="scope">
                {{ scope.row.progress }}%
              </template>
            </el-table-column>
            <el-table-column label="创建时间/修改时间" width="300" align="center">
              <template #default="scope">
                {{ scope.row.createTime }} / {{ scope.row.updateTime }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <div class="operation-buttons">
                  <el-tooltip content="查看详情" placement="top" :hide-after="1500">
                    <el-button type="primary" size="small" @click="handleViewDetail(scope.row)" circle>
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="下载日志" placement="top" :hide-after="1500">
                    <el-button type="success" size="small" @click="handleDownload(scope.row)" circle>
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="logsVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { taskApi } from '@/api/modules/task'
import { Document, VideoPause, View, Download, Refresh } from '@element-plus/icons-vue'

// 任务列表数据
const taskList = ref([])
const loading = ref(false)
const stoppingTasks = ref([])
const detailVisible = ref(false)
const currentTask = ref({})
const total = ref(0)

// 分页信息
const currentPage = ref(1) // 界面显示的当前页，从1开始
const pageSize = ref(10)   // 每页条数

// 日志相关数据
const logsVisible = ref(false)
const logsLoading = ref(false)
const logsList = ref([])
const logsMessage = ref('')
const currentTaskId = ref('')

// 详细日志相关数据
const detailLogsVisible = ref(false)
const detailLogsLoading = ref(false)
const detailLogsList = ref([])
const currentDetailShopLog = ref(null)

// 获取任务列表
const fetchTaskList = async () => {
  loading.value = true
  try {
    // 将currentPage转换为后端需要的pageSize(0表示第一页)
    const pageIndex = currentPage.value - 1

    const res = await taskApi.getRunningTasks(pageIndex, pageSize.value)
    console.log('API响应数据:', res)

    if (res.code === 200) {
      // 适应后端返回的数据结构
      if (res.data) {
        // 处理嵌套的data.data结构
        if (res.data.data && Array.isArray(res.data.data)) {
          taskList.value = res.data.data || []
          total.value = res.data.total || 0
        }
        // 处理嵌套的data.list结构
        else if (res.data.list && Array.isArray(res.data.list)) {
          taskList.value = res.data.list || []
          total.value = res.data.total || 0
        }
        // 处理records结构
        else if (res.data.records && Array.isArray(res.data.records)) {
          taskList.value = res.data.records || []
          total.value = res.data.total || 0
        }
        // 如果返回的直接就是数组
        else if (Array.isArray(res.data)) {
          taskList.value = res.data
          total.value = res.data.length
        }
        // 其他情况
        else {
          taskList.value = []
          total.value = 0
          console.warn('未能识别的数据格式:', res.data)
        }
      } else {
        taskList.value = []
        total.value = 0
      }
    } else {
      ElMessage.error(res.data?.message || '获取任务列表失败')
    }
  } catch (error) {
    console.error('获取任务列表出错:', error)
    ElMessage.error('获取任务列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTaskList()
}

// 处理每页显示条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1 // 切换每页条数时重置为第一页
  fetchTaskList()
}

// 刷新任务列表
const refreshTasks = () => {
  fetchTaskList()
}

// 停止任务
const handleStopTask = async (taskId) => {
  console.log("taskId", taskId)
  try {
    await ElMessageBox.confirm(
      `确定要停止任务吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    stoppingTasks.value.push(taskId)
    const res = await taskApi.stopTask(taskId)

    if (res.code === 200) {
      ElMessage.success('任务已成功停止')
      // 停止成功后刷新列表
      fetchTaskList()
    } else {
      ElMessage.error(res.data.message || '停止任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止任务出错:', error)
      ElMessage.error('停止任务失败: ' + (error.message || '未知错误'))
    }
  } finally {
    stoppingTasks.value = stoppingTasks.value.filter(id => id !== taskId)
  }
}

// 任务状态显示处理
const getTaskStatusText = (status) => {
  const statusMap = {
    '0': '未开始',
    '1': '执行中',
    '2': '已完成',
    '3': '已中止',
    '4': '执行失败'
  }
  return statusMap[status] || '未知状态'
}

const getTaskStatusType = (status) => {
  const typeMap = {
    '0': 'info',
    '1': 'success',
    '2': 'success',
    '3': 'warning',
    '4': 'danger'
  }
  return typeMap[status] || 'info'
}

// 查看任务详情
const showTaskDetail = (task) => {
  currentTask.value = task
  detailVisible.value = true
}

// 计算进度
const calculateProgress = (task) => {
  if (!task || !task.msg) return 0

  try {
    // 尝试从消息中提取进度信息
    const totalMatch = task.msg.match(/总执行数据：(\d+)条/)
    const executedMatch = task.msg.match(/已执行条数：(\d+)条/)

    if (totalMatch && executedMatch) {
      const total = parseInt(totalMatch[1])
      const executed = parseInt(executedMatch[1])

      if (total > 0) {
        return Math.floor((executed / total) * 100)
      }
    }

    return 0
  } catch (e) {
    return 0
  }
}

// 查看日志
const handleViewLogs = async (taskId) => {
  currentTaskId.value = taskId
  logsVisible.value = true
  await fetchLogs()
}

// 获取日志数据
const fetchLogs = async () => {
  logsLoading.value = true
  try {
    // 并行调用两个接口
    const [logsListRes, logsMsgRes] = await Promise.all([
      taskApi.getLogsList(currentTaskId.value),
      taskApi.getLogsMsg(currentTaskId.value)
    ])

    console.log('日志列表响应:', logsListRes)
    console.log('日志消息响应:', logsMsgRes)

    // 处理日志列表数据
    if (logsListRes.code === 200) {
      if (Array.isArray(logsListRes.data)) {
        logsList.value = logsListRes.data
      } else if (logsListRes.data && Array.isArray(logsListRes.data.data)) {
        logsList.value = logsListRes.data.data
      } else {
        logsList.value = []
      }
    } else {
      logsList.value = []
      ElMessage.error('获取日志列表失败: ' + (logsListRes.message || '未知错误'))
    }

    // 处理日志消息数据
    if (logsMsgRes.code === 200) {
      // 如果返回的是字符串，直接使用
      if (typeof logsMsgRes.data === 'string') {
        logsMessage.value = logsMsgRes.data
      }
      // 如果返回的是对象，尝试获取消息字段
      else if (logsMsgRes.data && typeof logsMsgRes.data === 'object') {
        logsMessage.value = logsMsgRes.data.message || logsMsgRes.data.msg || JSON.stringify(logsMsgRes.data)
      } else {
        logsMessage.value = '暂无日志消息'
      }
    } else {
      logsMessage.value = '获取日志消息失败'
      ElMessage.error('获取日志消息失败: ' + (logsMsgRes.message || '未知错误'))
    }

  } catch (error) {
    console.error('获取日志数据出错:', error)
    ElMessage.error('获取日志数据失败: ' + (error.message || '未知错误'))
    logsList.value = []
    logsMessage.value = '获取日志数据失败'
  } finally {
    logsLoading.value = false
  }
}

// 刷新日志
const refreshLogs = () => {
  fetchLogs()
}

// 查看详细日志
const handleViewDetail = async (row) => {
  try {
    // 保存当前选中的店铺日志
    currentDetailShopLog.value = row
    
    // 显示加载状态
    detailLogsLoading.value = true
    detailLogsVisible.value = true
    
    // 调用API获取详细日志数据
    const res = await taskApi.getLogsDetailList(row.taskId, row.shopId)
    console.log('详细日志响应:', res)

    if (res.code === 200) {
      // 处理返回的数据
      if (res.data && typeof res.data === 'object') {
        // 如果返回的是嵌套对象，尝试获取数据数组
        if (Array.isArray(res.data.data)) {
          detailLogsList.value = res.data.data
        } else if (Array.isArray(res.data.list)) {
          detailLogsList.value = res.data.list
        } else if (Array.isArray(res.data.records)) {
          detailLogsList.value = res.data.records
        } else if (Array.isArray(res.data)) {
          detailLogsList.value = res.data
        } else {
          detailLogsList.value = []
          console.warn('未能识别的详细日志数据格式:', res.data)
        }
      } else {
        detailLogsList.value = []
      }
    } else {
      detailLogsList.value = []
      ElMessage.error('获取详细日志失败: ' + (res.message || '未知错误'))
    }
  } catch (error) {
    console.error('获取详细日志出错:', error)
    ElMessage.error('获取详细日志失败: ' + (error.message || '未知错误'))
    detailLogsList.value = []
  } finally {
    detailLogsLoading.value = false
  }
}

// 下载日志
const handleDownload = async (row) => {
  try {
    // 构建文件名，根据后端逻辑应该是 taskId + shopId + ".txt"
    const fileName = `${row.taskId}${row.shopId}.txt`

    const response = await taskApi.downloadLogs(fileName)

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载日志文件出错:', error)
    ElMessage.error('下载日志文件失败: ' + (error.message || '未知错误'))
  }
}

// 格式化日志消息
const formatLogMessage = (message) => {
  if (!message) return ['暂无日志消息']

  // 将日志消息按行分割，处理各种可能的换行符
  const lines = message.split(/\r?\n|\r/).filter(line => line.trim() !== '')

  if (lines.length === 0) {
    return ['暂无日志消息']
  }

  return lines
}

// 处理API错误
const handleApiError = (error, operation, defaultMessage = '操作失败') => {
  console.error(`${operation}出错:`, error)
  
  // 确定错误消息
  let errorMessage = defaultMessage
  
  if (error.response) {
    // 服务器返回了错误状态码
    const status = error.response.status
    if (status === 404) {
      errorMessage = `${operation}: 资源不存在 (404)`
    } else if (status === 403) {
      errorMessage = `${operation}: 权限不足 (403)`
    } else if (status === 401) {
      errorMessage = `${operation}: 未授权，请重新登录 (401)`
    } else if (status >= 500) {
      errorMessage = `${operation}: 服务器错误 (${status})`
    } else {
      errorMessage = `${operation}: ${error.response.data?.message || error.message || '未知错误'}`
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    errorMessage = `${operation}: 网络错误，请检查网络连接`
  } else {
    // 请求设置时出错
    errorMessage = `${operation}: ${error.message || '未知错误'}`
  }
  
  // 显示错误消息
  ElMessage.error(errorMessage)
  
  return errorMessage
}

// 组件挂载时获取任务列表
onMounted(() => {
  fetchTaskList()
})
</script>

<style scoped>
.task-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-content,
.loading-content {
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.task-msg {
  white-space: pre-line;
  max-height: 150px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 15px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.operation-buttons .el-button {
  margin: 0;
  transition: transform 0.2s;
}

.operation-buttons .el-button:hover {
  transform: scale(1.1);
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
  scrollbar-width: thin;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  border-radius: 3px;
  background: #c0c4cc;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  border-radius: 3px;
  background: #f5f7fa;
}

:deep(.el-table--scrollable-y .el-table__body-wrapper) {
  overflow-y: auto;
}

:deep(.el-table th) {
  height: 40px;
  padding: 8px 0;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 8px;
  height: 50px;
}

/* 日志弹窗样式 */
.logs-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.logs-message-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.logs-message-content {
  margin-bottom: 10px;
}

.logs-text {
  color: #606266;
  line-height: 1.6;
  white-space: pre-line;
  font-size: 14px;
  min-height: 120px;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.log-line {
  margin-bottom: 4px;
  color: #606266;
  font-size: 14px;
}

.loading-text {
  color: #909399;
  text-align: center;
  padding: 40px;
  font-size: 14px;
}

.refresh-btn-container {
  display: flex;
  justify-content: flex-end;
}

.logs-table-section {
  margin-top: 10px;
}

/* 日志弹窗表格样式 */
.logs-table-section :deep(.el-table) {
  border-radius: 8px;
}

.logs-table-section :deep(.el-table th) {
  background-color: #f5f7fa !important;
  color: #606266;
  font-weight: 500;
}

.logs-table-section :deep(.el-button) {
  margin-right: 4px;
}

.logs-table-section :deep(.el-button:last-child) {
  margin-right: 0;
}

/* 弹窗底部样式 */
.dialog-footer {
  text-align: right;
}

/* 日志弹窗标题样式 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}
</style>