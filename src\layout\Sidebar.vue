<template>
	<el-menu router :default-active="$route.path" :collapse="false" unique-opened background-color="#304156" text-color="#bfcbd9" active-text-color="#409EFF">
		<!-- 一级菜单 -->
		<el-sub-menu v-for="item in menuData" :key="item.path" :index="item.path">
			<template #title>
				<el-icon>
					<component :is="item.icon" />
				</el-icon>
				<span>{{ item.title }}</span>
			</template>

			<!-- 二级菜单 -->
			<el-menu-item v-for="child in item.children" :key="child.path" :index="child.path">
				{{ child.title }}
			</el-menu-item>
		</el-sub-menu>
	</el-menu>
</template>

<script setup lang="ts">
	import { shallowRef } from 'vue'
	import {Document as DocIcon,Setting,User,Message,ShoppingCart,Shop,Connection,Notebook,Box,TrendCharts,HomeFilled,Monitor } from '@element-plus/icons-vue'

	const menuData = shallowRef([
		{
			title: '系统管理',
			path: '/system',
			icon: Setting,
			children: [
				{
					title: '配置列表',
					path: '/settledConfig/list'
				},
				{
					title: '会员开通记录',
					path: '/settledConfig/memberRecord'
				},
				{
					title: '用户列表',
					path: '/user/list'
				},
				{
					title: '角色管理',
					path: '/user/role'
				},
				{
					title: '权限管理',
					path: '/user/permission'
				},
				{
					title: '邀请列表',
					path: '/invitation/list'
				},
				{
					title: '操作日志',
					path: '/log/operate'
				},
				{
					title: '登录日志',
					path: '/log/login'
				}
			]
		},
		{
			title: '店铺管理',
			path: '/shop',
			icon: Shop,
			children: [
				{
					title: '店铺列表',
					path: '/shop/list'
				}
			]
		},
		{
			title: '书品管理',
			path: '/book',
			icon: Notebook,
			children: [
				{
					title: '选品中心',
					path: '/book/selection/center'
				}
			]
		},
		{
			title: '仓储管理',
			path: '/warehouse',
			icon: Box,
			children: [
				{
					title: '货区列表',
					path: '/warehouse/depot/list'
				}
			]
		},
		{
			title: '工具管理',
			path: '/tools',
			icon: DocIcon,
			children: [
				{
					title: '卡密列表',
					path: '/tools/cards/list'
				},
				{
					title: '活跃卡密列表',
					path: '/tools/cards/activeCardsList'
				}
			]
		},
		{
			title: '审核管理',
			path: '/examine',
			children: [
				{
					title: '违规列表',
					path: '/examine/violation/list'
				}
			]
		},
		{
			title: '日志管理',
			path: '/log',
			children: [
				{
					title: '日志列表',
					path: '/log/runningLog/list'
				}
			]
		},
		{
			title: '任务管理',
			path: '/task',
			icon: TrendCharts,
			children: [
				{
					title: '任务列表',
					path: '/task/list'
				}
			]
		},
		{
			title: '功能模块',
			path: '/useModule',
			children: [
				{
					title: '服务列表',
					path: '/useModule/vas/list'
				}
			]
		},
		{
			title: '监控中心',
			path: '/monitor',
			icon: Monitor,
			children: [
				{
					title: '监控大屏',
					path: '/monitor/dashboard'
				}
			]
		}
	])
</script>

<style scoped>
	.el-menu {
		height: 100%;
		border-right: none;
	}

	.el-menu-item.is-active {
		background-color: #263445 !important;
	}

	:deep(.el-menu) {
		border-right: none;
	}

	:deep(.el-sub-menu.is-opened) {
		> .el-sub-menu__title,
		.el-menu-item {
			background-color: #1a1a1a !important;
		}
	}

	:deep(.el-menu-item):hover,
	:deep(.el-sub-menu__title):hover {
		background-color: #1a1a1a !important;
	}

	:deep(.el-menu-item.is-active) {
		background-color: #1a1a1a !important;
		color: var(--el-menu-active-color);
	}

	:deep(.el-menu-item.is-active + .el-sub-menu .el-sub-menu__title),
	:deep(.el-menu-item.is-active) ~ .el-sub-menu .el-sub-menu__title {
		background-color: #1a1a1a !important;
	}

	:deep(.el-sub-menu) {
		&.is-active {
			> .el-sub-menu__title {
				background-color: #1a1a1a !important;
			}
		}
	}
</style>
