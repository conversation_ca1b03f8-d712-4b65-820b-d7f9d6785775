/**
 * WebSocket连接工具类
 */

// WebSocket URL
const WS_URL = 'ws://146.56.192.164:9090/ws';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000; // 3秒
    this.listeners = new Map();
  }

  /**
   * 初始化WebSocket连接
   */
  connect() {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      console.log('WebSocket已连接或正在连接中');
      return;
    }

    try {
      // 获取存储在localStorage中的token
      const token = localStorage.getItem('accessToken');
      if (!token) {
        console.error('WebSocket连接失败: 未找到accessToken');
        return;
      }

      // 创建WebSocket连接，通过传递header中的token
      this.socket = new WebSocket(WS_URL+'?token='+token);
      
      // 由于浏览器WebSocket API不允许直接设置headers，我们需要在连接建立后的第一条消息中发送token
      this.socket.onopen = () => {
        console.log('WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 连接后立即发送认证信息
        this.sendMessage({
          type: 'auth',
          token: token
        });
        
        // 触发已注册的连接事件监听器
        this.triggerEvent('connect');
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('收到WebSocket消息:', data);
          this.triggerEvent('message', data);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
          this.triggerEvent('error', error);
        }
      };

      this.socket.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event);
        this.isConnected = false;
        this.triggerEvent('disconnect');

        // 尝试重连
        this.attemptReconnect();
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        this.triggerEvent('error', error);
      };
    } catch (error) {
      console.error('初始化WebSocket失败:', error);
      this.triggerEvent('error', error);
    }
  }

  /**
   * 尝试重新连接
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('达到最大重连次数');
      return;
    }

    this.reconnectAttempts++;
    console.log(`尝试第 ${this.reconnectAttempts} 次重连...`);

    setTimeout(() => {
      this.connect();
    }, this.reconnectInterval);
  }

  /**
   * 发送消息到WebSocket服务器
   * @param {Object} data - 要发送的数据
   */
  sendMessage(data) {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      this.socket.send(message);
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      return false;
    }
  }

  /**
   * 关闭WebSocket连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.isConnected = false;
    }
  }

  /**
   * 注册事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * 注销事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event);
    const index = callbacks.indexOf(callback);
    if (index !== -1) {
      callbacks.splice(index, 1);
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  triggerEvent(event, data) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event);
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`执行${event}事件监听器出错:`, error);
      }
    });
  }
}

// 创建单例实例
const webSocketService = new WebSocketService();

export default webSocketService;