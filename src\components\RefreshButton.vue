<template>
  <el-button 
    class="btn-circle btn-refresh" 
    :size="size" 
    @click="handleRefresh"
    :loading="loading"
  >
    <el-icon><Refresh /></el-icon>
  </el-button>
</template>

<script setup>
import { ref } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'RefreshButton'
})

const props = defineProps({
  size: {
    type: String,
    default: 'default'
  },
  position: {
    type: String,
    default: 'right' // 默认在右侧显示
  }
})

const emit = defineEmits(['refresh'])

const loading = ref(false)

const handleRefresh = () => {
  loading.value = true
  emit('refresh')
  
  // 添加一个短暂的加载效果，提升用户体验
  setTimeout(() => {
    loading.value = false
  }, 500)
}
</script>

<style scoped>
/* 组件级样式可以在这里添加 */
</style> 