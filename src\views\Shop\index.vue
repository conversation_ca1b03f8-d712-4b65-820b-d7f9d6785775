<template>
  <div class="list-container">
    <div class="search-area">
      <el-form :model="queryParams" inline>
        <el-form-item label="店铺类型">
          <el-select v-model="queryParams.shopType" placeholder="请选择店铺类型" clearable>
            <el-option label="拼多多" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组">
          <el-input v-model="queryParams.shopGroup" placeholder="请输入分组" />
        </el-form-item>
        <el-form-item label="店铺名称">
          <el-input v-model="queryParams.shopName" placeholder="请输入店铺名称" />
        </el-form-item>
        <el-form-item label="三方名称">
          <el-input v-model="queryParams.shopAliasName" placeholder="请输入三方名称" />
        </el-form-item>
        <el-form-item label="是否授权">
          <el-select v-model="queryParams.shopAuthorize" placeholder="请选择是否授权" clearable>
            <el-option label="已授权" value="1" />
            <el-option label="未授权" value="0" />
            <el-option label="已过期" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺状态">
          <el-select v-model="queryParams.status" placeholder="请选择店铺状态" clearable>
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-bar">
      <div class="action-left">
        <el-button type="primary"  @click="handleAdd">新增</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </div>
      <div class="action-right">
        <RefreshButton @refresh="getShopListData" />
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="shopList"
      @selection-change="handleSelectionChange"
      border
      stripe
      style="width: 100%"
      row-key="id"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="店铺编码" prop="id" align="center" />
      <el-table-column label="店铺类型" align="center">
        <template #default="scope">
          {{ scope.row.shopType === '1' ? '拼多多' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="分组" prop="shopGroup" align="center" />
      <el-table-column label="店铺名称" prop="shopName" align="center" />
      <el-table-column label="三方名称" prop="shopAliasName" align="center" />
      <el-table-column label="是否授权" align="center">
        <template #default="scope">
          <el-tag :type="getAuthorizeTagType(scope.row.shopAuthorize)" v-if="scope.row.shopAuthorize === '1'">
            {{ getAuthorizeText(scope.row.shopAuthorize) }}
          </el-tag>
          <el-button 
            v-else
            size="small" 
            type="primary" 
            @click="handleAuthorize(scope.row)"
          >
            授权
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="到期时间" align="center">
        <template #default="scope">
          {{ scope.row.expirationTime }}
        </template>
      </el-table-column>
      <el-table-column label="添加时间" prop="addTime" align="center" />
      <el-table-column label="店铺状态" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="同步订单" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isSynOrder"
            :active-value="1"
            :inactive-value="0"
            class="ml-2"
            inline-prompt
            active-text="开"
            inactive-text="关"
            @change="handleSyncOrderChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="设置" align="center" width="60">
        <template #default="scope">
          <el-button type="primary" icon="Setting" circle plain size="small" @click="handleSetting(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          <el-button
            v-if="scope.row.status === '0'"
            size="small"
            type="warning"
            @click="handleChangeStatus(scope.row, '1')"
          >停用</el-button>
          <el-button
            v-if="scope.row.status === '1'"
            size="small"
            type="success"
            @click="handleChangeStatus(scope.row, '0')"
          >启用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="店铺类型" prop="shopType">
          <el-select v-model="form.shopType" placeholder="请选择店铺类型" style="width: 100%">
            <el-option label="拼多多" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组" prop="shopGroup">
          <el-input v-model="form.shopGroup" placeholder="请输入分组" />
        </el-form-item>
        <el-form-item label="店铺名称" prop="shopName">
          <el-input v-model="form.shopName" placeholder="请输入店铺名称" />
        </el-form-item>
        <el-form-item label="三方名称" prop="shopAliasName">
          <el-input v-model="form.shopAliasName" placeholder="请输入三方名称" />
        </el-form-item>
        <el-form-item label="店铺ID" prop="mallId">
          <el-input v-model="form.mallId" placeholder="请输入三方店铺ID" />
        </el-form-item>
        <el-form-item label="万里牛ID" prop="shopNike">
          <el-input v-model="form.shopNike" placeholder="请输入万里牛系统ID" />
        </el-form-item>
        <el-form-item label="第三方账号" prop="account">
          <el-input v-model="form.account" placeholder="请输入第三方平台账号" />
        </el-form-item>
        <el-form-item label="第三方密码" prop="password">
          <el-input v-model="form.password" type="password" placeholder="请输入第三方平台密码" show-password />
        </el-form-item>
        <el-form-item label="同步订单" prop="isSynOrder">
          <el-switch
            v-model="form.isSynOrder"
            :active-value="1"
            :inactive-value="0"
            inline-prompt
            active-text="开"
            inactive-text="关"
          />
        </el-form-item>
        <el-form-item label="店铺状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// import { shopApi } from '@/api'
import {
  getShopList, 
  getShopDetail, 
  addShop, 
  updateShop, 
  deleteShop, 
  batchDeleteShop, 
  updateSyncOrderStatus
} from '@/api/modules/shop'
import { fetchExternalApi } from '@/utils/request'
import RefreshButton from '@/components/RefreshButton.vue'

// 查询参数
const queryParams = reactive({
  shopType: '',
  shopGroup: '',
  shopName: '',
  shopAliasName: '',
  shopAuthorize: '',
  status: '',
  pageNum: 1,
  pageSize: 20
})

// 表格数据
const loading = ref(false)
const shopList = ref([])
const total = ref(0)
const selectedRows = ref<any[]>([])

// 对话框相关
const dialog = reactive({
  visible: false,
  title: ''
})

// 表单对象
const formRef = ref()
const form = reactive({
  id: undefined,
  mallId: '',
  shopNike: '',
  shopType: '1',
  shopGroup: '',
  shopName: '',
  shopAliasName: '',
  shopAuthorize: '0',
  status: '0',
  account: '',
  password: '',
  isSynOrder: 0
})

// 表单验证规则
const rules = {
  shopType: [{ required: true, message: '请选择店铺类型', trigger: 'change' }],
  shopName: [{ required: true, message: '请输入店铺名称', trigger: 'blur' }],
  shopAliasName: [{ required: true, message: '请输入三方名称', trigger: 'blur' }]
}

// 初始化方法
onMounted(() => {
  getShopListData()
})

// 获取授权状态标签类型
const getAuthorizeTagType = (authorize: string) => {
  switch (authorize) {
    case '1': return 'success'
    case '0': return 'info'
    case '2': return 'warning'
    default: return 'info'
  }
}

// 获取授权状态文本
const getAuthorizeText = (authorize: string) => {
  switch (authorize) {
    case '1': return '已授权'
    case '0': return '未授权'
    case '2': return '已过期'
    default: return '未知'
  }
}

// 获取店铺列表
const getShopListData = async () => {
  loading.value = true
  try {
    const res = await getShopList(queryParams)
    console.log("res", res)
    if (res.code === 200) {
      shopList.value = res.data.list
      total.value = res.data.total
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    ElMessage.error('获取店铺列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleSearch = () => {
  queryParams.pageNum = 1
  getShopListData()
}

// 重置按钮操作
const handleReset = () => {
  queryParams.shopType = ''
  queryParams.shopGroup = ''
  queryParams.shopName = ''
  queryParams.shopAliasName = ''
  queryParams.shopAuthorize = ''
  queryParams.status = ''
  queryParams.pageNum = 1
  getShopListData()
}

// 分页相关操作
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  getShopListData()
}

const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val
  getShopListData()
}

// 选择行事件
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 打开添加对话框
const handleAdd = () => {
  dialog.title = '添加店铺'
  dialog.visible = true
  form.id = undefined
  form.mallId = ''
  form.shopNike = ''
  form.shopType = '1'
  form.shopGroup = ''
  form.shopName = ''
  form.shopAliasName = ''
  form.shopAuthorize = '0'
  form.status = '0'
  form.account = ''
  form.password = ''
  form.isSynOrder = 0
}

// 修改按钮操作
const handleEdit = (row) => {
  dialog.title = '编辑店铺'
  dialog.visible = true
  
  if (row) {
    // 如果传递了行数据，则编辑该行
    const shopId = row.id
    if (shopId) {
      getShopDetail(shopId).then(res => {
        if (res.data.code === 200) {
          Object.assign(form, res.data.data)
        }
      })
    }
  } else {
    // 否则检查是否有选中行
    if (selectedRows.value.length !== 1) {
      ElMessage.warning('请选择一条记录进行修改')
      dialog.visible = false
      return
    }
    
    const shopId = selectedRows.value[0].id
    if (shopId) {
      getShopDetail(shopId).then(res => {
        if (res.data.code === 200) {
          Object.assign(form, res.data.data)
        }
      })
    }
  }
}

// 删除按钮操作
const handleDelete = (row) => {
  if (row) {
    // 删除单条记录
    ElMessageBox.confirm('确认删除该店铺记录吗？', '警告', {
      type: 'warning'
    }).then(() => {
      if (row.id) {
        deleteShop(row.id).then(res => {
          if (res.data.code === 200) {
            ElMessage.success('删除成功')
            getShopListData()
          }
        })
      }
    }).catch(() => {})
  } else {
    // 批量删除
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请至少选择一条记录')
      return
    }
    
    ElMessageBox.confirm(`确认删除选中的${selectedRows.value.length}条店铺记录吗？`, '警告', {
      type: 'warning'
    }).then(() => {
      const ids = selectedRows.value.map(item => item.id).filter(id => id !== undefined)
      if (ids.length > 0) {
        batchDeleteShop(ids.join(',')).then(res => {
          if (res.data.code === 200) {
            ElMessage.success('批量删除成功')
            getShopListData()
          }
        })
      }
    }).catch(() => {})
  }
}

// 设置按钮操作
const handleSetting = (row) => {
  ElMessage.info(`设置店铺: ${row.shopName}`)
  // 实现店铺设置功能
}

// 同步订单状态变更
const handleSyncOrderChange = (row) => {
  const status = row.isSynOrder === 1 ? '开启' : '关闭'
  if (row.id) {
    updateSyncOrderStatus(row.id, row.isSynOrder).then(res => {
      if (res.data.code === 200) {
        ElMessage.success(`${status}同步订单成功`)
      } else {
        ElMessage.error(`${status}同步订单失败`)
        // 恢复原状态
        row.isSynOrder = row.isSynOrder === 1 ? 0 : 1
      }
    }).catch(() => {
      // 恢复原状态
      row.isSynOrder = row.isSynOrder === 1 ? 0 : 1
      ElMessage.error(`${status}同步订单失败`)
    })
  }
}

// 处理授权操作
const handleAuthorize = async (row) => {
  try {
    // 显示加载提示
    ElMessage.info('正在获取授权链接，请稍候...');
    
    // 调用API获取授权链接
    const apiUrl = `https://api.buzhiyushu.cn/huidiao/pdd/toPddGetCode?id=${row.id}&type=4`;
    const result = await fetchExternalApi(apiUrl);
    
    // 处理不同类型的响应
    let authUrl = null;
    
    if (result) {
      if (result.url) {
        // 如果响应中直接包含url字段
        authUrl = result.url;
      } else if (typeof result === 'string' && result.includes('http')) {
        // 如果响应直接是URL字符串
        authUrl = result;
      }
    }
    
    if (authUrl) {
      console.log('授权URL:', authUrl);
      
      // 打开新窗口进行授权
      const newTab = window.open(authUrl, '_blank');
      
      // 如果无法打开新窗口，则尝试直接跳转
      if (!newTab || newTab.closed || typeof newTab.closed === 'undefined') {
        window.location.href = authUrl;
      }
    } else {
      console.error('无法解析授权链接:', result);
      ElMessage.error('获取授权链接失败');
    }
  } catch (error) {
    console.error('授权请求失败:', error);
    ElMessage.error('授权请求失败');
  }
}

// 处理状态变更
const handleChangeStatus = async (row, newStatus) => {
  const statusText = newStatus === '0' ? '启用' : '停用';
  
  ElMessageBox.confirm(`确认要${statusText}该店铺吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 更新状态的API调用
      const updatedData = { ...row, status: newStatus };
      await updateShop(updatedData);
      ElMessage.success(`${statusText}成功`);
      getShopListData(); // 刷新数据
    } catch (error) {
      console.error(`${statusText}失败:`, error);
      ElMessage.error(`${statusText}失败`);
    }
  }).catch(() => {});
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (form.id) {
        // 编辑
        updateShop(form).then(res => {
          if (res.data.code === 200) {
            ElMessage.success('修改成功')
            dialog.visible = false
            getShopListData()
          }
        })
      } else {
        // 新增
        addShop(form).then(res => {
          if (res.data.code === 200) {
            ElMessage.success('新增成功')
            dialog.visible = false
            getShopListData()
          }
        })
      }
    }
  })
}
</script>

<style scoped>
.list-container {
  padding: 20px;
}

.search-area {
  margin-bottom: 20px;
  padding: 18px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: left;
}

.search-area :deep(.el-form-item) {
  margin-right: 18px;
  margin-bottom: 10px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.action-left {
  display: flex;
  gap: 10px;
}

.action-right {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 